# TurdParty Local Development Makefile

.PHONY: help start stop restart logs health ci test clean setup

help: ## Show this help message
	@echo "TurdParty Development Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'

setup: ## Setup local CI/CD environment
	@./scripts/ci/setup-local-ci.sh

start: ## Start all services
	@./scripts/ci/dev-workflow.sh start

stop: ## Stop all services
	@./scripts/ci/dev-workflow.sh stop

restart: ## Restart all services
	@./scripts/ci/dev-workflow.sh restart

logs: ## Show service logs
	@./scripts/ci/dev-workflow.sh logs

health: ## Run health check
	@./scripts/ci/dev-workflow.sh health

ci: ## Run local CI pipeline
	@./scripts/ci/dev-workflow.sh ci

test: ## Run test suite
	@./scripts/ci/dev-workflow.sh test

clean: ## Clean up containers and volumes
	@./scripts/ci/dev-workflow.sh clean

# Development shortcuts
dev-start: start health ## Start services and run health check
dev-test: ci test ## Run CI pipeline and tests
dev-deploy: ci ## Run full CI pipeline before deployment
