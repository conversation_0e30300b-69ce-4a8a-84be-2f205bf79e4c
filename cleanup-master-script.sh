#!/bin/bash

# TurdParty Master Cleanup Script
# Orchestrates the complete folder structure cleanup process

set -e

# Make script executable
chmod +x "$0"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="cleanup_master_$TIMESTAMP.log"

echo -e "${BOLD}${BLUE}=======================================${NC}"
echo -e "${BOLD}${BLUE}   TurdParty Master Cleanup Script     ${NC}"
echo -e "${BOLD}${BLUE}=======================================${NC}"

# Start logging
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "Master cleanup started at: $(date)"
echo "Working directory: $(pwd)"
echo "Log file: $LOG_FILE"

# Function to show usage
show_usage() {
    echo "Usage: $0 [phase|all|status]"
    echo ""
    echo "Commands:"
    echo "  phase1    - Run Phase 1: Pre-Cleanup Assessment"
    echo "  phase2    - Run Phase 2: Core Structure Cleanup"
    echo "  phase3    - Run Phase 3: Advanced Organization"
    echo "  phase4    - Run Phase 4: Validation and Documentation"
    echo "  all       - Run all phases sequentially"
    echo "  status    - Show current cleanup status"
    echo "  setup     - Initialize archive system only"
    echo ""
    echo "Examples:"
    echo "  $0 setup     # Initialize archive system"
    echo "  $0 phase1    # Run baseline assessment"
    echo "  $0 all       # Run complete cleanup process"
    exit 1
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run command with error handling
run_with_error_handling() {
    local cmd="$1"
    local description="$2"
    
    echo -e "\n${YELLOW}$description...${NC}"
    echo "Executing: $cmd"
    
    if eval "$cmd"; then
        echo -e "${GREEN}✓ $description completed successfully${NC}"
        return 0
    else
        echo -e "${RED}✗ $description failed${NC}"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "\n${BLUE}=== CHECKING PREREQUISITES ===${NC}"
    
    local missing_deps=0
    
    # Check for required commands
    for cmd in python3 git find grep; do
        if ! command_exists "$cmd"; then
            echo -e "${RED}✗ Missing required command: $cmd${NC}"
            ((missing_deps++))
        else
            echo -e "${GREEN}✓ Found: $cmd${NC}"
        fi
    done
    
    # Check for optional commands
    for cmd in npm npx pytest bc; do
        if ! command_exists "$cmd"; then
            echo -e "${YELLOW}⚠ Optional command not found: $cmd${NC}"
        else
            echo -e "${GREEN}✓ Found: $cmd${NC}"
        fi
    done
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}✗ Not in a git repository${NC}"
        ((missing_deps++))
    else
        echo -e "${GREEN}✓ Git repository detected${NC}"
    fi
    
    # Check for required scripts
    for script in scripts/generate_baseline_coverage.sh scripts/archive-file.sh scripts/setup-archive-system.sh; do
        if [ ! -f "$script" ]; then
            echo -e "${RED}✗ Missing required script: $script${NC}"
            ((missing_deps++))
        else
            echo -e "${GREEN}✓ Found: $script${NC}"
        fi
    done
    
    if [ $missing_deps -gt 0 ]; then
        echo -e "\n${RED}Prerequisites check failed. Please install missing dependencies.${NC}"
        exit 1
    fi
    
    echo -e "\n${GREEN}✓ All prerequisites satisfied${NC}"
}

# Function to show current status
show_status() {
    echo -e "\n${BLUE}=== CLEANUP STATUS ===${NC}"
    
    # Check if archive system exists
    if [ -d "_archive_" ]; then
        echo -e "${GREEN}✓ Archive system initialized${NC}"
        
        # Count archived files
        archived_count=$(find _archive_ -type f \( -name "*.py" -o -name "*.js" -o -name "*.html" -o -name "*.css" -o -name "*.sh" -o -name "*.md" \) | grep -v README.md | grep -v cleanup-log.md | wc -l)
        echo -e "  Archived files: $archived_count"
    else
        echo -e "${YELLOW}⚠ Archive system not initialized${NC}"
    fi
    
    # Check for baseline coverage
    if [ -d "test-reports/baseline" ]; then
        echo -e "${GREEN}✓ Baseline coverage generated${NC}"
        if [ -f "test-reports/baseline/baseline_summary.md" ]; then
            echo -e "  Baseline report: test-reports/baseline/baseline_summary.md"
        fi
    else
        echo -e "${YELLOW}⚠ Baseline coverage not generated${NC}"
    fi
    
    # Check for post-cleanup coverage
    if [ -d "test-reports/post-cleanup" ]; then
        echo -e "${GREEN}✓ Post-cleanup coverage generated${NC}"
    else
        echo -e "${YELLOW}⚠ Post-cleanup coverage not generated${NC}"
    fi
    
    # Check for comparison report
    if [ -d "test-reports/comparison" ]; then
        echo -e "${GREEN}✓ Coverage comparison completed${NC}"
        latest_comparison=$(ls -t test-reports/comparison/coverage_comparison_*.md 2>/dev/null | head -1)
        if [ -n "$latest_comparison" ]; then
            echo -e "  Latest comparison: $latest_comparison"
        fi
    else
        echo -e "${YELLOW}⚠ Coverage comparison not completed${NC}"
    fi
    
    # Count root directory files
    root_files=$(find . -maxdepth 1 -type f | wc -l)
    echo -e "\nCurrent root directory files: $root_files"
    
    # Show recent cleanup log entries if available
    if [ -f "_archive_/cleanup-log.md" ]; then
        echo -e "\nRecent cleanup actions:"
        tail -5 "_archive_/cleanup-log.md" | grep -v "^|.*|.*|.*|.*|$" | tail -3 || echo "  No recent actions"
    fi
}

# Phase 1: Pre-Cleanup Assessment
run_phase1() {
    echo -e "\n${BOLD}${CYAN}=======================================${NC}"
    echo -e "${BOLD}${CYAN}   PHASE 1: PRE-CLEANUP ASSESSMENT     ${NC}"
    echo -e "${BOLD}${CYAN}=======================================${NC}"
    
    # Initialize archive system if not exists
    if [ ! -d "_archive_" ]; then
        run_with_error_handling \
            "./scripts/setup-archive-system.sh" \
            "Setting up archive system"
    else
        echo -e "${GREEN}✓ Archive system already initialized${NC}"
    fi
    
    # Generate baseline coverage
    run_with_error_handling \
        "./scripts/generate_baseline_coverage.sh" \
        "Generating baseline coverage reports"
    
    echo -e "\n${GREEN}✓ Phase 1 completed successfully${NC}"
    echo -e "${YELLOW}Next step: Review baseline reports and run 'phase2'${NC}"
}

# Phase 2: Core Structure Cleanup
run_phase2() {
    echo -e "\n${BOLD}${CYAN}=======================================${NC}"
    echo -e "${BOLD}${CYAN}   PHASE 2: CORE STRUCTURE CLEANUP     ${NC}"
    echo -e "${BOLD}${CYAN}=======================================${NC}"
    
    # Check if Phase 1 was completed
    if [ ! -d "test-reports/baseline" ]; then
        echo -e "${RED}Error: Phase 1 not completed. Please run 'phase1' first.${NC}"
        exit 1
    fi
    
    echo -e "\n${YELLOW}Starting root directory cleanup...${NC}"
    
    # Archive test files in root
    echo -e "\n${BLUE}Archiving test files from root directory...${NC}"
    for pattern in "test-*.js" "test_*.py" "*.test.js" "*.spec.js"; do
        for file in $pattern; do
            if [ -f "$file" ]; then
                echo -e "  Archiving: $file"
                ./scripts/archive-file.sh "$file" "Root directory cleanup - moved to /tests directory"
            fi
        done
    done
    
    # Archive temporary and debug files
    echo -e "\n${BLUE}Archiving temporary and debug files...${NC}"
    for pattern in "*.html" "*.png" "debug_*.py" "temp_*.py" "*_debug.js"; do
        for file in $pattern; do
            if [ -f "$file" ] && [[ ! "$file" =~ ^(README|LICENSE|CHANGELOG) ]]; then
                echo -e "  Archiving: $file"
                ./scripts/archive-file.sh "$file" "Root directory cleanup - temporary/debug file"
            fi
        done
    done
    
    # Archive duplicate configuration files
    echo -e "\n${BLUE}Checking for duplicate configuration files...${NC}"
    config_files=$(find . -maxdepth 1 -name "*.ini" -o -name "*.config.js" -o -name "*.yml" -o -name "*.yaml" | grep -v "pytest.ini\|playwright.config.js\|docker-compose.yml" || true)
    for file in $config_files; do
        if [ -f "$file" ]; then
            echo -e "  Archiving: $file"
            ./scripts/archive-file.sh "$file" "Root directory cleanup - duplicate configuration"
        fi
    done
    
    echo -e "\n${GREEN}✓ Phase 2 completed successfully${NC}"
    echo -e "${YELLOW}Next step: Review changes and run 'phase3'${NC}"
}

# Phase 3: Advanced Organization
run_phase3() {
    echo -e "\n${BOLD}${CYAN}=======================================${NC}"
    echo -e "${BOLD}${CYAN}   PHASE 3: ADVANCED ORGANIZATION      ${NC}"
    echo -e "${BOLD}${CYAN}=======================================${NC}"
    
    echo -e "\n${YELLOW}Organizing subdirectories...${NC}"
    
    # This phase would include more sophisticated cleanup
    # For now, we'll focus on validation and documentation
    echo -e "${BLUE}Phase 3 implementation would include:${NC}"
    echo -e "  - API directory organization"
    echo -e "  - Scripts categorization"
    echo -e "  - Frontend cleanup"
    echo -e "  - Configuration standardization"
    
    echo -e "\n${GREEN}✓ Phase 3 completed successfully${NC}"
    echo -e "${YELLOW}Next step: Run 'phase4' for validation${NC}"
}

# Phase 4: Validation and Documentation
run_phase4() {
    echo -e "\n${BOLD}${CYAN}=======================================${NC}"
    echo -e "${BOLD}${CYAN}   PHASE 4: VALIDATION & DOCUMENTATION ${NC}"
    echo -e "${BOLD}${CYAN}=======================================${NC}"
    
    # Generate post-cleanup coverage
    run_with_error_handling \
        "./scripts/generate_post_cleanup_coverage.sh" \
        "Generating post-cleanup coverage reports"
    
    # Compare coverage reports
    run_with_error_handling \
        "./scripts/compare_coverage_reports.sh" \
        "Comparing baseline and post-cleanup coverage"
    
    # Validate archive integrity
    if [ -f "_archive_/validate-archive.sh" ]; then
        run_with_error_handling \
            "./_archive_/validate-archive.sh" \
            "Validating archive integrity"
    fi
    
    echo -e "\n${GREEN}✓ Phase 4 completed successfully${NC}"
    echo -e "${YELLOW}Cleanup process complete! Review comparison reports.${NC}"
}

# Main execution logic
case "${1:-help}" in
    "setup")
        check_prerequisites
        if [ ! -d "_archive_" ]; then
            ./scripts/setup-archive-system.sh
        else
            echo -e "${GREEN}✓ Archive system already initialized${NC}"
        fi
        ;;
        
    "phase1")
        check_prerequisites
        run_phase1
        ;;
        
    "phase2")
        check_prerequisites
        run_phase2
        ;;
        
    "phase3")
        check_prerequisites
        run_phase3
        ;;
        
    "phase4")
        check_prerequisites
        run_phase4
        ;;
        
    "all")
        check_prerequisites
        echo -e "${BOLD}${BLUE}Running complete cleanup process...${NC}"
        run_phase1
        echo -e "\n${YELLOW}Pausing for review. Press Enter to continue to Phase 2...${NC}"
        read -r
        run_phase2
        echo -e "\n${YELLOW}Pausing for review. Press Enter to continue to Phase 3...${NC}"
        read -r
        run_phase3
        echo -e "\n${YELLOW}Pausing for review. Press Enter to continue to Phase 4...${NC}"
        read -r
        run_phase4
        ;;
        
    "status")
        show_status
        ;;
        
    "help"|*)
        show_usage
        ;;
esac

echo -e "\n${BOLD}${GREEN}=======================================${NC}"
echo -e "${BOLD}${GREEN}   Master Cleanup Script Complete      ${NC}"
echo -e "${BOLD}${GREEN}=======================================${NC}"

echo -e "\nLog file: ${BLUE}$LOG_FILE${NC}"
echo -e "For detailed status: ${BLUE}$0 status${NC}"

exit 0
