FROM node:18

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    libmagic1 \
    openssh-client \
    libyaml-dev \
    && rm -rf /var/lib/apt/lists/*

# Create and activate Python virtual environment
RUN python3 -m venv /app/venv
ENV PATH="/app/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN pip install pytest pytest-cov

# Copy package files and install Node.js dependencies
COPY package*.json ./
RUN npm ci

# Copy application code
COPY . .

# Install Playwright browsers
RUN npx playwright install chromium

# Set environment variables
ENV NODE_ENV=test

# Create necessary directories
RUN mkdir -p /app/test-results /app/test_logs /app/uploads /app/logs

# Default command to run tests
CMD ["python3", "-m", "pytest", "api/tests/test_consolidated_vagrant.py", "-v"]