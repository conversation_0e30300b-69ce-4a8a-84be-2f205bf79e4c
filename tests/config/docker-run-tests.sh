#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/docker_test_run_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Create a temporary Dockerfile for testing
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

section "PREPARING TEST CONTAINER"
log "Creating testing Docker container"

# Create Dockerfile for testing
cat > $TEMP_DIR/Dockerfile.test << 'EOF'
FROM python:3.10-slim

WORKDIR /app

# Install essential packages
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt pytest pytest-mock

# Copy the application code
COPY . .

# Set the Python path to include the current directory
ENV PYTHONPATH="/app:${PYTHONPATH}"

# Set up environment variables for tests 
ENV TEST_MODE=true
ENV DATABASE_URL="sqlite:///:memory:"
EOF

# Build the Docker image
log "Building Docker test image"
docker build -t turdparty-test -f $TEMP_DIR/Dockerfile.test . | tee -a "$LOG_FILE"

if [ $? -ne 0 ]; then
    error "Failed to build Docker test image"
    exit 1
fi

success "Successfully built Docker test image"

# Run the Python tests (excluding Playwright tests)
section "RUNNING PYTHON TESTS IN CONTAINER"
log "Running Python tests in Docker container"

docker run --rm -it turdparty-test sh -c "find . -type f -name 'test_*.py' -o -name '*_test.py' | grep -v '__pycache__' | grep -v 'tests/playwright' | xargs -I {} python -m pytest {} -v" | tee -a "$LOG_FILE"

TEST_EXIT_CODE=${PIPESTATUS[0]}

if [ $TEST_EXIT_CODE -eq 0 ]; then
    success "All tests passed!"
else
    error "Some tests failed with exit code $TEST_EXIT_CODE"
fi

# Summary
section "TEST SUMMARY"
log "Completed test run at $(date)"

# Display pass/fail count from the log file
if grep -q "failed" "$LOG_FILE"; then
    FAILED_COUNT=$(grep "failed" "$LOG_FILE" | grep "=" | tail -1 | awk '{print $1}')
    error "Tests failed: $FAILED_COUNT"
else
    success "All tests passed"
fi

# Display any skipped tests
if grep -q "skipped" "$LOG_FILE"; then
    SKIPPED_COUNT=$(grep "skipped" "$LOG_FILE" | grep "=" | tail -1 | awk '{print $3}')
    log "Tests skipped: $SKIPPED_COUNT"
fi

exit $TEST_EXIT_CODE 