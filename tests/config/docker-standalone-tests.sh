#!/bin/bash

# Create a temporary directory for the tests
temp_dir=$(mktemp -d)
echo "Creating temporary test directory: $temp_dir"

# Create the file structure
mkdir -p $temp_dir/ui/services/tests

# Create minimalist versions of the necessary files
cat > $temp_dir/ui/services/connector.py << 'EOF'
"""Service connector pattern implementation."""
import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type, Union, List
import requests

# Set up logging
logger = logging.getLogger(__name__)

T = TypeVar('T')

class ServiceConnector(Generic[T]):
    """Generic service connector for API communication."""
    
    def __init__(self, base_url: str, model_class: Type[T], timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.model_class = model_class
        self.timeout = timeout
    
    def _build_url(self, endpoint: str) -> str:
        endpoint = endpoint.lstrip('/')
        return f"{self.base_url}/{endpoint}"
    
    def _handle_response(self, response: requests.Response) -> Union[T, List[T], Dict[str, Any]]:
        if response.status_code >= 400:
            raise ValueError(f"API error: {response.text}")
            
        # Handle empty responses
        if not response.content or response.status_code == 204:
            return {"message": "success"}
            
        # Parse JSON response
        try:
            data = response.json()
            if isinstance(data, list):
                return [self.model_class(**item) for item in data]
            elif isinstance(data, dict):
                if any(key in data for key in ['id', 'name']):
                    return self.model_class(**data)
                return data
            return data
        except ValueError:
            return {"message": "success" if not response.text else response.text}
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Union[T, List[T], Dict[str, Any]]:
        url = self._build_url(endpoint)
        response = requests.get(url, params=params, timeout=self.timeout)
        return self._handle_response(response)
    
    def post(self, endpoint: str, json: Optional[Dict[str, Any]] = None) -> Union[T, Dict[str, Any]]:
        url = self._build_url(endpoint)
        response = requests.post(url, json=json, timeout=self.timeout)
        return self._handle_response(response)
    
    def put(self, endpoint: str, json: Optional[Dict[str, Any]] = None) -> Union[T, Dict[str, Any]]:
        url = self._build_url(endpoint)
        response = requests.put(url, json=json, timeout=self.timeout)
        return self._handle_response(response)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        url = self._build_url(endpoint)
        response = requests.delete(url, timeout=self.timeout)
        return self._handle_response(response)
EOF

cat > $temp_dir/ui/services/config.py << 'EOF'
"""Configuration for services."""
import os
from typing import Dict, Any

# Default API configuration
DEFAULT_API_URL = "http://localhost:8000"
DEFAULT_TIMEOUT = 10  # seconds

def get_from_env(key: str, default: Any) -> Any:
    value = os.environ.get(key)
    if value is None or value.strip() == '':
        return default
    return value

def parse_timeout(value: str, default: int) -> int:
    if not value or not value.strip():
        return default
    try:
        timeout = int(value)
        return timeout if timeout > 0 else default
    except (ValueError, TypeError):
        return default

# Get API URL from environment or use default
API_URL = get_from_env("API_URL", DEFAULT_API_URL)

# Get timeout from environment or use default
REQUEST_TIMEOUT_STR = get_from_env("REQUEST_TIMEOUT", str(DEFAULT_TIMEOUT))
REQUEST_TIMEOUT = parse_timeout(REQUEST_TIMEOUT_STR, DEFAULT_TIMEOUT)

# Service configuration dictionary
SERVICE_CONFIG: Dict[str, Any] = {
    "api_url": API_URL,
    "timeout": REQUEST_TIMEOUT,
}
EOF

cat > $temp_dir/ui/services/item_service.py << 'EOF'
"""Item service connector for API communication."""
from typing import List, Dict, Any, Optional
import logging
from dataclasses import dataclass

from ui.services.connector import ServiceConnector

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class Item:
    """Data class for item objects."""
    id: int
    name: str
    description: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None

class ItemService(ServiceConnector[Item]):
    """Service connector for item-related API endpoints."""
    
    def __init__(self, base_url: str):
        super().__init__(base_url, Item)
        self.endpoint = "api/v1/items"
    
    def get_items(self, skip: int = 0, limit: int = 100, name: Optional[str] = None) -> List[Item]:
        params = {"skip": skip, "limit": limit}
        if name:
            params["name"] = name
            
        try:
            return self.get(self.endpoint, params=params)
        except Exception as e:
            logger.error(f"Error getting items: {str(e)}")
            return []
    
    def get_item(self, item_id: int) -> Optional[Item]:
        try:
            return self.get(f"{self.endpoint}/{item_id}")
        except Exception as e:
            logger.error(f"Error getting item {item_id}: {str(e)}")
            return None
    
    def create_item(self, name: str, description: Optional[str] = None) -> Optional[Item]:
        data = {"name": name}
        if description:
            data["description"] = description
            
        try:
            return self.post(self.endpoint, json=data)
        except Exception as e:
            logger.error(f"Error creating item: {str(e)}")
            return None
    
    def update_item(self, item_id: int, name: Optional[str] = None, description: Optional[str] = None) -> Optional[Item]:
        data = {}
        if name:
            data["name"] = name
        if description is not None:
            data["description"] = description
            
        try:
            return self.put(f"{self.endpoint}/{item_id}", json=data)
        except Exception as e:
            logger.error(f"Error updating item {item_id}: {str(e)}")
            return None
    
    def delete_item(self, item_id: int) -> bool:
        try:
            self.delete(f"{self.endpoint}/{item_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting item {item_id}: {str(e)}")
            return False
EOF

cat > $temp_dir/ui/services/__init__.py << 'EOF'
"""Services package for API communication."""
EOF

cat > $temp_dir/ui/__init__.py << 'EOF'
"""UI package initialization."""
EOF

# Create test files
cat > $temp_dir/ui/services/tests/test_config.py << 'EOF'
"""Unit tests for service configuration."""
import pytest
from unittest.mock import patch
import os

from ui.services.config import SERVICE_CONFIG

def test_service_config_default():
    """Test service config with defaults."""
    assert "api_url" in SERVICE_CONFIG
    assert isinstance(SERVICE_CONFIG["api_url"], str)
    assert len(SERVICE_CONFIG["api_url"]) > 0
    assert SERVICE_CONFIG["api_url"] == "http://localhost:8000"  # Default value
    assert "timeout" in SERVICE_CONFIG
    assert isinstance(SERVICE_CONFIG["timeout"], int)
    assert SERVICE_CONFIG["timeout"] == 10  # Default value
EOF

cat > $temp_dir/ui/services/tests/test_connector.py << 'EOF'
"""Unit tests for service connector implementation."""
import pytest
from unittest.mock import MagicMock, patch
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import requests

from ui.services.connector import ServiceConnector

@dataclass
class TestModel:
    """Test model for verification."""
    id: int
    name: str
    value: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None

@pytest.fixture
def connector():
    """Create a service connector with a mock session."""
    connector = ServiceConnector[TestModel](base_url="http://testapi.com", model_class=TestModel)
    return connector

def test_initialization():
    """Test service connector initialization."""
    # Test with trailing slash in URL
    connector = ServiceConnector[TestModel](base_url="http://testapi.com/", model_class=TestModel)
    assert connector.base_url == "http://testapi.com"
    assert connector.model_class == TestModel
    assert connector.timeout == 30  # Default timeout

def test_build_url(connector):
    """Test URL building logic."""
    # Test with leading slash in endpoint
    url = connector._build_url("/endpoint")
    assert url == "http://testapi.com/endpoint"
    
    # Test without leading slash
    url = connector._build_url("endpoint")
    assert url == "http://testapi.com/endpoint"

def test_handle_response_with_model(connector):
    """Test response handling with model class."""
    # Create a mock response
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.content = json.dumps({
        "id": 1,
        "name": "Test",
        "value": "data",
        "created_at": "2024-03-30T12:00:00Z",
        "updated_at": "2024-03-30T12:01:00Z"
    }).encode()
    mock_response.json.return_value = {
        "id": 1,
        "name": "Test",
        "value": "data",
        "created_at": "2024-03-30T12:00:00Z",
        "updated_at": "2024-03-30T12:01:00Z"
    }
    
    result = connector._handle_response(mock_response)
    
    assert isinstance(result, TestModel)
    assert result.id == 1
    assert result.name == "Test"
    assert result.value == "data"
    assert result.created_at == "2024-03-30T12:00:00Z"
    assert result.updated_at == "2024-03-30T12:01:00Z"

def test_handle_response_with_list(connector):
    """Test response handling with list of items."""
    # Create a mock response with a list
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.content = json.dumps([
        {
            "id": 1,
            "name": "Test1",
            "value": "data1",
            "created_at": "2024-03-30T12:00:00Z"
        },
        {
            "id": 2,
            "name": "Test2",
            "value": "data2",
            "created_at": "2024-03-30T12:01:00Z"
        }
    ]).encode()
    mock_response.json.return_value = [
        {
            "id": 1,
            "name": "Test1",
            "value": "data1",
            "created_at": "2024-03-30T12:00:00Z"
        },
        {
            "id": 2,
            "name": "Test2",
            "value": "data2",
            "created_at": "2024-03-30T12:01:00Z"
        }
    ]
    
    result = connector._handle_response(mock_response)
    
    assert isinstance(result, list)
    assert len(result) == 2
    assert isinstance(result[0], TestModel)
    assert result[0].id == 1
    assert result[0].name == "Test1"
    assert result[1].id == 2
    assert result[1].name == "Test2"

def test_get_request(connector):
    """Test GET request execution."""
    with patch('requests.get') as mock_get:
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = json.dumps({
            "id": 1,
            "name": "Test",
            "value": "data",
            "created_at": "2024-03-30T12:00:00Z"
        }).encode()
        mock_response.json.return_value = {
            "id": 1,
            "name": "Test",
            "value": "data",
            "created_at": "2024-03-30T12:00:00Z"
        }
        mock_get.return_value = mock_response
        
        # Execute GET request
        result = connector.get("test-endpoint", params={"param": "value"})
        
        # Verify request was made correctly
        mock_get.assert_called_once_with(
            "http://testapi.com/test-endpoint",
            params={"param": "value"},
            timeout=30
        )
        
        # Verify result
        assert isinstance(result, TestModel)
        assert result.id == 1
        assert result.name == "Test"
        assert result.value == "data"

def test_post_request(connector):
    """Test POST request execution."""
    with patch('requests.post') as mock_post:
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.content = json.dumps({
            "id": 1,
            "name": "Created",
            "value": "new",
            "created_at": "2024-03-30T12:00:00Z"
        }).encode()
        mock_response.json.return_value = {
            "id": 1,
            "name": "Created",
            "value": "new",
            "created_at": "2024-03-30T12:00:00Z"
        }
        mock_post.return_value = mock_response
        
        # Execute POST request
        result = connector.post("test-endpoint", json={"name": "Created"})
        
        # Verify request was made correctly
        mock_post.assert_called_once_with(
            "http://testapi.com/test-endpoint",
            json={"name": "Created"},
            timeout=30
        )
        
        # Verify result
        assert isinstance(result, TestModel)
        assert result.id == 1
        assert result.name == "Created"
        assert result.value == "new"

def test_put_request(connector):
    """Test PUT request execution."""
    with patch('requests.put') as mock_put:
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = json.dumps({
            "id": 1,
            "name": "Updated",
            "value": "updated",
            "updated_at": "2024-03-30T12:01:00Z"
        }).encode()
        mock_response.json.return_value = {
            "id": 1,
            "name": "Updated",
            "value": "updated",
            "updated_at": "2024-03-30T12:01:00Z"
        }
        mock_put.return_value = mock_response
        
        # Execute PUT request
        result = connector.put("test-endpoint/1", json={"name": "Updated"})
        
        # Verify request was made correctly
        mock_put.assert_called_once_with(
            "http://testapi.com/test-endpoint/1",
            json={"name": "Updated"},
            timeout=30
        )
        
        # Verify result
        assert isinstance(result, TestModel)
        assert result.id == 1
        assert result.name == "Updated"
        assert result.value == "updated"

def test_delete_request(connector):
    """Test DELETE request execution."""
    with patch('requests.delete') as mock_delete:
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 204
        mock_response.content = b""
        # For empty content, we should return {"message": "success"}
        mock_response.json.side_effect = ValueError("No JSON content")
        mock_delete.return_value = mock_response
        
        # Execute DELETE request
        result = connector.delete("test-endpoint/1")
        
        # Verify request was made correctly
        mock_delete.assert_called_once_with(
            "http://testapi.com/test-endpoint/1",
            timeout=30
        )
        
        # Verify result - for empty responses we should get {"message": "success"}
        assert result == {"message": "success"}
EOF

cat > $temp_dir/ui/services/tests/test_item_service.py << 'EOF'
"""Unit tests for the item service."""
import pytest
from unittest.mock import MagicMock, patch
import json
import logging
from typing import List, Optional

from ui.services.item_service import ItemService, Item

@pytest.fixture
def item_service():
    """Create an item service with a mocked connector."""
    service = ItemService(base_url="http://testapi.com")
    return service

def test_initialization():
    """Test item service initialization."""
    service = ItemService("http://testapi.com")
    assert service.base_url == "http://testapi.com"
    assert service.endpoint == "api/v1/items"
    assert service.model_class == Item

def test_get_items_success(item_service):
    """Test successful retrieval of items."""
    with patch('requests.get') as mock_get:
        # Create sample items
        items_data = [
            {
                "id": 1,
                "name": "Item 1",
                "description": "Description 1",
                "created_at": "2024-03-30T12:00:00Z",
                "updated_at": "2024-03-30T12:01:00Z"
            },
            {
                "id": 2,
                "name": "Item 2",
                "description": "Description 2",
                "created_at": "2024-03-30T12:02:00Z",
                "updated_at": "2024-03-30T12:03:00Z"
            }
        ]
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = json.dumps(items_data).encode()
        mock_response.json.return_value = items_data
        mock_get.return_value = mock_response
        
        # Execute the method
        result = item_service.get_items(skip=0, limit=10)
        
        # Verify the request was made correctly
        mock_get.assert_called_once_with(
            "http://testapi.com/api/v1/items",
            params={"skip": 0, "limit": 10},
            timeout=30
        )
        
        # Verify result
        assert len(result) == 2
        assert result[0].id == 1
        assert result[0].name == "Item 1"
        assert result[0].description == "Description 1"
        assert result[0].created_at == "2024-03-30T12:00:00Z"
        assert result[1].id == 2
        assert result[1].name == "Item 2"
        assert result[1].description == "Description 2"
        assert result[1].created_at == "2024-03-30T12:02:00Z"

def test_get_item_success(item_service):
    """Test successful retrieval of a single item."""
    with patch('requests.get') as mock_get:
        # Create sample item
        item_data = {
            "id": 1,
            "name": "Test Item",
            "description": "Description",
            "created_at": "2024-03-30T12:00:00Z",
            "updated_at": "2024-03-30T12:01:00Z"
        }
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = json.dumps(item_data).encode()
        mock_response.json.return_value = item_data
        mock_get.return_value = mock_response
        
        # Execute the method
        result = item_service.get_item(1)
        
        # Verify the request was made correctly
        mock_get.assert_called_once_with(
            "http://testapi.com/api/v1/items/1",
            params=None,
            timeout=30
        )
        
        # Verify result
        assert result is not None
        assert result.id == 1
        assert result.name == "Test Item"
        assert result.description == "Description"
        assert result.created_at == "2024-03-30T12:00:00Z"

def test_create_item_success(item_service):
    """Test successful item creation."""
    with patch('requests.post') as mock_post:
        # Create sample item
        item_data = {
            "id": 1,
            "name": "New Item",
            "description": "New Description",
            "created_at": "2024-03-30T12:00:00Z",
            "updated_at": "2024-03-30T12:01:00Z"
        }
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.content = json.dumps(item_data).encode()
        mock_response.json.return_value = item_data
        mock_post.return_value = mock_response
        
        # Execute the method
        result = item_service.create_item(name="New Item", description="New Description")
        
        # Verify the request was made correctly
        mock_post.assert_called_once_with(
            "http://testapi.com/api/v1/items",
            json={"name": "New Item", "description": "New Description"},
            timeout=30
        )
        
        # Verify result
        assert result is not None
        assert result.id == 1
        assert result.name == "New Item"
        assert result.description == "New Description"
        assert result.created_at == "2024-03-30T12:00:00Z"

def test_update_item_success(item_service):
    """Test successful item update."""
    with patch('requests.put') as mock_put:
        # Create sample item
        item_data = {
            "id": 1,
            "name": "Updated Item",
            "description": "Updated Description",
            "created_at": "2024-03-30T12:00:00Z",
            "updated_at": "2024-03-30T12:01:00Z"
        }
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = json.dumps(item_data).encode()
        mock_response.json.return_value = item_data
        mock_put.return_value = mock_response
        
        # Execute the method
        result = item_service.update_item(
            item_id=1,
            name="Updated Item",
            description="Updated Description"
        )
        
        # Verify the request was made correctly
        mock_put.assert_called_once_with(
            "http://testapi.com/api/v1/items/1",
            json={"name": "Updated Item", "description": "Updated Description"},
            timeout=30
        )
        
        # Verify result
        assert result is not None
        assert result.id == 1
        assert result.name == "Updated Item"
        assert result.description == "Updated Description"
        assert result.updated_at == "2024-03-30T12:01:00Z"

def test_delete_item_success(item_service):
    """Test successful item deletion."""
    with patch('requests.delete') as mock_delete:
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 204
        mock_response.content = b""
        mock_delete.return_value = mock_response
        
        # Execute the method
        result = item_service.delete_item(1)
        
        # Verify the request was made correctly
        mock_delete.assert_called_once_with(
            "http://testapi.com/api/v1/items/1",
            timeout=30
        )
        
        # Verify result
        assert result is True
EOF

cat > $temp_dir/ui/services/tests/__init__.py << 'EOF'
"""Test package for services."""
EOF

# Create a Docker file in the temporary directory
cat > $temp_dir/Dockerfile << 'EOF'
FROM python:3.10-slim

WORKDIR /app

# Install minimal dependencies
RUN pip install pytest pytest-mock requests

# Copy test code
COPY ui /app/ui

# Make sure the directory is importable
RUN touch __init__.py

# Set Python path
ENV PYTHONPATH="/app:${PYTHONPATH}"

# Default command
CMD ["pytest", "ui/services/tests/", "-v"]
EOF

# Build the Docker image
echo "Building Docker image for tests..."
docker build -t standalone-tests $temp_dir

# Run the tests
echo "Running tests..."
docker run -it standalone-tests

# Clean up
echo "Cleaning up..."
rm -rf $temp_dir
echo "Done." 