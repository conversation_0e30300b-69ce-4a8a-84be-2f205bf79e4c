#!/bin/bash

# Script to test VM injection with both gRPC and SSH modes

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing VM Injection with gRPC and SSH Modes...${NC}"

# Check if the API server is running
echo -e "${YELLOW}Checking if API server is running...${NC}"
API_RESPONSE=$(curl -s http://localhost:3055/api/v1/health || echo "Failed")

if [[ "$API_RESPONSE" == "Failed" ]]; then
    echo -e "${RED}API server is not running or not accessible${NC}"
    echo -e "${YELLOW}Starting API server...${NC}"

    # Start the API server in the background
    docker-compose up -d api

    # Wait for the API server to start
    echo -e "${YELLOW}Waiting for API server to start...${NC}"
    sleep 10

    # Check if the API server is running now
    API_RESPONSE=$(curl -s http://localhost:3055/api/v1/health || echo "Failed")

    if [[ "$API_RESPONSE" == "Failed" ]]; then
        echo -e "${RED}Failed to start API server${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}API server is running${NC}"

# API server is running in test mode with authentication bypassed
echo -e "${GREEN}API server is running in test mode with authentication bypassed${NC}"

# Create a test file
echo -e "${YELLOW}Creating test file...${NC}"
echo "This is a test file for VM injection" > test-file.txt

# Upload the file to the API server
echo -e "${YELLOW}Uploading test file...${NC}"
UPLOAD_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: multipart/form-data" \
    -F "file=@test-file.txt" \
    http://localhost:3055/api/v1/file_upload/)

echo "Upload response: $UPLOAD_RESPONSE"

FILE_UPLOAD_ID=$(echo $UPLOAD_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$FILE_UPLOAD_ID" ]; then
    echo -e "${RED}Failed to upload test file${NC}"
    rm -f test-file.txt
    exit 1
fi

echo -e "${GREEN}Successfully uploaded test file with ID: $FILE_UPLOAD_ID${NC}"

# Create a file selection
echo -e "${YELLOW}Creating file selection...${NC}"
FILE_SELECTION_DATA='{
    "file_upload_id": "'$FILE_UPLOAD_ID'",
    "name": "Test Selection",
    "description": "Test file selection for VM injection"
}'

FILE_SELECTION_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$FILE_SELECTION_DATA" \
    http://localhost:3055/api/v1/file_selection/)

echo "File selection response: $FILE_SELECTION_RESPONSE"

FILE_SELECTION_ID=$(echo $FILE_SELECTION_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$FILE_SELECTION_ID" ]; then
    echo -e "${RED}Failed to create file selection${NC}"
    rm -f test-file.txt
    exit 1
fi

echo -e "${GREEN}Successfully created file selection with ID: $FILE_SELECTION_ID${NC}"

# Get available VMs
echo -e "${YELLOW}Getting available VMs...${NC}"
VMS_RESPONSE=$(curl -s http://localhost:3055/api/v1/vagrant_vm/)

echo "Available VMs: $VMS_RESPONSE"

# Get the first VM ID
VM_ID=$(echo $VMS_RESPONSE | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)

if [ -z "$VM_ID" ]; then
    echo -e "${YELLOW}No VMs found, creating a new VM...${NC}"

    # Get available templates
    TEMPLATES_RESPONSE=$(curl -s http://localhost:3055/api/v1/virtual-machines)

    echo "Available templates: $TEMPLATES_RESPONSE"

    # Get the first template ID
    TEMPLATE_ID=$(echo $TEMPLATES_RESPONSE | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)

    if [ -z "$TEMPLATE_ID" ]; then
        echo -e "${RED}No templates found${NC}"
        rm -f test-file.txt
        exit 1
    fi

    echo -e "${GREEN}Using template ID: $TEMPLATE_ID${NC}"

    # Create a new VM
    VM_CREATE_DATA='{
        "template_id": "'$TEMPLATE_ID'",
        "name": "test-vm-'$(date +%s)'"
    }'

    VM_CREATE_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$VM_CREATE_DATA" \
        http://localhost:3055/api/v1/vagrant_vm/)

    echo "VM create response: $VM_CREATE_RESPONSE"

    VM_ID=$(echo $VM_CREATE_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

    if [ -z "$VM_ID" ]; then
        echo -e "${RED}Failed to create VM${NC}"
        rm -f test-file.txt
        exit 1
    fi

    echo -e "${GREEN}Successfully created VM with ID: $VM_ID${NC}"
fi

echo -e "${GREEN}Using VM ID: $VM_ID${NC}"

# Create a VM injection
echo -e "${YELLOW}Creating VM injection...${NC}"
VM_INJECTION_DATA='{
    "vagrant_vm_id": "'$VM_ID'",
    "file_selection_id": "'$FILE_SELECTION_ID'",
    "description": "Test VM injection",
    "target_path": "/app/test-file.txt",
    "permissions": "0755",
    "additional_command": "echo \"File injected successfully\" > /app/injection-success.txt"
}'

echo "VM injection request data: $VM_INJECTION_DATA"

VM_INJECTION_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$VM_INJECTION_DATA" \
    http://localhost:3055/api/v1/vm_injection/)

echo "VM injection response: $VM_INJECTION_RESPONSE"

VM_INJECTION_ID=$(echo $VM_INJECTION_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$VM_INJECTION_ID" ]; then
    echo -e "${RED}Failed to create VM injection${NC}"
    rm -f test-file.txt
    exit 1
fi

echo -e "${GREEN}Successfully created VM injection with ID: $VM_INJECTION_ID${NC}"

# Poll the VM injection status
echo -e "${YELLOW}Polling VM injection status...${NC}"
MAX_ATTEMPTS=20
ATTEMPTS=0
STATUS="pending"

while [ "$STATUS" = "pending" ] || [ "$STATUS" = "in_progress" ]; do
    ATTEMPTS=$((ATTEMPTS + 1))

    if [ $ATTEMPTS -gt $MAX_ATTEMPTS ]; then
        echo -e "${YELLOW}Maximum attempts reached, stopping polling${NC}"
        break
    fi

    echo -e "${YELLOW}Polling attempt $ATTEMPTS of $MAX_ATTEMPTS...${NC}"

    VM_INJECTION_STATUS_RESPONSE=$(curl -s http://localhost:3055/api/v1/vm_injection/$VM_INJECTION_ID/status)

    echo "Status response: $VM_INJECTION_STATUS_RESPONSE"

    STATUS=$(echo $VM_INJECTION_STATUS_RESPONSE | grep -o '"status":"[^"]*' | cut -d'"' -f4)

    if [ -z "$STATUS" ]; then
        echo -e "${RED}Failed to get VM injection status${NC}"
        break
    fi

    echo -e "${GREEN}VM injection status: $STATUS${NC}"

    if [ "$STATUS" = "completed" ] || [ "$STATUS" = "failed" ]; then
        break
    fi

    sleep 5
done

# Get VM injection details
echo -e "${YELLOW}Getting VM injection details...${NC}"
VM_INJECTION_DETAILS_RESPONSE=$(curl -s http://localhost:3055/api/v1/vm_injection/$VM_INJECTION_ID)

echo "VM injection details: $VM_INJECTION_DETAILS_RESPONSE"

# Check the logs to see which mode was used
echo -e "${YELLOW}Checking logs to see which mode was used...${NC}"
docker logs turdparty-api-1 | grep -A 10 "Connecting to Vagrant service for VM injection $VM_INJECTION_ID"

# Clean up
echo -e "${YELLOW}Cleaning up...${NC}"
rm -f test-file.txt

if [ "$STATUS" = "completed" ]; then
    echo -e "${GREEN}VM injection completed successfully!${NC}"
    exit 0
else
    echo -e "${RED}VM injection failed or timed out!${NC}"
    exit 1
fi
