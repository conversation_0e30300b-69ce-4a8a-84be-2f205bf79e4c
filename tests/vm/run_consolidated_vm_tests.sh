#!/bin/bash

# Set environment variables for testing
export TESTING=true
export PYTHONPATH=$PWD

# Build the Docker test image
echo "Building Docker test image..."
docker build -t turdparty-test-env -f Dockerfile.test .

# Run the tests for consolidated VM endpoints
echo "Running tests for consolidated VM endpoints..."
docker run --rm \
  --network=host \
  -v $PWD:/app \
  -e TESTING=true \
  -e PYTHONPATH=/app \
  turdparty-test-env \
  /app/venv/bin/python -m pytest api/tests/test_consolidated_vagrant.py -v

# Check the exit code
if [ $? -eq 0 ]; then
  echo "Tests passed successfully!"
else
  echo "Tests failed. Please check the output above for details."
fi
