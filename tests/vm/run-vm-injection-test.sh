#!/bin/bash

# Script to build and run the Docker container for VM injection testing

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Building and running VM injection test in Docker...${NC}"

# Build the Docker image
echo -e "${YELLOW}Building Docker image...${NC}"
docker build -t vm-injection-test -f Dockerfile.test .

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to build Docker image${NC}"
    exit 1
fi

echo -e "${GREEN}Docker image built successfully${NC}"

# Run the Docker container
echo -e "${YELLOW}Running VM injection test...${NC}"
docker run --rm \
    --network host \
    -v $(pwd)/test-vm-injection-ssh.sh:/app/test-vm-injection-ssh.sh \
    vm-injection-test python3 -m pytest api/tests/test_consolidated_vagrant.py -v

if [ $? -ne 0 ]; then
    echo -e "${RED}VM injection test failed${NC}"
    exit 1
fi

echo -e "${GREEN}VM injection test completed successfully${NC}"
