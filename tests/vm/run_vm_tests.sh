#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running consolidated VM endpoints tests...${NC}"

# Run the test using the existing script
./run_tests.sh run api/tests/test_consolidated_vagrant.py

# Check exit code
if [ $? -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
    exit 0
else
    echo -e "${RED}Tests failed. Please check the output above for details.${NC}"
    exit 1
fi
