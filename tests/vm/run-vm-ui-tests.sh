#!/bin/bash

# Script to run the VM management UI tests

# Set up output directories
mkdir -p test-screenshots

# Run tests with Play<PERSON>
echo "Running VM management UI tests..."
npx playwright test tests/playwright/vm-management.spec.ts --headed

# Check test results
if [ $? -eq 0 ]; then
  echo "✅ Tests passed successfully!"
else
  echo "❌ Tests failed. Check the output above for details."
fi

# Show test screenshots
echo "Test screenshots saved to test-screenshots/ directory."
echo "You can view them with:"
echo "open test-screenshots/vm-management-page.png"
echo "open test-screenshots/vm-management-error.png" 