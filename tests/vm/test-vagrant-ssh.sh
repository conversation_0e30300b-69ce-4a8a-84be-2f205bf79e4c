#!/bin/bash

# Script to test Vagrant SSH connection

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Vagrant SSH Connection...${NC}"

# Check if Vagrant is installed
if ! command -v vagrant &> /dev/null; then
    echo -e "${RED}Vagrant is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}Vagrant is installed${NC}"

# Check if there are any running VMs
echo -e "${YELLOW}Checking for running VMs...${NC}"
RUNNING_VMS=$(vagrant global-status | grep running | wc -l)

if [ "$RUNNING_VMS" -eq 0 ]; then
    echo -e "${YELLOW}No running VMs found, creating a new VM...${NC}"
    
    # Create a test VM
    mkdir -p test-vm
    cd test-vm
    
    # Create a Vagrantfile
    cat > Vagrantfile << EOF
Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/focal64"
  config.vm.network "private_network", type: "dhcp"
end
EOF
    
    # Start the VM
    vagrant up
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to start VM${NC}"
        cd ..
        rm -rf test-vm
        exit 1
    fi
    
    echo -e "${GREEN}Successfully created and started VM${NC}"
    
    # Test SSH connection
    echo -e "${YELLOW}Testing SSH connection...${NC}"
    vagrant ssh -c "echo 'SSH connection successful'"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to connect to VM via SSH${NC}"
        vagrant destroy -f
        cd ..
        rm -rf test-vm
        exit 1
    fi
    
    echo -e "${GREEN}SSH connection successful${NC}"
    
    # Clean up
    echo -e "${YELLOW}Cleaning up...${NC}"
    vagrant destroy -f
    cd ..
    rm -rf test-vm
else
    echo -e "${GREEN}Found $RUNNING_VMS running VMs${NC}"
    
    # Get the ID of the first running VM
    VM_ID=$(vagrant global-status | grep running | head -1 | awk '{print $1}')
    
    echo -e "${YELLOW}Testing SSH connection to VM $VM_ID...${NC}"
    vagrant ssh $VM_ID -c "echo 'SSH connection successful'"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to connect to VM $VM_ID via SSH${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}SSH connection successful${NC}"
fi

echo -e "${GREEN}Test completed successfully!${NC}"
