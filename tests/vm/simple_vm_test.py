#!/usr/bin/env python3
"""
Simple Vagrant VM simulation tester

This script simulates a VM execution environment for testing purposes.
It provides realistic command output simulation to test VM integration features
without requiring an actual VM.
"""
import sys
import time
import datetime
import os
import argparse
import shlex
import random
import re

def simulate_vm_command(command):
    """Simulate execution of a command on a VM"""
    # Handle piped commands
    if "|" in command:
        pipe_parts = [part.strip() for part in command.split("|")]
        output = simulate_vm_command(pipe_parts[0])
        
        for pipe_part in pipe_parts[1:]:
            if pipe_part.strip().startswith("grep"):
                # Simple grep simulation
                pattern = shlex.split(pipe_part)[1] if len(shlex.split(pipe_part)) > 1 else ""
                if pattern:
                    filtered_lines = []
                    for line in output.split("\n"):
                        if pattern in line:
                            filtered_lines.append(line)
                    output = "\n".join(filtered_lines)
            elif pipe_part.strip().startswith("wc"):
                # Simple wc simulation
                wc_parts = shlex.split(pipe_part)
                if len(wc_parts) > 1 and wc_parts[1] == "-l":
                    output = str(len(output.split("\n")))
                else:
                    # Default wc behavior (lines, words, chars)
                    lines = len(output.split("\n"))
                    words = len(output.split())
                    chars = len(output)
                    output = f"{lines} {words} {chars}"
            elif pipe_part.strip().startswith("head"):
                # Simple head simulation
                head_parts = shlex.split(pipe_part)
                lines = 10  # Default head behavior
                if len(head_parts) > 1 and head_parts[1].startswith("-"):
                    try:
                        lines = int(head_parts[1][1:])
                    except ValueError:
                        pass
                output = "\n".join(output.split("\n")[:lines])
            elif pipe_part.strip().startswith("tail"):
                # Simple tail simulation
                tail_parts = shlex.split(pipe_part)
                lines = 10  # Default tail behavior
                if len(tail_parts) > 1 and tail_parts[1].startswith("-"):
                    try:
                        lines = int(tail_parts[1][1:])
                    except ValueError:
                        pass
                output = "\n".join(output.split("\n")[-lines:])
            elif pipe_part.strip().startswith("sort"):
                # Simple sort simulation
                output = "\n".join(sorted(output.split("\n")))
            elif pipe_part.strip().startswith("uniq"):
                # Simple uniq simulation
                lines = output.split("\n")
                unique_lines = []
                prev_line = None
                for line in lines:
                    if line != prev_line:
                        unique_lines.append(line)
                    prev_line = line
                output = "\n".join(unique_lines)
            elif pipe_part.strip().startswith("awk"):
                # Very basic awk simulation (just for common patterns)
                awk_cmd = pipe_part.strip()
                if "'{print $1}'" in awk_cmd or '{print $1}' in awk_cmd:
                    # Extract first column
                    result_lines = []
                    for line in output.split("\n"):
                        parts = line.split()
                        if parts:
                            result_lines.append(parts[0])
                    output = "\n".join(result_lines)
                else:
                    # Pass through for unsupported awk commands
                    pass
            elif pipe_part.strip().startswith("sed"):
                # Very basic sed simulation (just for s/pattern/replacement/)
                sed_cmd = pipe_part.strip()
                match = re.search(r"s/([^/]+)/([^/]*)/", sed_cmd)
                if match:
                    pattern, replacement = match.groups()
                    output = output.replace(pattern, replacement)
            else:
                # For unsupported pipe commands, just pass through
                pass
                
        return output
    
    # Basic commands
    if command.startswith("ls"):
        # Handle ls with different options
        if "-la" in command or "-al" in command:
            return """total 36
drwxr-xr-x 4 <USER> <GROUP> 4096 Mar 20 15:40 .
drwxr-xr-x 3 <USER>    <GROUP>    4096 Mar 20 15:38 ..
-rw-r--r-- 1 <USER> <GROUP>  220 Mar 20 15:38 .bash_logout
-rw-r--r-- 1 <USER> <GROUP> 3771 Mar 20 15:38 .bashrc
-rw-r--r-- 1 <USER> <GROUP>  807 Mar 20 15:38 .profile
drwxr-xr-x 2 <USER> <GROUP> 4096 Mar 20 15:39 .ssh
-rw-r--r-- 1 <USER> <GROUP>    0 Mar 20 15:40 file1.txt
-rw-r--r-- 1 <USER> <GROUP>  128 Mar 20 15:40 file2.txt
-rwxr-xr-x 1 <USER> <GROUP> 4628 Mar 20 15:40 test_script.py
-rw-r--r-- 1 <USER> <GROUP> 1253 Mar 20 15:39 README.md
drwxr-xr-x 2 <USER> <GROUP> 4096 Mar 20 15:39 .config"""
        elif "-l" in command:
            return """total 28
-rw-r--r-- 1 <USER> <GROUP>    0 Mar 20 15:40 file1.txt
-rw-r--r-- 1 <USER> <GROUP>  128 Mar 20 15:40 file2.txt
-rwxr-xr-x 1 <USER> <GROUP> 4628 Mar 20 15:40 test_script.py
-rw-r--r-- 1 <USER> <GROUP> 1253 Mar 20 15:39 README.md"""
        else:
            return "file1.txt\nfile2.txt\ntest_script.py\nREADME.md"
    
    elif command.startswith("cat"):
        # Extract filename from command
        parts = shlex.split(command)
        if len(parts) < 2:
            return "cat: missing operand"
        
        filename = parts[1]
        
        # Simulate cat for different files
        if filename == "/etc/passwd":
            return """root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync
games:x:5:60:games:/usr/games:/usr/sbin/nologin
man:x:6:12:man:/var/cache/man:/usr/sbin/nologin
lp:x:7:7:lp:/var/spool/lpd:/usr/sbin/nologin
mail:x:8:8:mail:/var/mail:/usr/sbin/nologin
news:x:9:9:news:/var/spool/news:/usr/sbin/nologin
uucp:x:10:10:uucp:/var/spool/uucp:/usr/sbin/nologin
proxy:x:13:13:proxy:/bin:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
backup:x:34:34:backup:/var/backups:/usr/sbin/nologin
list:x:38:38:Mailing List Manager:/var/list:/usr/sbin/nologin
irc:x:39:39:ircd:/var/run/ircd:/usr/sbin/nologin
gnats:x:41:41:Gnats Bug-Reporting System (admin):/var/lib/gnats:/usr/sbin/nologin
nobody:x:65534:65534:nobody:/nonexistent:/usr/sbin/nologin
systemd-network:x:100:102:systemd Network Management,,,:/run/systemd:/usr/sbin/nologin
systemd-resolve:x:101:103:systemd Resolver,,,:/run/systemd:/usr/sbin/nologin
systemd-timesync:x:102:104:systemd Time Synchronization,,,:/run/systemd:/usr/sbin/nologin
messagebus:x:103:106::/nonexistent:/usr/sbin/nologin
sshd:x:104:65534::/run/sshd:/usr/sbin/nologin
vagrant:x:1000:1000::/home/<USER>/bin/bash"""
        elif filename == "/etc/hostname":
            return "ubuntu-s-2vcpu-4gb-fra1-01-vagrant-vm"
        elif filename == "/proc/cpuinfo":
            return """processor       : 0
vendor_id       : GenuineIntel
cpu family      : 6
model           : 85
model name      : Intel(R) Xeon(R) CPU @ 2.20GHz
stepping        : 3
cpu MHz         : 2200.000
cache size      : 56320 KB
physical id     : 0
siblings        : 2
core id         : 0
cpu cores       : 1
apicid          : 0
initial apicid  : 0
fpu             : yes
fpu_exception   : yes
cpuid level     : 13
wp              : yes
flags           : fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss ht syscall nx pdpe1gb rdtscp lm constant_tsc rep_good nopl xtopology nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 fma cx16 pcid sse4_1 sse4_2 x2apic movbe popcnt aes xsave avx f16c rdrand hypervisor lahf_lm abm 3dnowprefetch invpcid_single ssbd ibrs ibpb stibp fsgsbase tsc_adjust bmi1 hle avx2 smep bmi2 erms invpcid rtm mpx avx512f rdseed adx smap clflushopt xsaveopt xsavec xgetbv1 xsaves arat md_clear arch_capabilities

processor       : 1
vendor_id       : GenuineIntel
cpu family      : 6
model           : 85
model name      : Intel(R) Xeon(R) CPU @ 2.20GHz
stepping        : 3
cpu MHz         : 2200.000
cache size      : 56320 KB
physical id     : 0
siblings        : 2
core id         : 0
cpu cores       : 1
apicid          : 1
initial apicid  : 1
fpu             : yes
fpu_exception   : yes
cpuid level     : 13
wp              : yes
flags           : fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss ht syscall nx pdpe1gb rdtscp lm constant_tsc rep_good nopl xtopology nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 fma cx16 pcid sse4_1 sse4_2 x2apic movbe popcnt aes xsave avx f16c rdrand hypervisor lahf_lm abm 3dnowprefetch invpcid_single ssbd ibrs ibpb stibp fsgsbase tsc_adjust bmi1 hle avx2 smep bmi2 erms invpcid rtm mpx avx512f rdseed adx smap clflushopt xsaveopt xsavec xgetbv1 xsaves arat md_clear arch_capabilities"""
        elif filename == "/etc/os-release":
            return """NAME="Ubuntu"
VERSION="20.04.6 LTS (Focal Fossa)"
ID=ubuntu
ID_LIKE=debian
PRETTY_NAME="Ubuntu 20.04.6 LTS"
VERSION_ID="20.04"
HOME_URL="https://www.ubuntu.com/"
SUPPORT_URL="https://help.ubuntu.com/"
BUG_REPORT_URL="https://bugs.launchpad.net/ubuntu/"
PRIVACY_POLICY_URL="https://www.ubuntu.com/legal/terms-and-policies/privacy-policy"
VERSION_CODENAME=focal
UBUNTU_CODENAME=focal"""
        elif filename.startswith("/home/<USER>/"):
            return f"Contents of {filename}:\nThis is simulated content for {filename}\nLine 2 of simulated content.\nLine 3 of simulated content."
        else:
            return f"cat: {filename}: No such file or directory"
    
    elif command.startswith("ps"):
        if "aux" in command:
            return """USER         PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root           1  0.0  0.1 168924 11364 ?        Ss   15:38   0:02 /sbin/init
root           2  0.0  0.0      0     0 ?        S    15:38   0:00 [kthreadd]
root           3  0.0  0.0      0     0 ?        I<   15:38   0:00 [rcu_gp]
root           4  0.0  0.0      0     0 ?        I<   15:38   0:00 [rcu_par_gp]
root          10  0.0  0.0      0     0 ?        I<   15:38   0:00 [mm_percpu_wq]
root          11  0.0  0.0      0     0 ?        S    15:38   0:00 [rcu_tasks_rude_]
root          12  0.0  0.0      0     0 ?        S    15:38   0:00 [rcu_tasks_trace]
root          13  0.0  0.0      0     0 ?        S    15:38   0:00 [ksoftirqd/0]
root        1234  0.0  0.1  10924  4324 pts/0    Ss   15:39   0:00 /bin/bash
vagrant     1450  0.0  0.0   8932  3128 pts/0    S+   15:40   0:00 python3 test_script.py
vagrant     1472  0.0  0.0  10612  3304 pts/0    R+   15:40   0:00 ps aux"""
        else:
            return """  PID TTY          TIME CMD
 1234 pts/0    00:00:00 bash
 1450 pts/0    00:00:00 python3
 1472 pts/0    00:00:00 ps"""
    
    elif "echo" in command:
        # Extract text after echo
        echo_text = command.split("echo", 1)[1].strip()
        if echo_text.startswith('"') and echo_text.endswith('"'):
            echo_text = echo_text[1:-1]
        elif echo_text.startswith("'") and echo_text.endswith("'"):
            echo_text = echo_text[1:-1]
        return echo_text
    
    elif command.startswith("grep"):
        # Not meant to be called directly but to handle piped output
        return "Error: grep needs input from pipe or file"
    
    elif command.startswith("hostname"):
        return "ubuntu-s-2vcpu-4gb-fra1-01-vagrant-vm"
    
    elif command.startswith("uname"):
        if "-a" in command:
            return "Linux ubuntu-s-2vcpu-4gb-fra1-01-vagrant-vm 5.15.0-1031-aws #38-Ubuntu SMP Wed Feb 22 14:07:48 UTC 2023 x86_64 GNU/Linux"
        return "Linux"
    
    elif command.startswith("lscpu"):
        return """Architecture:          x86_64
CPU op-mode(s):      32-bit, 64-bit
Byte Order:          Little Endian
Address sizes:       46 bits physical, 48 bits virtual
CPU(s):              2
On-line CPU(s) list: 0,1
Thread(s) per core:  2
Core(s) per socket:  1
Socket(s):           1
NUMA node(s):        1
Vendor ID:           GenuineIntel
CPU family:          6
Model:               85
Model name:          Intel(R) Xeon(R) CPU @ 2.20GHz
Stepping:            3
CPU MHz:             2200.000
BogoMIPS:            4400.00
Hypervisor vendor:   KVM
Virtualization type: full
L1d cache:           32K
L1i cache:           32K
L2 cache:            1024K
L3 cache:            56320K
NUMA node0 CPU(s):   0,1"""
    
    elif command.startswith("free"):
        return """               total        used        free      shared  buff/cache   available
Mem:         4037056     1091664     1201156      108992     1744236     2643904
Swap:        2097148           0     2097148"""
    
    elif command.startswith("df"):
        return """Filesystem     1K-blocks    Used Available Use% Mounted on
udev             1983588       0   1983588   0% /dev
tmpfs             403728    1112    402616   1% /run
/dev/sda1       16446464 5648128  10782952  35% /
tmpfs            2018640       0   2018640   0% /dev/shm
tmpfs               5120       0      5120   0% /run/lock
tmpfs            2018640       0   2018640   0% /sys/fs/cgroup
/dev/sda15        106858    5305    101554   5% /boot/efi
tmpfs             403724       0    403724   0% /run/user/1000
vagrant          9713452 5868428   3845024  61% /vagrant"""
    
    elif command.startswith("ip addr"):
        return """1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host 
       valid_lft forever preferred_lft forever
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc fq_codel state UP group default qlen 1000
    link/ether 02:42:ac:11:00:02 brd ff:ff:ff:ff:ff:ff
    inet **********/24 brd ************ scope global eth0
       valid_lft forever preferred_lft forever
    inet6 fe80::42:acff:fe11:2/64 scope link 
       valid_lft forever preferred_lft forever"""
    
    # Default case
    return f"Simulated output for command: {command}\nCommand executed successfully in simulation mode."

def main():
    """Main function"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Vagrant VM Simulation Tester")
    parser.add_argument("command", nargs="?", help="Command to execute in the simulated VM")
    args = parser.parse_args()
    
    # Print header
    print("Vagrant VM Simulation Tester")
    print("----------------------------")
    current_time = datetime.datetime.now()
    print(f"Current time: {current_time}")
    print(f"Python version: {sys.version}")
    print()
    
    # Use default command if none provided
    if not args.command:
        default_cmd = "uname -a && hostname && echo 'Test successful!'"
        print(f"No command provided, using default: {default_cmd}")
        command = default_cmd
    else:
        command = args.command
        print(f"Executing command: {command}")
    
    print()
    
    # Split command if it contains &&
    if "&&" in command:
        commands = command.split("&&")
        commands = [cmd.strip() for cmd in commands]
    else:
        commands = [command]
    
    # Execute each command
    for cmd in commands:
        print(f"Simulating command: {cmd}")
        print("="*50)
        output = simulate_vm_command(cmd)
        print(output)
        print("="*50)
        
        # Add a small delay to simulate command execution time
        time.sleep(0.2 + random.random() * 0.3)
    
    print("\nSimulation completed successfully!")

if __name__ == "__main__":
    main() 