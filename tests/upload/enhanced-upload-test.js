const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const assert = require('assert');

// Enhanced configuration with multiple targets and fallbacks
const config = {
  targets: {
    frontend: {
      baseUrl: process.env.FRONTEND_URL || 'http://localhost:3100',
      uploadUrl: process.env.UPLOAD_URL || 'http://localhost:3100/upload',
      filesUrl: process.env.FILES_URL || 'http://localhost:3100/files',
    },
    dockerFrontend: {
      baseUrl: 'http://host.docker.internal:3100',
      uploadUrl: 'http://host.docker.internal:3100/upload',
      filesUrl: 'http://host.docker.internal:3100/files',
    },
    containerNetwork: {
      // These will be populated dynamically from environment if available
      baseUrl: process.env.CONTAINER_FRONTEND_URL || '',
      uploadUrl: process.env.CONTAINER_UPLOAD_URL || '',
      filesUrl: process.env.CONTAINER_FILES_URL || '',
    },
    api: {
      baseUrl: process.env.API_URL || 'http://localhost:3050',
      uploadUrl: process.env.API_UPLOAD_URL || 'http://localhost:3050/api/v1/files',
      filesUrl: process.env.API_FILES_URL || 'http://localhost:3050/api/v1/files',
    }
  },
  
  // Test file configuration
  testFile: {
    path: process.env.TEST_FILE_PATH || './test-upload.txt',
    content: `Test file for upload functionality testing
Created at: ${new Date().toISOString()}
Random ID: ${Math.random().toString(36).substring(2, 15)}`,
  },
  
  // Screenshot directory
  screenshotDir: process.env.SCREENSHOT_DIR || './test_screenshots',
  
  // Browser options
  browserOptions: {
    headless: process.env.HEADLESS !== 'false',
    slowMo: parseInt(process.env.SLOW_MO || '50'),
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage'
    ]
  },
  
  // Timeouts
  timeouts: {
    navigation: parseInt(process.env.NAVIGATION_TIMEOUT || '30000'),
    assertion: parseInt(process.env.ASSERTION_TIMEOUT || '10000'),
    upload: parseInt(process.env.UPLOAD_TIMEOUT || '45000')
  },
  
  // Retry options
  retries: parseInt(process.env.TEST_RETRIES || '2'),
  retryDelay: parseInt(process.env.RETRY_DELAY || '2000')
};

// Prepare diagnostics directory
console.log('Setting up test environment...');
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
  console.log(`Created screenshot directory: ${config.screenshotDir}`);
}

// Create test file
if (!fs.existsSync(config.testFile.path)) {
  fs.writeFileSync(config.testFile.path, config.testFile.content);
  console.log(`Created test file: ${config.testFile.path}`);
} else {
  console.log(`Using existing test file: ${config.testFile.path}`);
}

// Helper function to take screenshots
async function takeScreenshot(page, name) {
  const screenshotPath = path.join(config.screenshotDir, `${name}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  console.log(`Screenshot saved: ${screenshotPath}`);
  return screenshotPath;
}

// Helper function to get container environment info for diagnostics
function getContainerEnvironmentInfo() {
  const info = {
    isDocker: false,
    containerName: '',
    containerIp: '',
    networkInfo: {}
  };
  
  // Check if running in a container
  if (fs.existsSync('/.dockerenv')) {
    info.isDocker = true;
    
    try {
      // Get container hostname
      info.containerName = fs.readFileSync('/etc/hostname', 'utf8').trim();
      
      // Try to get container networking info if possible
      try {
        // This is a simplified approach - in a real test we might use proper tools
        const ipCmdOutput = require('child_process').execSync('hostname -i || ip addr | grep inet').toString();
        info.containerIp = ipCmdOutput.trim();
      } catch (err) {
        info.containerIp = 'Could not determine';
      }
    } catch (err) {
      console.error('Error getting container environment info:', err);
    }
  }
  
  return info;
}

// Enhanced diagnostic logging
function logDiagnosticInfo() {
  console.log('=== TEST ENVIRONMENT INFO ===');
  console.log(`Node.js version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);
  
  const containerInfo = getContainerEnvironmentInfo();
  console.log(`Running in container: ${containerInfo.isDocker}`);
  if (containerInfo.isDocker) {
    console.log(`Container name: ${containerInfo.containerName}`);
    console.log(`Container IP: ${containerInfo.containerIp}`);
  }
  
  console.log('=== TEST CONFIGURATION ===');
  console.log(`Frontend URL: ${config.targets.frontend.baseUrl}`);
  console.log(`Upload URL: ${config.targets.frontend.uploadUrl}`);
  console.log(`Docker Frontend URL: ${config.targets.dockerFrontend.baseUrl}`);
  console.log(`API URL: ${config.targets.api.baseUrl}`);
  console.log(`Test file: ${config.testFile.path}`);
  console.log(`Screenshot directory: ${config.screenshotDir}`);
  console.log(`Browser headless: ${config.browserOptions.headless}`);
  console.log('==========================');
}

// File upload test with specified target environment
async function runFileUploadTest(targetKey = 'frontend') {
  logDiagnosticInfo();
  
  const target = config.targets[targetKey];
  if (!target || !target.baseUrl) {
    throw new Error(`Invalid target configuration for "${targetKey}"`);
  }
  
  const browser = await chromium.launch(config.browserOptions);
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    console.log(`Starting file upload test against ${targetKey} (${target.baseUrl})`);
    
    // Step 1: Navigate to the upload page
    console.log(`Step 1: Navigating to upload page: ${target.uploadUrl}`);
    try {
      await page.goto(target.uploadUrl, { timeout: config.timeouts.navigation });
      await page.waitForLoadState('networkidle');
      await takeScreenshot(page, `01-${targetKey}-upload-page`);
    } catch (error) {
      console.error(`Navigation failed: ${error.message}`);
      await takeScreenshot(page, `01-${targetKey}-navigation-error`);
      throw new Error(`Failed to navigate to ${target.uploadUrl}: ${error.message}`);
    }
    
    // Step 2: Fill upload form
    console.log('Step 2: Filling upload form');
    
    // Check if form elements exist
    const hasDescriptionField = await page.locator('textarea[placeholder*="description"], textarea[name="description"]').count() > 0;
    const hasFileInput = await page.locator('input[type="file"]').count() > 0;
    
    if (!hasDescriptionField || !hasFileInput) {
      console.error('Upload form elements not found');
      await takeScreenshot(page, `02-${targetKey}-form-elements-missing`);
      
      // Dump page HTML for debugging
      const html = await page.content();
      const debugPath = path.join(config.screenshotDir, `${targetKey}-page-html.txt`);
      fs.writeFileSync(debugPath, html);
      console.log(`Page HTML saved to ${debugPath}`);
      
      throw new Error('Upload form elements not found on page');
    }
    
    // Fill description
    const description = `Upload test to ${targetKey} - ${new Date().toISOString()}`;
    await page.locator('textarea[placeholder*="description"], textarea[name="description"]').fill(description);
    
    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(config.testFile.path);
    await takeScreenshot(page, `02-${targetKey}-file-selected`);
    
    // Step 3: Submit upload
    console.log('Step 3: Submitting the upload');
    const uploadButton = page.locator('button:has-text("Upload"), button[type="submit"]');
    
    if (await uploadButton.count() === 0) {
      console.error('Upload button not found');
      await takeScreenshot(page, `03-${targetKey}-upload-button-missing`);
      throw new Error('Upload button not found on page');
    }
    
    await uploadButton.click();
    
    // Step 4: Wait for upload result
    console.log('Step 4: Waiting for upload to complete');
    try {
      await page.waitForSelector('.ant-message-success, .upload-success, .success-message', { 
        timeout: config.timeouts.upload 
      });
      console.log('✓ Upload completed successfully');
      await takeScreenshot(page, `04-${targetKey}-upload-success`);
    } catch (error) {
      console.error(`Upload failed or timed out: ${error.message}`);
      await takeScreenshot(page, `04-${targetKey}-upload-error`);
      
      // Check for error messages
      const errorText = await page.locator('.ant-message-error, .error-message, .alert-error').textContent();
      if (errorText) {
        console.error(`Error message: ${errorText}`);
      }
      
      throw new Error(`Upload failed: ${error.message}`);
    }
    
    // Step 5: Verify file in list (if applicable)
    if (target.filesUrl) {
      console.log(`Step 5: Verifying file in list at ${target.filesUrl}`);
      try {
        await page.goto(target.filesUrl, { timeout: config.timeouts.navigation });
        await page.waitForLoadState('networkidle');
        await takeScreenshot(page, `05-${targetKey}-files-list`);
        
        // Look for our file in the list
        const fileEntry = page.locator(`.ant-table-row:has-text("${description}"), tr:has-text("${description}")`);
        
        if (await fileEntry.count() > 0) {
          console.log('✓ File found in list');
          await fileEntry.highlight();
          await takeScreenshot(page, `05-${targetKey}-file-found`);
        } else {
          console.error('File not found in list');
          await takeScreenshot(page, `05-${targetKey}-file-not-found`);
          
          // Save HTML for debugging
          const html = await page.content();
          const debugPath = path.join(config.screenshotDir, `${targetKey}-files-page-html.txt`);
          fs.writeFileSync(debugPath, html);
          
          throw new Error('Uploaded file not found in files list');
        }
      } catch (error) {
        console.error(`File verification failed: ${error.message}`);
        throw new Error(`Failed to verify file: ${error.message}`);
      }
    } else {
      console.log('Files URL not configured, skipping verification');
    }
    
    console.log(`✅ File upload test against ${targetKey} completed successfully!`);
    return true;
  } catch (error) {
    console.error(`Test failed for ${targetKey}: ${error.message}`);
    return false;
  } finally {
    await browser.close();
  }
}

// Try each target in sequence until one succeeds
async function runAllTests() {
  const results = {};
  const targets = Object.keys(config.targets);
  let anySuccess = false;
  
  for (const target of targets) {
    if (!config.targets[target].baseUrl) {
      console.log(`Skipping ${target} (no URL configured)`);
      continue;
    }
    
    console.log(`\n======= Testing ${target} =======\n`);
    try {
      const success = await runFileUploadTest(target);
      results[target] = { success };
      if (success) anySuccess = true;
    } catch (error) {
      console.error(`Error in ${target} test:`, error);
      results[target] = { success: false, error: error.message };
    }
  }
  
  // Output summary
  console.log('\n======= TEST RESULTS SUMMARY =======');
  for (const [target, result] of Object.entries(results)) {
    console.log(`${target}: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
    if (!result.success && result.error) {
      console.log(`  Error: ${result.error}`);
    }
  }
  
  return anySuccess;
}

// Run the tests
console.log('=== ENHANCED FILE UPLOAD TEST ===');
runAllTests()
  .then(success => {
    if (success) {
      console.log('\n✅ At least one test passed successfully!');
      process.exit(0);
    } else {
      console.error('\n❌ All tests failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n❌ Unexpected error:', error);
    process.exit(1);
  }); 