#!/bin/bash
echo "Creating test file..."
echo "This is a test file for upload" > test-file.txt
echo "Testing API health..."
curl -v http://localhost:3050/api/v1/health/
echo -e "\n\nAttempting file upload..."
curl -v -X POST http://localhost:3050/api/v1/file-upload/ -F "file=@test-file.txt" -F "description=Test upload from script"
echo -e "\n\nAttempting file upload with alternate endpoint..."
curl -v -X POST http://localhost:3050/api/v1/file-upload/upload -F "file=@test-file.txt" -F "description=Test upload from script"
