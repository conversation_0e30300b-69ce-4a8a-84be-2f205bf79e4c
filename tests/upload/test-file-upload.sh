#!/bin/bash

# A simple script to test file upload functionality using curl
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing file upload with curl${NC}"

# Create a test file
TEMP_FILE="test-upload.txt"
echo "Test file for upload" > "$TEMP_FILE"
echo "Created at: $(date)" >> "$TEMP_FILE"
echo "Random ID: $(date +%s)" >> "$TEMP_FILE"
echo -e "${YELLOW}Created test file: ${TEMP_FILE}${NC}"

# Check if frontend is running
echo -e "${YELLOW}Checking if frontend is running...${NC}"
if ! curl -s http://localhost:3100 > /dev/null; then
  echo -e "${RED}Error: Frontend is not accessible at http://localhost:3100${NC}"
  exit 1
fi

# Try direct file upload using the API
echo -e "${YELLOW}Uploading file through API...${NC}"
RESPONSE=$(curl -s -F "file=@$TEMP_FILE" -F "description=API upload test" http://localhost:3050/api/v1/files)

if [[ $RESPONSE == *"id"* ]]; then
  FILE_ID=$(echo $RESPONSE | grep -o '"id":"[^"]*' | sed 's/"id":"//')
  echo -e "${GREEN}File uploaded successfully! File ID: ${FILE_ID}${NC}"
  
  # Verify file exists
  echo -e "${YELLOW}Verifying file exists in API...${NC}"
  if curl -s http://localhost:3050/api/v1/files | grep -q "$FILE_ID"; then
    echo -e "${GREEN}File verified in API!${NC}"
    echo -e "${GREEN}✅ File upload test passed!${NC}"
    exit 0
  else
    echo -e "${RED}Could not verify file in API${NC}"
    exit 1
  fi
else
  echo -e "${RED}File upload failed: ${RESPONSE}${NC}"
  exit 1
fi 