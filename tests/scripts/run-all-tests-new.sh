#!/bin/bash

# Script to run all tests without disturbing existing containers in .dockerwrapper/
# This script runs both Playwright UI tests and API tests

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}==================================================${NC}"
echo -e "${YELLOW}       Running Complete Test Suite                 ${NC}"
echo -e "${YELLOW}==================================================${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Error: Docker is not running.${NC}"
  exit 1
fi

# Function to run UI tests using the existing Playwright container
run_ui_tests() {
  echo -e "${YELLOW}Running UI tests...${NC}"
  
  # List all UI test files
  UI_TESTS=(
    "tests/accessibility.spec.js"
    "tests/file-upload-e2e.test.js"
    "tests/navigation-flow.spec.js"
    "tests/performance.spec.js"
    "tests/security.spec.js"
    "tests/form-inputs.spec.js"
    "tests/vm-operations.spec.js"
  )
  
  # Make sure test directories exist in the container
  echo -e "${YELLOW}Setting up test directories in container...${NC}"
  docker exec turdparty_playwright mkdir -p /app/test_screenshots /app/test_results/performance /app/playwright-report /app/test-results
  
  # Copy all test files to the container
  echo -e "${YELLOW}Copying test files to container...${NC}"
  for test in "${UI_TESTS[@]}"; do
    echo -e "${YELLOW}Copying ${test}...${NC}"
    dir=$(dirname "/app/${test}")
    docker exec turdparty_playwright mkdir -p "${dir}"
    docker cp "${test}" "turdparty_playwright:${dir}/"
  done
  
  # Run each UI test
  TOTAL_TESTS=${#UI_TESTS[@]}
  PASSED_TESTS=0
  FAILED_TESTS=0
  
  for test in "${UI_TESTS[@]}"; do
    echo -e "${YELLOW}Running test: ${test}${NC}"
    docker exec turdparty_playwright npx playwright test "/app/${test}" --reporter=html || {
      echo -e "${RED}Test failed: ${test}${NC}"
      FAILED_TESTS=$((FAILED_TESTS+1))
    }
    if [ $? -eq 0 ]; then
      PASSED_TESTS=$((PASSED_TESTS+1))
    fi
  done
  
  # Copy test reports from container
  echo -e "${YELLOW}Copying UI test reports from container...${NC}"
  docker cp turdparty_playwright:/app/playwright-report ./ui-test-report
  
  echo -e "${GREEN}UI Tests completed: ${PASSED_TESTS} passed, ${FAILED_TESTS} failed out of ${TOTAL_TESTS}${NC}"
}

# Function to run API integration tests
run_api_tests() {
  echo -e "${YELLOW}Setting up API test environment...${NC}"
  
  # Create Docker network if it doesn't exist
  docker network create test-network 2>/dev/null || true
  
  # Stop and remove old containers if they exist
  docker stop api-test 2>/dev/null || true
  docker rm api-test 2>/dev/null || true
  
  # Build API test server
  echo -e "${YELLOW}Building API test server...${NC}"
  docker build -t api-test-server -f Dockerfile.simple .
  
  # Start API container
  echo -e "${YELLOW}Starting API container...${NC}"
  docker run -d -p 3055:8000 --name api-test --network test-network api-test-server
  
  # Wait for API to start
  echo -e "${YELLOW}Waiting for API to start...${NC}"
  sleep 3
  
  # Check if API is running
  if ! docker ps | grep -q "api-test"; then
    echo -e "${RED}Error: API container failed to start.${NC}"
    return 1
  fi
  
  # Connect Playwright container to network
  echo -e "${YELLOW}Connecting Playwright container to network...${NC}"
  docker network connect test-network turdparty_playwright 2>/dev/null || true
  
  # List all API test files
  API_TESTS=(
    "tests/playwright/api-health.spec.js"
    "tests/playwright/api-endpoints.spec.js"
    "tests/playwright/error-handling.spec.js"
    "tests/playwright/file-management.spec.js"
    "tests/playwright/system-settings.spec.js"
    "tests/playwright/api-connectivity.spec.js"
  )
  
  # Copy API test files to the container
  echo -e "${YELLOW}Copying API test files to container...${NC}"
  for test in "${API_TESTS[@]}"; do
    echo -e "${YELLOW}Copying ${test}...${NC}"
    dir=$(dirname "/app/${test}")
    docker exec turdparty_playwright mkdir -p "${dir}"
    docker cp "${test}" "turdparty_playwright:${dir}/"
  done
  
  # Copy config files
  echo -e "${YELLOW}Copying test config files...${NC}"
  docker cp "tests/playwright/integration.config.js" "turdparty_playwright:/app/tests/playwright/"
  docker cp "tests/playwright/playwright.config.js" "turdparty_playwright:/app/tests/playwright/"
  
  # Run each API test
  TOTAL_API_TESTS=${#API_TESTS[@]}
  PASSED_API_TESTS=0
  FAILED_API_TESTS=0
  
  for test in "${API_TESTS[@]}"; do
    echo -e "${YELLOW}Running API test: ${test}${NC}"
    docker exec -e API_URL=http://api-test:8000 turdparty_playwright npx playwright test "/app/${test}" --config=/app/tests/playwright/integration.config.js --reporter=html || {
      echo -e "${RED}API test failed: ${test}${NC}"
      FAILED_API_TESTS=$((FAILED_API_TESTS+1))
    }
    if [ $? -eq 0 ]; then
      PASSED_API_TESTS=$((PASSED_API_TESTS+1))
    fi
  done
  
  # Copy test reports from container
  echo -e "${YELLOW}Copying API test reports from container...${NC}"
  docker cp turdparty_playwright:/app/playwright-report ./api-test-report
  
  # Clean up
  echo -e "${YELLOW}Cleaning up API test environment...${NC}"
  docker stop api-test 2>/dev/null || true
  docker rm api-test 2>/dev/null || true
  
  echo -e "${GREEN}API Tests completed: ${PASSED_API_TESTS} passed, ${FAILED_API_TESTS} failed out of ${TOTAL_API_TESTS}${NC}"
}

# Run both test suites
run_ui_tests
run_api_tests

# Generate a combined summary report
echo -e "${YELLOW}==================================================${NC}"
echo -e "${YELLOW}             TEST SUMMARY                         ${NC}"
echo -e "${YELLOW}==================================================${NC}"
echo -e "${GREEN}UI Tests: ${PASSED_TESTS}/${TOTAL_TESTS} passed${NC}"
echo -e "${GREEN}API Tests: ${PASSED_API_TESTS}/${TOTAL_API_TESTS} passed${NC}"
echo -e "${GREEN}Total: $((PASSED_TESTS + PASSED_API_TESTS))/$((TOTAL_TESTS + TOTAL_API_TESTS)) passed${NC}"
echo -e "${YELLOW}==================================================${NC}"
echo -e "${GREEN}UI Test report: ${PWD}/ui-test-report/index.html${NC}"
echo -e "${GREEN}API Test report: ${PWD}/api-test-report/index.html${NC}"

# Create a simple HTML summary of all test results
cat > all-test-summary.html << EOF
<!DOCTYPE html>
<html>
<head>
  <title>All Test Results</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    .summary { margin: 20px 0; padding: 10px; background-color: #f0f0f0; border-radius: 5px; }
    .passed { color: green; }
    .failed { color: red; }
    .links { margin: 20px 0; }
    .links a { display: block; margin: 10px 0; }
  </style>
</head>
<body>
  <h1>Test Results Summary</h1>
  <div class="summary">
    <h2>UI Tests</h2>
    <p class="passed">Passed: ${PASSED_TESTS}</p>
    <p class="failed">Failed: ${FAILED_TESTS}</p>
    <p>Total: ${TOTAL_TESTS}</p>
  </div>
  <div class="summary">
    <h2>API Tests</h2>
    <p class="passed">Passed: ${PASSED_API_TESTS}</p>
    <p class="failed">Failed: ${FAILED_API_TESTS}</p>
    <p>Total: ${TOTAL_API_TESTS}</p>
  </div>
  <div class="summary">
    <h2>Overall</h2>
    <p class="passed">Total Passed: $((PASSED_TESTS + PASSED_API_TESTS))</p>
    <p class="failed">Total Failed: $((FAILED_TESTS + FAILED_API_TESTS))</p>
    <p>Grand Total: $((TOTAL_TESTS + TOTAL_API_TESTS))</p>
  </div>
  <div class="links">
    <h2>Detailed Reports</h2>
    <a href="./ui-test-report/index.html">UI Test Detailed Report</a>
    <a href="./api-test-report/index.html">API Test Detailed Report</a>
  </div>
</body>
</html>
EOF

echo -e "${GREEN}Complete test summary: ${PWD}/all-test-summary.html${NC}" 