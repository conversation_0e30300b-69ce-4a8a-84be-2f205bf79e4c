#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to ensure containers are running
ensure_containers() {
    echo -e "${YELLOW}Ensuring test environment is running...${NC}"
    
    # Check if containers exist and are running
    if ! docker ps -q -f name=turdparty_test_runner_test >/dev/null; then
        echo "Starting test environment..."
        docker compose -f docker-compose.testing.yml up -d
        
        # Wait for services to be ready
        echo "Waiting for services to be ready..."
        sleep 5
    fi
}

# Function to run a specific test
run_test() {
    local test_file=$1
    echo -e "${YELLOW}Running test: ${test_file}${NC}"
    
    # Run the test in the existing container with markers to skip tests requiring external services
    docker exec -it turdparty_test_runner_test python -m pytest "$test_file" -v \
        -k "not requires_ssh and not requires_vagrant and not requires_playwright" \
        --no-header --tb=native
    
    # Check exit code
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Test passed: ${test_file}${NC}"
        return 0
    else
        echo -e "${RED}Test failed: ${test_file}${NC}"
        return 1
    fi
}

# Function to list available tests
list_tests() {
    echo -e "${YELLOW}Finding test files...${NC}"
    # Only include specific test directories and exclude support modules
    docker exec turdparty_test_runner_test find /app -name "test_*.py" -type f \
        -not -path "*/\.focus_forge/*" \
        -not -path "*/.pip_modules/*" \
        -not -path "*/node_modules/*" \
        -not -path "*/api/core/*" \
        -not -path "*/api/db/*" \
        | grep -v "__pycache__" \
        | sort
}

# Function to run pytest with debugger
debug_test() {
    local test_file=$1
    echo -e "${YELLOW}Debugging test: ${test_file}${NC}"
    docker exec -it turdparty_test_runner_test python -m pytest "$test_file" -v --pdb
}

# Function to show test environment status
show_status() {
    echo -e "${YELLOW}Test Environment Status:${NC}"
    echo
    echo "Containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "turdparty_"
    echo
    echo "Python packages:"
    docker exec turdparty_test_runner_test pip list
    echo
    echo "Environment variables:"
    docker exec turdparty_test_runner_test env | grep -E "PYTHON|MINIO|TEST"
}

# Function to rebuild the environment
rebuild_env() {
    echo -e "${YELLOW}Rebuilding test environment...${NC}"
    docker compose -f docker-compose.testing.yml down -v
    docker compose -f docker-compose.testing.yml build
    docker compose -f docker-compose.testing.yml up -d
    echo -e "${GREEN}Test environment rebuilt!${NC}"
}

# Function to install additional packages
install_package() {
    local package=$1
    echo -e "${YELLOW}Installing package: ${package}${NC}"
    docker exec turdparty_test_runner_test pip install "$package"
}

# Show help
show_help() {
    echo "Usage: $0 [command] [arguments]"
    echo
    echo "Commands:"
    echo "  run [test_file]     Run a specific test file or all tests if no file specified"
    echo "  debug [test_file]   Run a test file with debugger"
    echo "  list               List all available tests"
    echo "  status             Show test environment status"
    echo "  rebuild            Rebuild the test environment"
    echo "  install [package]  Install a Python package in the test environment"
    echo "  help              Show this help message"
    echo
    echo "Examples:"
    echo "  $0 run                          # Run all tests"
    echo "  $0 run /app/test_minio.py       # Run specific test"
    echo "  $0 debug /app/test_minio.py     # Debug specific test"
    echo "  $0 install pytest-xdist         # Install additional package"
}

# Ensure containers are running first
ensure_containers

# Parse command line arguments
case "${1:-run}" in
    "run")
        if [ -n "$2" ]; then
            run_test "$2"
        else
            # Run all tests
            TEST_FILES=$(list_tests)
            PASS_COUNT=0
            FAIL_COUNT=0
            
            echo -e "${YELLOW}Running all tests:${NC}"
            echo "$TEST_FILES"
            echo
            
            while IFS= read -r test_file; do
                run_test "$test_file"
                if [ $? -eq 0 ]; then
                    ((PASS_COUNT++))
                else
                    ((FAIL_COUNT++))
                fi
            done <<< "$TEST_FILES"
            
            echo
            echo -e "${GREEN}Tests passed: ${PASS_COUNT}${NC}"
            echo -e "${RED}Tests failed: ${FAIL_COUNT}${NC}"
            echo -e "Total tests: $((PASS_COUNT + FAIL_COUNT))"
        fi
        ;;
        
    "debug")
        if [ -n "$2" ]; then
            debug_test "$2"
        else
            echo "Please specify a test file to debug"
            exit 1
        fi
        ;;
        
    "list")
        list_tests
        ;;
        
    "status")
        show_status
        ;;
        
    "rebuild")
        rebuild_env
        ;;
        
    "install")
        if [ -n "$2" ]; then
            install_package "$2"
        else
            echo "Please specify a package to install"
            exit 1
        fi
        ;;
        
    "help")
        show_help
        ;;
        
    *)
        echo "Unknown command: $1"
        show_help
        exit 1
        ;;
esac 