#!/bin/bash
# <PERSON>ript to run integration tests in parallel

# Default values
PARALLEL=4
COVERAGE=false
TEST_PATH="api/tests/test_integration"
VERBOSE=false
MARKERS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --parallel)
      PARALLEL="$2"
      shift 2
      ;;
    --coverage)
      COVERAGE=true
      shift
      ;;
    --path)
      TEST_PATH="$2"
      shift 2
      ;;
    --verbose|-v)
      VERBOSE=true
      shift
      ;;
    --markers)
      MARKERS="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--parallel N] [--coverage] [--path PATH] [--verbose] [--markers MARKERS]"
      exit 1
      ;;
  esac
done

# Ensure the test directory exists
if [ ! -d "$TEST_PATH" ]; then
  echo "Test directory $TEST_PATH does not exist"
  exit 1
fi

# Build the pytest command
PYTEST_CMD="python -m pytest"

# Add verbosity if requested
if [ "$VERBOSE" = true ]; then
  PYTEST_CMD="$PYTEST_CMD -v"
fi

# Add markers if specified
if [ -n "$MARKERS" ]; then
  PYTEST_CMD="$PYTEST_CMD -m \"$MARKERS\""
fi

# Add coverage if requested
if [ "$COVERAGE" = true ]; then
  PYTEST_CMD="$PYTEST_CMD --cov=api --cov-report=term"
fi

# Add parallel execution
PYTEST_CMD="$PYTEST_CMD -n $PARALLEL"

# Add the test path
PYTEST_CMD="$PYTEST_CMD $TEST_PATH"

# Print the command
echo "Running: $PYTEST_CMD"

# Execute the command
eval $PYTEST_CMD

# Check the exit code
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "Integration tests passed successfully"
else
  echo "Integration tests failed with exit code $EXIT_CODE"
fi

exit $EXIT_CODE
