#!/bin/bash

# Run All Tests Script
# This script runs all the Playwright tests with proper permissions

# Ensure the script exits on error
set -e

# Create required directories with proper permissions
echo "Creating required directories..."
mkdir -p test_screenshots test_results/performance playwright-report test-results

# Set proper permissions - without using sudo
echo "Setting proper permissions..."
# Skip chmod step and just warn if directories might not be writable
echo "Warning: Running without sudo. If tests fail due to permission issues, you may need to manually set permissions."

# Run tests using nix-shell to ensure dependencies are available
echo "Running tests..."
nix-shell -p "docker compose" --command "cd .dockerwrapper && docker compose -f docker-compose.playwright.yml up -d"

echo "Wait for containers to start..."
sleep 5

# Make sure the test directories exist inside the container
echo "Setting up test directories in container..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright mkdir -p /app/test_screenshots /app/test_results/performance /app/playwright-report /app/test-results"

# Copy the new test files into the container
echo "Copying test files into container..."
nix-shell -p "docker compose" --command "docker cp tests/accessibility.spec.js turdparty_playwright:/app/tests/"
nix-shell -p "docker compose" --command "docker cp tests/file-upload-e2e.test.js turdparty_playwright:/app/tests/"
nix-shell -p "docker compose" --command "docker cp tests/navigation-flow.spec.js turdparty_playwright:/app/tests/"
nix-shell -p "docker compose" --command "docker cp tests/performance.spec.js turdparty_playwright:/app/tests/"
nix-shell -p "docker compose" --command "docker cp tests/security.spec.js turdparty_playwright:/app/tests/"
nix-shell -p "docker compose" --command "docker cp tests/form-inputs.spec.js turdparty_playwright:/app/tests/"
nix-shell -p "docker compose" --command "docker cp tests/vm-operations.spec.js turdparty_playwright:/app/tests/"

echo "Running accessibility tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/accessibility.spec.js --reporter=html" || echo "Some accessibility tests failed, continuing..."

echo "Running file upload tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/file-upload-e2e.test.js --reporter=html" || echo "Some file upload tests failed, continuing..."

echo "Running navigation flow tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/navigation-flow.spec.js --reporter=html" || echo "Some navigation flow tests failed, continuing..."

echo "Running form validation tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/form-inputs.spec.js --reporter=html" || echo "Some form validation tests failed, continuing..."

echo "Running VM operations tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/vm-operations.spec.js --reporter=html" || echo "Some VM operations tests failed, continuing..."

echo "Running performance tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/performance.spec.js --reporter=html" || echo "Some performance tests failed, continuing..."

echo "Running security tests..."
nix-shell -p "docker compose" --command "docker exec turdparty_playwright npx playwright test tests/security.spec.js --reporter=html" || echo "Some security tests failed, continuing..."

echo "Copying test reports from container..."
nix-shell -p "docker compose" --command "docker cp turdparty_playwright:/app/playwright-report ."

echo "Stopping containers..."
nix-shell -p "docker compose" --command "cd .dockerwrapper && docker compose -f docker-compose.playwright.yml down"

echo "All tests completed! Check the playwright-report directory for test results."
echo "Some tests may have failed, but the suite ran to completion."

# Generate a simple test summary
echo "Generating test summary..."
if [ -f "playwright-report/index.html" ]; then
  echo "Test report available at: $PWD/playwright-report/index.html"
  
  # Try to extract some basic stats if possible
  PASSED_COUNT=$(grep -c "status-passed" playwright-report/index.html || echo "Unknown")
  FAILED_COUNT=$(grep -c "status-failed" playwright-report/index.html || echo "Unknown")
  
  echo "-------------------------------------------"
  echo "TESTS SUMMARY"
  echo "-------------------------------------------"
  echo "Tests passed: $PASSED_COUNT"
  echo "Tests failed: $FAILED_COUNT"
  echo "-------------------------------------------"
  echo "See full report for details"
else
  echo "No test report found. Tests may have failed to run."
fi 