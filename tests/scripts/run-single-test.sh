#!/bin/bash

# <PERSON>ript to run a single test directly

# Set the test file to run (default to accessibility.spec.js)
TEST_FILE=${1:-"accessibility.spec.js"}
echo "Running test: $TEST_FILE"

# Create a unique container name
CONTAINER_NAME="turdparty_test_runner_$(date +%s)"
echo "Container name: $CONTAINER_NAME"

# Run the test in a new container
docker run --rm \
  --network=dockerwrapper_turdparty_network \
  -v "$(pwd)/tests:/app/tests" \
  -v "$(pwd)/playwright-report:/app/playwright-report" \
  -v "$(pwd)/test-results:/app/test-results" \
  -v "$(pwd)/test_screenshots:/app/test_screenshots" \
  -v "$(pwd)/playwright.config.js:/app/playwright.config.js" \
  --name $CONTAINER_NAME \
  mcr.microsoft.com/playwright:v1.39.0-focal \
  bash -c "
    mkdir -p /app/tests /app/test_screenshots /app/test-results /app/playwright-report && 
    cd /app && 
    npm init -y && 
    npm install @playwright/test @axe-core/playwright && 
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright FRONTEND_URL=http://turdparty_frontend:3100/ui API_URL=http://turdparty_api:3050 \
    npx playwright test tests/$TEST_FILE --config=playwright.config.js
  "

RESULT=$?
if [ $RESULT -eq 0 ]; then
  echo "✅ Test passed: $TEST_FILE"
else
  echo "❌ Test failed: $TEST_FILE"
fi

exit $RESULT 