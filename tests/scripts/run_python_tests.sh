#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/python_test_run_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Keep track of test statistics
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

section "PYTHON TEST SUITE"
log "Starting Python test run at $(date)"

# Find all Python test files
PYTHON_TEST_FILES=(
    $(find . -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__" | grep -v ".venv" | grep -v "tests/playwright")
)

log "Found ${#PYTHON_TEST_FILES[@]} Python test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#PYTHON_TEST_FILES[@]}))

# Run each test file
for TEST_FILE in "${PYTHON_TEST_FILES[@]}"; do
    log "Running test file: $TEST_FILE"
    
    # Run the test with pytest
    PYTHONPATH="." python3 -m pytest $TEST_FILE -v
    
    # Check result
    if [ $? -eq 0 ]; then
        success "Test passed: $TEST_FILE"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        error "Test failed: $TEST_FILE"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
done

# Summary
section "TEST SUMMARY"
log "Completed test run at $(date)"
log "Total tests: $TOTAL_TESTS"
success "Tests passed: $PASSED_TESTS"
if [ $FAILED_TESTS -gt 0 ]; then
    error "Tests failed: $FAILED_TESTS"
fi
if [ $SKIPPED_TESTS -gt 0 ]; then
    log "Tests skipped: $SKIPPED_TESTS"
fi

# Exit with success if all tests passed, failure otherwise
if [ $FAILED_TESTS -eq 0 ]; then
    success "All tests passed!"
    exit 0
else
    error "Some tests failed."
    exit 1
fi 