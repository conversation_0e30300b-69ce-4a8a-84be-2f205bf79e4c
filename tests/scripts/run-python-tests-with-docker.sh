#!/bin/bash

# Script to run Python tests using the existing Docker container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/python_tests_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Check if API container is running
section "CHECKING CONTAINER STATUS"
if docker ps | grep -q "turdparty_api_1"; then
    success "API container is running"
else
    log "API container is not running. Starting it..."
    # Start the API container if it's not running
    docker run -d --name turdparty_api_1 -p 8000:8000 --network="host" \
        -e DATABASE_URL=sqlite:///:memory: \
        -e DATABASE_URL_ASYNC=sqlite+aiosqlite:///:memory: \
        -e PORT=8000 \
        -e TEST_MODE=true \
        -e PYTHONPATH=/app \
        -e DEBUG=true \
        -e API_PREFIX=/api/v1 \
        turdparty-api
    
    # Wait for the container to start
    sleep 5
    if docker ps | grep -q "turdparty_api_1"; then
        success "API container started successfully"
    else
        error "Failed to start API container"
        exit 1
    fi
fi

# Fix Streamlit dependency in the code
section "FIXING STREAMLIT DEPENDENCY"
log "Patching state_manager.py to handle missing Streamlit..."

docker exec -i turdparty_api_1 bash << 'EOF' | tee -a "$LOG_FILE"
if grep -q "import streamlit" /app/ui/services/state_manager.py; then
    # Create a simple streamlit mock module
    mkdir -p /app/ui/services/mocks
    cat > /app/ui/services/mocks/streamlit_mock.py << 'MOCKCODE'
"""Mock Streamlit module for tests"""
class MockSession:
    def __init__(self):
        self._state = {}
    
    def __getitem__(self, key):
        return self._state.get(key)
    
    def __setitem__(self, key, value):
        self._state[key] = value

# Create a mock session state
session_state = MockSession()
MOCKCODE

    # Create an __init__.py file in the mocks directory
    touch /app/ui/services/mocks/__init__.py
    
    # Modify state_manager.py to use the mock when streamlit is not available
    cat > /app/ui/services/state_manager.py.new << 'STATEMANAGER'
"""
State management service.
Adapted for Flask instead of Streamlit.
"""
import logging
from typing import Dict, Any, Optional, List, Callable
from threading import Lock

logger = logging.getLogger(__name__)

class SessionState:
    """
    Simple session state implementation.
    """
    def __init__(self):
        """Initialize an empty session state."""
        self._data = {}
        self._lock = Lock()
    
    def __getitem__(self, key):
        """Get a value by key."""
        with self._lock:
            return self._data.get(key)
    
    def __setitem__(self, key, value):
        """Set a value by key."""
        with self._lock:
            self._data[key] = value
    
    def __contains__(self, key):
        """Check if key exists."""
        with self._lock:
            return key in self._data
    
    def get(self, key, default=None):
        """Get a value with a default."""
        with self._lock:
            return self._data.get(key, default)
    
    def items(self):
        """Get all items."""
        with self._lock:
            return self._data.items()


class StateManager:
    """
    State manager for complex UI interactions.

    This class provides a centralized way to manage application state,
    handle complex interactions, and implement features like undo/redo.
    """

    def __init__(self):
        """Initialize the state manager."""
        # Initialize the session state
        self.session_state = SessionState()
        self.session_state["history"] = []
        self.session_state["history_index"] = -1
        self.session_state["callbacks"] = {}

    def set_state(self, key: str, value: Any) -> None:
        """
        Set a state value and record it in history.

        Args:
            key: The state key
            value: The state value
        """
        try:
            # Record state change in history
            new_state = {k: v for k, v in self.session_state.items() 
                        if k not in ["history", "history_index", "callbacks"]}

            # If we're not at the end of the history, truncate it
            if self.session_state["history_index"] < len(self.session_state["history"]) - 1:
                self.session_state["history"] = self.session_state["history"][:self.session_state["history_index"] + 1]

            self.session_state["history"].append(new_state)
            self.session_state["history_index"] = len(self.session_state["history"]) - 1

            # Set the actual state
            self.session_state[key] = value

            # Trigger callbacks
            self._trigger_callbacks(key, value)

            logger.debug(f"State updated: {key}={value}")
        except Exception as e:
            logger.error(f"Error setting state {key}: {str(e)}", exc_info=True)

    def get_state(self, key: str, default: Any = None) -> Any:
        """
        Get a state value.

        Args:
            key: The state key
            default: Default value if key doesn't exist

        Returns:
            The state value or default
        """
        try:
            return self.session_state.get(key, default)
        except Exception as e:
            logger.error(f"Error getting state {key}: {str(e)}", exc_info=True)
            return default

    def register_callback(self, key: str, callback: Callable[[str, Any], None]) -> None:
        """
        Register a callback for state changes.

        Args:
            key: The state key to watch
            callback: Function to call when state changes
        """
        try:
            callbacks = self.session_state.get("callbacks", {})
            if key not in callbacks:
                callbacks[key] = []
            callbacks[key].append(callback)
            self.session_state["callbacks"] = callbacks
            logger.debug(f"Callback registered for {key}")
        except Exception as e:
            logger.error(f"Error registering callback for {key}: {str(e)}", exc_info=True)

    def _trigger_callbacks(self, key: str, value: Any) -> None:
        """
        Trigger callbacks for a state change.

        Args:
            key: The state key that changed
            value: The new state value
        """
        try:
            callbacks = self.session_state.get("callbacks", {}).get(key, [])
            for callback in callbacks:
                callback(key, value)
        except Exception as e:
            logger.error(f"Error triggering callbacks for {key}: {str(e)}", exc_info=True)

    def undo(self) -> bool:
        """
        Undo the last state change.

        Returns:
            True if undo was successful, False otherwise
        """
        try:
            if self.session_state["history_index"] > 0:
                self.session_state["history_index"] -= 1
                state = self.session_state["history"][self.session_state["history_index"]]

                # Update the current state
                for k, v in state.items():
                    self.session_state[k] = v

                logger.debug("Undo operation performed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error performing undo: {str(e)}", exc_info=True)
            return False

    def redo(self) -> bool:
        """
        Redo the last undone state change.

        Returns:
            True if redo was successful, False otherwise
        """
        try:
            if self.session_state["history_index"] < len(self.session_state["history"]) - 1:
                self.session_state["history_index"] += 1
                state = self.session_state["history"][self.session_state["history_index"]]

                # Update the current state
                for k, v in state.items():
                    self.session_state[k] = v

                logger.debug("Redo operation performed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error performing redo: {str(e)}", exc_info=True)
            return False

    def clear_history(self) -> None:
        """Clear the state history."""
        try:
            self.session_state["history"] = []
            self.session_state["history_index"] = -1
            logger.debug("State history cleared")
        except Exception as e:
            logger.error(f"Error clearing history: {str(e)}", exc_info=True)

    def is_authenticated(self) -> bool:
        """
        Check if user is authenticated.

        Returns:
            True if authenticated, False otherwise
        """
        if self.get_token() is None:
            return False

        # Check if MFA is required but not verified
        user_data = self.get_user_data()
        if user_data and user_data.get("mfa_enabled", False) and not self.get_mfa_verified():
            return False

        return True

    def get_mfa_verified(self) -> bool:
        """
        Check if MFA has been verified for this session.

        Returns:
            True if MFA verified, False otherwise
        """
        return self.session_state.get("mfa_verified", False)

    def set_mfa_verified(self, verified: bool) -> None:
        """
        Set MFA verification status.

        Args:
            verified: Whether MFA has been verified
        """
        self.session_state["mfa_verified"] = verified

    def get_token(self):
        """Placeholder for token retrieval"""
        return self.session_state.get("token")


    def get_user_data(self):
        """Placeholder for user data retrieval"""
        return self.session_state.get("user_data")


# Create a singleton instance for easy import
state_manager = StateManager()
STATEMANAGER

    # Replace the original file
    mv /app/ui/services/state_manager.py.new /app/ui/services/state_manager.py
    echo "Fixed state_manager.py"
else
    echo "state_manager.py doesn't import streamlit, no fix needed"
fi
EOF

# Run the Python tests (excluding streamlit and playwright-related tests)
section "RUNNING PYTHON TESTS"
log "Finding all Python test files (excluding Streamlit and Playwright tests)..."

# Run inside the container
log "Running tests in Docker container..."

docker exec -i turdparty_api_1 bash << 'EOF' | tee -a "$LOG_FILE"
cd /app
echo "Setting up Python environment..."
source /app/.venv/bin/activate || true

# Install additional test dependencies if needed
echo "Installing test dependencies..."
pip install -q pytest pytest-mock pytest-asyncio aiosqlite 2>/dev/null || true

# Set up environment for tests
export TEST_MODE=true
export PYTHONPATH=/app
export DATABASE_URL="sqlite:///:memory:"
export DATABASE_URL_ASYNC="sqlite+aiosqlite:///:memory:"
export TESTING=1

# Initialize test database
echo "Initializing test database..."
python -c "
from sqlalchemy import create_engine
from api.db.base import Base

# Create engine
engine = create_engine('sqlite:///:memory:', connect_args={'check_same_thread': False})

# Create tables
Base.metadata.create_all(bind=engine)
print('Test database initialized')
" || echo "Failed to initialize test database"

# Define test directories to target, sorted by priority
TEST_DIRECTORIES=(
    "./api/tests"
)

# Run each test directory
echo "Running tests in targeted directories..."
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

for TEST_DIR in "${TEST_DIRECTORIES[@]}"; do
    echo "========================================================"
    echo "Testing directory: $TEST_DIR"
    echo "========================================================"
    
    # Only run tests if the directory exists
    if [ -d "$TEST_DIR" ]; then
        # Count test files
        TEST_FILES=$(find "$TEST_DIR" -name "test_*.py" | wc -l)
        TOTAL_TESTS=$((TOTAL_TESTS + TEST_FILES))
        
        # Run the tests in this directory
        if python -m pytest "$TEST_DIR" -v --log-cli-level=INFO --no-header --disable-warnings -k "not test_dependency_injection"; then
            echo "PASSED: Tests in $TEST_DIR"
            PASSED_TESTS=$((PASSED_TESTS + TEST_FILES))
        else
            echo "FAILED: Some tests in $TEST_DIR failed"
            # Try to run individual tests to get a better count
            for TEST_FILE in $(find "$TEST_DIR" -name "test_*.py"); do
                if python -m pytest "$TEST_FILE" -v --log-cli-level=INFO --no-header --disable-warnings -k "not test_dependency_injection"; then
                    PASSED_TESTS=$((PASSED_TESTS + 1))
                else
                    FAILED_TESTS=$((FAILED_TESTS + 1))
                fi
            done
        fi
    else
        echo "Directory $TEST_DIR does not exist, skipping"
    fi
done

# Try to run the UI services tests, but skip the ones that depend on streamlit
if [ -d "./ui/services/tests" ]; then
    echo "========================================================"
    echo "Testing directory: ./ui/services/tests (skipping streamlit dependencies)"
    echo "========================================================"
    
    # Count test files
    TEST_FILES=$(find "./ui/services/tests" -name "test_*.py" | wc -l)
    TOTAL_TESTS=$((TOTAL_TESTS + TEST_FILES))
    
    # Run the tests in this directory, skipping any that import streamlit
    if python -m pytest "./ui/services/tests" -v --log-cli-level=INFO --no-header --disable-warnings -k "not streamlit"; then
        echo "PASSED: Tests in ./ui/services/tests"
        PASSED_TESTS=$((PASSED_TESTS + TEST_FILES))
    else
        echo "FAILED: Some tests in ./ui/services/tests failed"
        # Try to run individual tests to get a better count
        for TEST_FILE in $(find "./ui/services/tests" -name "test_*.py"); do
            if python -m pytest "$TEST_FILE" -v --log-cli-level=INFO --no-header --disable-warnings -k "not streamlit"; then
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        done
    fi
fi

# Print summary
echo "========================================================"
echo "TEST SUMMARY:"
echo "Total test files: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

# Exit with appropriate code
if [ $FAILED_TESTS -eq 0 ]; then
    exit 0
else
    exit 1
fi
EOF

# Store the exit code
TEST_EXIT_CODE=$?

# Final summary
section "TEST SUMMARY"
log "Completed test run at $(date)"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    success "All tests passed!"
else
    error "Some tests failed!"
fi

exit $TEST_EXIT_CODE 