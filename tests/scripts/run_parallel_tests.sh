#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PARALLEL_WORKERS=4
COVERAGE=false
VERBOSE="-v"
TEST_PATH="api/tests"
FAIL_FAST=false
JUNIT_REPORT=false
REPORT_DIR="test-reports"

# Function to show help
show_help() {
    echo "Usage: $0 [options] [test_path]"
    echo
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -p, --parallel N           Run tests in parallel with N workers (default: 4)"
    echo "  -c, --coverage             Generate coverage reports"
    echo "  -v, --verbose              Run tests in verbose mode"
    echo "  -f, --fail-fast            Stop on first failure"
    echo "  -j, --junit                Generate JUnit XML reports"
    echo "  -r, --report-dir DIR       Directory for reports (default: test-reports)"
    echo
    echo "Examples:"
    echo "  $0 api/tests/schemas                # Run schema tests"
    echo "  $0 -p 8 -c api/tests               # Run all tests with 8 workers and coverage"
    echo "  $0 -c -j api/tests/test_unit       # Run unit tests with coverage and JUnit reports"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--parallel)
            PARALLEL_WORKERS="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE="-vv"
            shift
            ;;
        -f|--fail-fast)
            FAIL_FAST=true
            shift
            ;;
        -j|--junit)
            JUNIT_REPORT=true
            shift
            ;;
        -r|--report-dir)
            REPORT_DIR="$2"
            shift 2
            ;;
        *)
            TEST_PATH="$1"
            shift
            ;;
    esac
done

# Ensure test environment is running
echo -e "${YELLOW}Ensuring test environment is running...${NC}"
if ! docker ps -q -f name=turdparty_test_runner_test >/dev/null; then
    echo "Starting test environment..."
    docker compose -f docker-compose.testing.yml up -d

    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 5
fi

# Create report directory
mkdir -p "${REPORT_DIR}"
mkdir -p "${REPORT_DIR}/coverage"

# Build the pytest command
PYTEST_CMD="python -m pytest ${TEST_PATH} ${VERBOSE}"

# Add parallel execution
PYTEST_CMD="${PYTEST_CMD} -n ${PARALLEL_WORKERS}"

# Add fail-fast if requested
if [ "$FAIL_FAST" = true ]; then
    PYTEST_CMD="${PYTEST_CMD} -x"
fi

# Add JUnit report if requested
if [ "$JUNIT_REPORT" = true ]; then
    PYTEST_CMD="${PYTEST_CMD} --junitxml=${REPORT_DIR}/junit.xml"
fi

# Add coverage if requested
if [ "$COVERAGE" = true ]; then
    PYTEST_CMD="python -m coverage run --source=api --parallel-mode -m pytest ${TEST_PATH} ${VERBOSE} -n ${PARALLEL_WORKERS}"

    # Add fail-fast if requested
    if [ "$FAIL_FAST" = true ]; then
        PYTEST_CMD="${PYTEST_CMD} -x"
    fi

    # Add JUnit report if requested
    if [ "$JUNIT_REPORT" = true ]; then
        PYTEST_CMD="${PYTEST_CMD} --junitxml=${REPORT_DIR}/junit.xml"
    fi
fi

# Skip tests requiring external services or database
PYTEST_CMD="${PYTEST_CMD} -k \"not requires_ssh and not requires_vagrant and not requires_playwright\" -m \"no_db_required or schemas_only\""

# Run the tests
echo -e "${YELLOW}Running tests with command:${NC}"
echo "${PYTEST_CMD}"
echo

# Execute the command in the Docker container
docker exec -it turdparty_test_runner_test bash -c "${PYTEST_CMD}"
TEST_EXIT_CODE=$?

# If coverage was enabled, combine and generate reports
if [ "$COVERAGE" = true ]; then
    echo -e "${YELLOW}Combining coverage data and generating reports...${NC}"
    docker exec -it turdparty_test_runner_test bash -c "
        python -m coverage combine
        python -m coverage report
        python -m coverage xml -o ${REPORT_DIR}/coverage/coverage.xml
        python -m coverage json -o ${REPORT_DIR}/coverage/coverage.json
        python -m coverage html -d ${REPORT_DIR}/coverage/html
    "

    # Copy coverage reports from container to host
    echo -e "${YELLOW}Copying coverage reports from container...${NC}"
    docker cp turdparty_test_runner_test:/app/${REPORT_DIR}/coverage ./${REPORT_DIR}/
fi

# Display test results
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
else
    echo -e "${RED}Some tests failed with exit code ${TEST_EXIT_CODE}${NC}"
fi

# If JUnit report was generated, copy it from container to host
if [ "$JUNIT_REPORT" = true ]; then
    echo -e "${YELLOW}Copying JUnit report from container...${NC}"
    docker cp turdparty_test_runner_test:/app/${REPORT_DIR}/junit.xml ./${REPORT_DIR}/
fi

# Display coverage summary if available
if [ "$COVERAGE" = true ] && [ -f "${REPORT_DIR}/coverage/coverage.json" ]; then
    echo -e "${BLUE}Coverage Summary:${NC}"
    cat "${REPORT_DIR}/coverage/coverage.json" | grep -o '"percent_covered":[^,]*' | head -1
    echo -e "${BLUE}Detailed coverage report available at:${NC} ${REPORT_DIR}/coverage/html/index.html"
fi

exit $TEST_EXIT_CODE
