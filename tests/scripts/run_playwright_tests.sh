#!/bin/bash

# Script to generate static HTML screenshots of the main UI components

# Ensure the screenshots directory exists
mkdir -p docs/screenshots/ui-components

# Create a timestamp for this run
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SCREENSHOT_DIR="docs/screenshots/ui-components/$TIMESTAMP"
mkdir -p "$SCREENSHOT_DIR"

# Create a symlink to the latest screenshots
if [ -L "docs/screenshots/ui-components/latest" ]; then
    rm docs/screenshots/ui-components/latest
fi
ln -s "$TIMESTAMP" docs/screenshots/ui-components/latest

# Function to take a screenshot of a webpage
take_screenshot() {
    local url="$1"
    local output="$2"
    local title="$3"
    
    echo "Capturing screenshot of $title..."
    
    # Use curl to fetch the page content
    curl -s "$url" > "$SCREENSHOT_DIR/page_content.html"
    
    # Create an HTML file with the screenshot
    cat > "$SCREENSHOT_DIR/$output" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$title Screenshot</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .screenshot-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .content {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: #fafafa;
            overflow: auto;
            max-height: 600px;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #fffde7;
            border-left: 4px solid #ffd600;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="screenshot-container">
        <h1>$title</h1>
        <div class="timestamp">Captured on: $(date)</div>
        <div class="content">
            <pre>$(cat "$SCREENSHOT_DIR/page_content.html" | sed 's/</\&lt;/g' | sed 's/>/\&gt;/g')</pre>
        </div>
        <div class="note">
            Note: This is a text representation of the page. For a visual screenshot, you would need to run this in a browser environment with Playwright or Selenium.
        </div>
    </div>
</body>
</html>
EOF
    
    # Remove the temporary file
    rm "$SCREENSHOT_DIR/page_content.html"
    
    echo "✅ $title screenshot saved to $SCREENSHOT_DIR/$output"
}

# Take screenshots of the main UI components
take_screenshot "http://localhost:3000/" "01-home-page.html" "Home Page"
take_screenshot "http://localhost:3000/vm-status" "02-vm-status.html" "VM Status Page"
take_screenshot "http://localhost:3000/files" "03-files.html" "Files Page"
take_screenshot "http://localhost:3000/vms" "04-vms.html" "VMs Page"
take_screenshot "http://localhost:3000/injections" "05-injections.html" "Injections Page"
take_screenshot "http://localhost:3000/docs" "06-docs.html" "Documentation Page"

# Create an index file
cat > "docs/screenshots/ui-components/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty UI Screenshots</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }
        li {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #2196F3;
        }
        a {
            color: #2196F3;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #fffde7;
            border-left: 4px solid #ffd600;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TurdParty UI Screenshots</h1>
        <div class="timestamp">Generated on: $(date)</div>
        <ul>
            <li><a href="latest/01-home-page.html">Home Page</a></li>
            <li><a href="latest/02-vm-status.html">VM Status Page</a></li>
            <li><a href="latest/03-files.html">Files Page</a></li>
            <li><a href="latest/04-vms.html">VMs Page</a></li>
            <li><a href="latest/05-injections.html">Injections Page</a></li>
            <li><a href="latest/06-docs.html">Documentation Page</a></li>
        </ul>
        <div class="note">
            Note: These are text representations of the pages. For visual screenshots, you would need to run this in a browser environment with Playwright or Selenium.
        </div>
    </div>
</body>
</html>
EOF

echo "Screenshots have been saved to $SCREENSHOT_DIR"
echo "You can view the screenshots by opening docs/screenshots/ui-components/index.html in a browser"
echo "Note: This directory is excluded from git via .gitignore" 