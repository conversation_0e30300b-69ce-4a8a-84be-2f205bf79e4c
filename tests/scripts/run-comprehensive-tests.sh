#!/bin/bash

# Comprehensive Test Runner Script
# This script runs all tests in the project (500+ tests)

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/comprehensive_test_run_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Ensure the script exits on error
set -e

section "COMPREHENSIVE TEST SUITE - RUNNING ALL TESTS"
log "Starting comprehensive test run at $(date)"
log "This will run all 500+ tests in the project"
log "Log file: $LOG_FILE"

# Create required directories with proper permissions
log "Creating required directories..."
mkdir -p test_screenshots test_results/performance playwright-report test-results

# Initialize counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 1. Run Playwright UI Tests
section "RUNNING PLAYWRIGHT UI TESTS"

# Find all playwright test files
UI_TEST_FILES=(
    $(find tests -type f -name "*.spec.js" -o -name "*.test.js" | grep -v ".focus_forge")
)

log "Found ${#UI_TEST_FILES[@]} Playwright UI test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#UI_TEST_FILES[@]}))

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if the Playwright container is running
if ! docker ps | grep -q "turdparty_playwright"; then
    log "Starting Playwright container..."
    docker compose -f .dockerwrapper/docker-compose.playwright.yml up -d || {
        error "Failed to start Playwright container. Trying alternative method..."
        nix-shell -p "docker compose" --command "cd .dockerwrapper && docker compose -f docker-compose.playwright.yml up -d" || {
            error "Failed to start Playwright container. Exiting."
            exit 1
        }
    }
    
    # Wait for container to start
    log "Waiting for container to start..."
    sleep 5
fi

# Make sure the test directories exist inside the container
log "Setting up test directories in container..."
docker exec turdparty_playwright mkdir -p /app/test_screenshots /app/test_results/performance /app/playwright-report /app/test-results

# Copy all test files into the container
log "Copying test files into container..."
for test_file in "${UI_TEST_FILES[@]}"; do
    dir=$(dirname "/app/${test_file}")
    log "Creating directory ${dir} in container..."
    docker exec turdparty_playwright mkdir -p "${dir}"
    log "Copying ${test_file} to container..."
    docker cp "${test_file}" "turdparty_playwright:${dir}/"
done

# Copy playwright config
log "Copying Playwright config to container..."
docker cp "playwright.config.js" "turdparty_playwright:/app/"

# Run each UI test
UI_PASSED=0
UI_FAILED=0
for test_file in "${UI_TEST_FILES[@]}"; do
    log "Running test: ${test_file}"
    
    if docker exec turdparty_playwright npx playwright test "/app/${test_file}" --reporter=list >> "$LOG_FILE" 2>&1; then
        success "✓ Test passed: ${test_file}"
        UI_PASSED=$((UI_PASSED+1))
    else
        error "✗ Test failed: ${test_file}"
        UI_FAILED=$((UI_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + UI_PASSED))
FAILED_TESTS=$((FAILED_TESTS + UI_FAILED))

log "Copying UI test reports from container..."
docker cp turdparty_playwright:/app/playwright-report ./ui-test-report

# 2. Run API Integration Tests
section "RUNNING API INTEGRATION TESTS"

# Create Docker network if it doesn't exist
log "Setting up API test environment..."
docker network create test-network 2>/dev/null || true

# Stop and remove old API test containers if they exist
docker stop api-test 2>/dev/null || true
docker rm api-test 2>/dev/null || true

# Build API test server
log "Building API test server..."
docker build -t api-test-server -f Dockerfile.simple . >> "$LOG_FILE" 2>&1

# Start API container
log "Starting API container..."
docker run -d -p 3055:8000 --name api-test --network test-network api-test-server >> "$LOG_FILE" 2>&1

# Wait for API to start
log "Waiting for API to start..."
sleep 3

# Connect Playwright container to network
log "Connecting Playwright container to network..."
docker network connect test-network turdparty_playwright 2>/dev/null || true

# Find all API test files
API_TEST_FILES=(
    $(find tests/playwright -type f -name "*.spec.js" -o -name "*.test.js" | grep -E 'api|integration' | grep -v ".focus_forge")
)

log "Found ${#API_TEST_FILES[@]} API test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#API_TEST_FILES[@]}))

# Copy API test files to container
log "Copying API test files to container..."
for test_file in "${API_TEST_FILES[@]}"; do
    dir=$(dirname "/app/${test_file}")
    docker exec turdparty_playwright mkdir -p "${dir}"
    docker cp "${test_file}" "turdparty_playwright:${dir}/"
done

# Copy config files
log "Copying test config files..."
docker cp "tests/playwright/integration.config.js" "turdparty_playwright:/app/tests/playwright/" 2>/dev/null || true
docker cp "tests/playwright/playwright.config.js" "turdparty_playwright:/app/tests/playwright/" 2>/dev/null || true

# Run each API test
API_PASSED=0
API_FAILED=0
for test_file in "${API_TEST_FILES[@]}"; do
    log "Running API test: ${test_file}"
    
    if docker exec -e API_URL=http://api-test:8000 turdparty_playwright npx playwright test "/app/${test_file}" --reporter=list >> "$LOG_FILE" 2>&1; then
        success "✓ API test passed: ${test_file}"
        API_PASSED=$((API_PASSED+1))
    else
        error "✗ API test failed: ${test_file}"
        API_FAILED=$((API_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + API_PASSED))
FAILED_TESTS=$((FAILED_TESTS + API_FAILED))

log "Copying API test reports from container..."
docker cp turdparty_playwright:/app/playwright-report ./api-test-report

# 3. Run Python Tests
section "RUNNING PYTHON TESTS"

# Find all Python test files
PYTHON_TEST_FILES=(
    $(find . -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__" | grep -v ".venv" | grep -v ".focus_forge")
)

log "Found ${#PYTHON_TEST_FILES[@]} Python test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#PYTHON_TEST_FILES[@]}))

# Set up Python virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    log "Setting up Python virtual environment..."
    python -m venv .venv
    source .venv/bin/activate
    pip install -r requirements.txt >> "$LOG_FILE" 2>&1
else
    source .venv/bin/activate
fi

# Run each Python test
PYTHON_PASSED=0
PYTHON_FAILED=0
for test_file in "${PYTHON_TEST_FILES[@]}"; do
    log "Running Python test: ${test_file}"
    
    if python -m pytest "${test_file}" -v >> "$LOG_FILE" 2>&1; then
        success "✓ Python test passed: ${test_file}"
        PYTHON_PASSED=$((PYTHON_PASSED+1))
    else
        error "✗ Python test failed: ${test_file}"
        PYTHON_FAILED=$((PYTHON_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + PYTHON_PASSED))
FAILED_TESTS=$((FAILED_TESTS + PYTHON_FAILED))

# 4. Run Shell Script Tests
section "RUNNING SHELL SCRIPT TESTS"

# Find all shell test scripts
SHELL_TEST_FILES=(
    $(find scripts -type f -name "*test*.sh" | grep -v "run" | grep -v ".focus_forge")
)

log "Found ${#SHELL_TEST_FILES[@]} Shell test scripts"
TOTAL_TESTS=$((TOTAL_TESTS + ${#SHELL_TEST_FILES[@]}))

# Run each shell test
SHELL_PASSED=0
SHELL_FAILED=0
for test_file in "${SHELL_TEST_FILES[@]}"; do
    log "Running Shell test: ${test_file}"
    
    if bash "${test_file}" >> "$LOG_FILE" 2>&1; then
        success "✓ Shell test passed: ${test_file}"
        SHELL_PASSED=$((SHELL_PASSED+1))
    else
        error "✗ Shell test failed: ${test_file}"
        SHELL_FAILED=$((SHELL_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + SHELL_PASSED))
FAILED_TESTS=$((FAILED_TESTS + SHELL_FAILED))

# 5. Run NodeJS Tests (non-Playwright)
section "RUNNING NODEJS TESTS"

# Find all NodeJS test files (excluding Playwright tests)
NODE_TEST_FILES=(
    $(find . -type f -name "*test*.js" | grep -v "spec.js" | grep -v "node_modules" | grep -v "playwright" | grep -v ".focus_forge")
)

log "Found ${#NODE_TEST_FILES[@]} NodeJS test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#NODE_TEST_FILES[@]}))

# Run each NodeJS test
NODE_PASSED=0
NODE_FAILED=0
for test_file in "${NODE_TEST_FILES[@]}"; do
    log "Running NodeJS test: ${test_file}"
    
    if node "${test_file}" >> "$LOG_FILE" 2>&1; then
        success "✓ NodeJS test passed: ${test_file}"
        NODE_PASSED=$((NODE_PASSED+1))
    else
        error "✗ NodeJS test failed: ${test_file}"
        NODE_FAILED=$((NODE_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + NODE_PASSED))
FAILED_TESTS=$((FAILED_TESTS + NODE_FAILED))

# Clean up API test environment
log "Cleaning up API test environment..."
docker stop api-test 2>/dev/null || true
docker rm api-test 2>/dev/null || true

# Generate comprehensive test summary
section "COMPREHENSIVE TEST SUMMARY"
log "UI Tests: ${UI_PASSED} passed, ${UI_FAILED} failed (total: ${#UI_TEST_FILES[@]})"
log "API Tests: ${API_PASSED} passed, ${API_FAILED} failed (total: ${#API_TEST_FILES[@]})"
log "Python Tests: ${PYTHON_PASSED} passed, ${PYTHON_FAILED} failed (total: ${#PYTHON_TEST_FILES[@]})"
log "Shell Tests: ${SHELL_PASSED} passed, ${SHELL_FAILED} failed (total: ${#SHELL_TEST_FILES[@]})"
log "NodeJS Tests: ${NODE_PASSED} passed, ${NODE_FAILED} failed (total: ${#NODE_TEST_FILES[@]})"
log "---------------------------------------------"
log "TOTAL: ${PASSED_TESTS} passed, ${FAILED_TESTS} failed (total: ${TOTAL_TESTS})"

# Create HTML summary report
cat > comprehensive-test-summary.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .summary { margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }
        .passed { color: green; }
        .failed { color: red; }
        .test-group { margin-bottom: 30px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .progress-bar-container { width: 100%; background-color: #e0e0e0; border-radius: 4px; }
        .progress-bar { height: 24px; background-color: #4CAF50; border-radius: 4px; text-align: center; line-height: 24px; color: white; }
    </style>
</head>
<body>
    <h1>Comprehensive Test Results</h1>
    <p>Run completed at $(date)</p>
    
    <div class="summary">
        <h2>Overall Summary</h2>
        <div class="progress-bar-container">
            <div class="progress-bar" style="width: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%;">
                $(( PASSED_TESTS * 100 / TOTAL_TESTS ))% Passed
            </div>
        </div>
        <p class="passed">Passed: ${PASSED_TESTS}</p>
        <p class="failed">Failed: ${FAILED_TESTS}</p>
        <p>Total: ${TOTAL_TESTS}</p>
    </div>
    
    <div class="test-group">
        <h2>UI Tests</h2>
        <table>
            <tr>
                <th>Category</th>
                <th>Passed</th>
                <th>Failed</th>
                <th>Total</th>
                <th>Pass Rate</th>
            </tr>
            <tr>
                <td>Playwright UI Tests</td>
                <td class="passed">${UI_PASSED}</td>
                <td class="failed">${UI_FAILED}</td>
                <td>${#UI_TEST_FILES[@]}</td>
                <td>$(( UI_PASSED * 100 / ${#UI_TEST_FILES[@]} ))%</td>
            </tr>
        </table>
    </div>
    
    <div class="test-group">
        <h2>API Tests</h2>
        <table>
            <tr>
                <th>Category</th>
                <th>Passed</th>
                <th>Failed</th>
                <th>Total</th>
                <th>Pass Rate</th>
            </tr>
            <tr>
                <td>API Integration Tests</td>
                <td class="passed">${API_PASSED}</td>
                <td class="failed">${API_FAILED}</td>
                <td>${#API_TEST_FILES[@]}</td>
                <td>$(( API_PASSED * 100 / ${#API_TEST_FILES[@]} ))%</td>
            </tr>
        </table>
    </div>
    
    <div class="test-group">
        <h2>Other Tests</h2>
        <table>
            <tr>
                <th>Category</th>
                <th>Passed</th>
                <th>Failed</th>
                <th>Total</th>
                <th>Pass Rate</th>
            </tr>
            <tr>
                <td>Python Tests</td>
                <td class="passed">${PYTHON_PASSED}</td>
                <td class="failed">${PYTHON_FAILED}</td>
                <td>${#PYTHON_TEST_FILES[@]}</td>
                <td>$(( PYTHON_PASSED * 100 / ${#PYTHON_TEST_FILES[@]} ))%</td>
            </tr>
            <tr>
                <td>Shell Script Tests</td>
                <td class="passed">${SHELL_PASSED}</td>
                <td class="failed">${SHELL_FAILED}</td>
                <td>${#SHELL_TEST_FILES[@]}</td>
                <td>$(( SHELL_PASSED * 100 / ${#SHELL_TEST_FILES[@]} ))%</td>
            </tr>
            <tr>
                <td>NodeJS Tests</td>
                <td class="passed">${NODE_PASSED}</td>
                <td class="failed">${NODE_FAILED}</td>
                <td>${#NODE_TEST_FILES[@]}</td>
                <td>$(( NODE_PASSED * 100 / ${#NODE_TEST_FILES[@]} ))%</td>
            </tr>
        </table>
    </div>
    
    <div class="links">
        <h2>Detailed Reports</h2>
        <ul>
            <li><a href="./ui-test-report/index.html">UI Test Detailed Report</a></li>
            <li><a href="./api-test-report/index.html">API Test Detailed Report</a></li>
            <li><a href="${LOG_FILE}">Full Test Log</a></li>
        </ul>
    </div>
</body>
</html>
EOF

success "Comprehensive test run completed!"
success "HTML summary: ${PWD}/comprehensive-test-summary.html"
success "Log file: ${LOG_FILE}"

# Exit with overall status
if [ $FAILED_TESTS -gt 0 ]; then
    error "Some tests failed. See logs for details."
    exit 1
else
    success "All tests passed successfully!"
    exit 0
fi 