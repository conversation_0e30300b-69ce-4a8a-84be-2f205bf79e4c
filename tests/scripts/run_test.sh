#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to run a specific test
run_test() {
  local test_file=$1
  echo -e "${YELLOW}Running test: ${test_file}${NC}"
  
  # Run the test and capture output
  docker compose -f docker-compose.testing.yml run --rm test_runner python -m pytest "$test_file" -v
  
  # Check exit code
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Test passed: ${test_file}${NC}"
    return 0
  else
    echo -e "${RED}Test failed: ${test_file}${NC}"
    return 1
  fi
}

# If specific test file is provided, run just that one
if [ $# -gt 0 ]; then
  run_test "$1"
  exit $?
fi

# Find all test files (excluding .focus_forge)
echo -e "${YELLOW}Finding test files...${NC}"
TEST_FILES=$(find /app -name "test_*.py" -type f -not -path "*/\.focus_forge/*" | sort)

# Display found test files
echo -e "${YELLOW}Found $(echo "$TEST_FILES" | wc -l) test files:${NC}"
echo "$TEST_FILES"
echo

# Ask user if they want to run all tests
read -p "Run all tests? (y/n): " RUN_ALL

if [ "$RUN_ALL" = "y" ]; then
  # Run all tests
  PASS_COUNT=0
  FAIL_COUNT=0
  
  for test_file in $TEST_FILES; do
    run_test "$test_file"
    if [ $? -eq 0 ]; then
      ((PASS_COUNT++))
    else
      ((FAIL_COUNT++))
    fi
  done
  
  echo
  echo -e "${GREEN}Tests passed: ${PASS_COUNT}${NC}"
  echo -e "${RED}Tests failed: ${FAIL_COUNT}${NC}"
  echo -e "Total tests: $((PASS_COUNT + FAIL_COUNT))"
else
  # Let user select a test to run
  echo "Select a test to run:"
  select test_file in $TEST_FILES "Quit"; do
    if [ "$test_file" = "Quit" ]; then
      exit 0
    fi
    
    if [ -n "$test_file" ]; then
      run_test "$test_file"
      break
    else
      echo "Invalid selection"
    fi
  done
fi
