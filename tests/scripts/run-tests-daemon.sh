#!/bin/bash

# Script to set up a persistent test container using volume mounts
# This allows you to run tests without restarting the container each time

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Container name
CONTAINER_NAME="python-test-daemon"

log() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}"
}

# Check if the container is already running
if docker ps -f "name=$CONTAINER_NAME" --format '{{.Names}}' | grep -q "$CONTAINER_NAME"; then
    section "CONTAINER ALREADY RUNNING"
    log "Test container '$CONTAINER_NAME' is already running"
    log "Use the following commands to run tests:"
    echo ""
    echo -e "${GREEN}# Run a specific test file:${NC}"
    echo "docker exec -w /app $CONTAINER_NAME python -m pytest path/to/test_file.py -v"
    echo ""
    echo -e "${GREEN}# Run all tests in a directory:${NC}"
    echo "docker exec -w /app $CONTAINER_NAME python -m pytest path/to/directory/ -v"
    echo ""
    echo -e "${GREEN}# Run tests matching a pattern:${NC}"
    echo "docker exec -w /app $CONTAINER_NAME python -m pytest -k 'pattern' -v"
    echo ""
    echo -e "${GREEN}# To stop the container:${NC}"
    echo "docker stop $CONTAINER_NAME"
    exit 0
fi

# Create the test container with volume mounts
section "CREATING PERSISTENT TEST CONTAINER"
log "Creating persistent Python test container with volume mounts..."

# Delete any existing stopped container with the same name
docker rm -f $CONTAINER_NAME 2>/dev/null || true

# Create the container with volume mounts
docker run -d --name $CONTAINER_NAME \
  -v "$(pwd):/app:ro" \
  -v "$(pwd)/test_logs:/app/test_logs:rw" \
  -e PYTHONUNBUFFERED=1 \
  -e TEST_MODE=true \
  -e "DATABASE_URL=sqlite:///:memory:" \
  -e "DATABASE_URL_ASYNC=sqlite+aiosqlite:///:memory:" \
  -e PYTHONPATH=/app \
  -w /app \
  python:3.10-slim \
  tail -f /dev/null

if [ $? -ne 0 ]; then
    error "Failed to create test container"
    exit 1
fi

# Install required dependencies in the container
section "INSTALLING DEPENDENCIES"
log "Installing test dependencies in container..."

docker exec $CONTAINER_NAME pip install --no-cache-dir pytest pytest-asyncio pytest-mock pytest-cov \
  sqlalchemy sqlalchemy-utils aiosqlite pydantic fastapi uvicorn requests black

if [ $? -ne 0 ]; then
    error "Failed to install dependencies"
    docker rm -f $CONTAINER_NAME 2>/dev/null || true
    exit 1
fi

# Create helper scripts for running tests easily
section "CREATING HELPER SCRIPTS"

# Create run-test.sh script
cat > run-test.sh << 'EOT'
#!/bin/bash
# Usage: ./run-test.sh <test_path>
if [ -z "$1" ]; then
  echo "Usage: $0 <test_path>"
  echo "Example: $0 ui/services/tests/test_config.py"
  exit 1
fi
docker exec -w /app python-test-daemon python -m pytest "$1" -v
EOT
chmod +x run-test.sh

# Create run-test-dir.sh script
cat > run-test-dir.sh << 'EOT'
#!/bin/bash
# Usage: ./run-test-dir.sh <directory_path>
if [ -z "$1" ]; then
  echo "Usage: $0 <directory_path>"
  echo "Example: $0 ui/services/tests"
  exit 1
fi
docker exec -w /app python-test-daemon python -m pytest "$1" -v
EOT
chmod +x run-test-dir.sh

# Create stop-test-daemon.sh script
cat > stop-test-daemon.sh << 'EOT'
#!/bin/bash
docker stop python-test-daemon
EOT
chmod +x stop-test-daemon.sh

section "CONTAINER READY"
success "Test container is now running and ready to use!"
log "Container ID: $(docker ps -f "name=$CONTAINER_NAME" --format '{{.ID}}')"
log "Container will remain running until you stop it with: ./stop-test-daemon.sh"
echo ""
log "Use the following commands to run tests:"
echo ""
echo -e "${GREEN}# Run a specific test file:${NC}"
echo "./run-test.sh path/to/test_file.py"
echo ""
echo -e "${GREEN}# Run all tests in a directory:${NC}"
echo "./run-test-dir.sh path/to/directory/"
echo ""
echo -e "${GREEN}# To stop the container:${NC}"
echo "./stop-test-daemon.sh"

exit 0 