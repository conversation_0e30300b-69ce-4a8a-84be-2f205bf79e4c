#!/bin/bash

# Advanced test runner that uses the persistent test container
# with more options for filtering tests and saving results

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CONTAINER_NAME="python-test-daemon"
TEST_PATH=""
TEST_PATTERN=""
VERBOSE="-v"
LOG_RESULTS=false
LOG_FILE=""
FAIL_FAST=false
COLLECT_ONLY=false

function show_help {
  echo "Usage: $0 [OPTIONS] <test_path>"
  echo "Run tests in the persistent test container with advanced options"
  echo ""
  echo "Options:"
  echo "  -h, --help             Show this help message"
  echo "  -p, --pattern PATTERN  Run tests matching the given pattern"
  echo "  -v, --verbose          Run tests with increased verbosity"
  echo "  -q, --quiet            Run tests with minimal output"
  echo "  -l, --log FILE         Save test results to the specified log file"
  echo "  -x, --fail-fast        Stop on first failure"
  echo "  -c, --collect-only     Only collect tests, don't run them"
  echo ""
  echo "Examples:"
  echo "  $0 ui/services/tests/test_config.py"
  echo "  $0 ui/services/tests -p test_item"
  echo "  $0 -v ui/services/tests -l test_results.log"
  echo "  $0 --collect-only ui/services/tests"
  exit 0
}

# Process command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -h|--help)
      show_help
      ;;
    -p|--pattern)
      TEST_PATTERN="-k $2"
      shift 2
      ;;
    -v|--verbose)
      VERBOSE="-vv"
      shift
      ;;
    -q|--quiet)
      VERBOSE=""
      shift
      ;;
    -l|--log)
      LOG_RESULTS=true
      LOG_FILE=$2
      shift 2
      ;;
    -x|--fail-fast)
      FAIL_FAST="-xvs"
      shift
      ;;
    -c|--collect-only)
      COLLECT_ONLY="--collect-only"
      shift
      ;;
    *)
      TEST_PATH=$1
      shift
      ;;
  esac
done

# Check if test path is provided
if [ -z "$TEST_PATH" ]; then
  echo -e "${RED}Error: No test path specified${NC}"
  show_help
fi

# Check if the container is running
if ! docker ps -f "name=$CONTAINER_NAME" --format '{{.Names}}' | grep -q "$CONTAINER_NAME"; then
  echo -e "${RED}Error: Test container '$CONTAINER_NAME' is not running${NC}"
  echo "Start the container first with: ./run-tests-daemon.sh"
  exit 1
fi

# Build the pytest command
PYTEST_CMD="python -m pytest $TEST_PATH $TEST_PATTERN $VERBOSE $FAIL_FAST $COLLECT_ONLY"

echo -e "${BLUE}Running tests:${NC} $PYTEST_CMD"

# Execute the command and handle logging
if [ "$LOG_RESULTS" = true ]; then
  # Create logs directory if it doesn't exist
  mkdir -p test_logs
  
  # Set default log file name if not provided
  if [ -z "$LOG_FILE" ]; then
    LOG_FILE="test_logs/test_run_$(date +%Y%m%d_%H%M%S).log"
  elif [[ "$LOG_FILE" != test_logs/* ]]; then
    LOG_FILE="test_logs/$LOG_FILE"
  fi
  
  echo -e "${BLUE}Saving results to:${NC} $LOG_FILE"
  echo "Test run at $(date)" > $LOG_FILE
  echo "Command: $PYTEST_CMD" >> $LOG_FILE
  echo "----------------------------------------" >> $LOG_FILE
  
  # Run tests and save output to log file
  docker exec -w /app $CONTAINER_NAME $PYTEST_CMD | tee -a $LOG_FILE
  exit_code=${PIPESTATUS[0]}
else
  # Run tests without logging
  docker exec -w /app $CONTAINER_NAME $PYTEST_CMD
  exit_code=$?
fi

# Show summary based on exit code
if [ $exit_code -eq 0 ]; then
  echo -e "${GREEN}All tests passed!${NC}"
else
  echo -e "${RED}Tests failed with exit code:${NC} $exit_code"
fi

exit $exit_code 