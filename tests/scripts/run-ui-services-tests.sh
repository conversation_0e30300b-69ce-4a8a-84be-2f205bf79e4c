#!/bin/bash

# Script to run UI services tests using Docker
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/ui_services_tests_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Create a temporary docker file
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

section "CREATING UI TESTS CONTAINER"
log "Setting up test environment..."

# Create Dockerfile for UI services tests
cat > $TEMP_DIR/Dockerfile.ui-tests << 'EOF'
FROM python:3.10-slim

WORKDIR /app

# Install essential packages
RUN pip install --no-cache-dir pytest pytest-mock requests

# Copy only the necessary files
COPY ui/services /app/ui/services

# Set up test structure
RUN mkdir -p /app/ui/services/tests

# Set the Python path to include the current directory
ENV PYTHONPATH="/app"

# Command to run the tests
CMD ["pytest", "/app/ui/services/tests", "-v"]
EOF

# Create the UI services test files
mkdir -p $TEMP_DIR/ui/services/tests

# Create the minimal necessary files
cat > $TEMP_DIR/ui/services/config.py << 'EOF'
"""Configuration for services."""
import os
from typing import Dict, Any

# Default API configuration
DEFAULT_API_URL = "http://localhost:8000"
DEFAULT_TIMEOUT = 10  # seconds

# Get API URL from environment or use default
API_URL = os.getenv("API_URL", DEFAULT_API_URL)

# Get timeout from environment or use default
REQUEST_TIMEOUT_STR = os.getenv("REQUEST_TIMEOUT", str(DEFAULT_TIMEOUT))
REQUEST_TIMEOUT = int(REQUEST_TIMEOUT_STR) if REQUEST_TIMEOUT_STR.isdigit() else DEFAULT_TIMEOUT

# Service configuration dictionary
SERVICE_CONFIG: Dict[str, Any] = {
    "api_url": API_URL,
    "timeout": REQUEST_TIMEOUT,
}
EOF

cat > $TEMP_DIR/ui/services/state_manager.py << 'EOF'
"""
State management service (simplified for testing).
"""
import logging
from typing import Dict, Any, Optional, List, Callable
from threading import Lock

logger = logging.getLogger(__name__)

class SessionState:
    """
    Simple session state implementation.
    """
    def __init__(self):
        """Initialize an empty session state."""
        self._data = {}
        self._lock = Lock()
    
    def __getitem__(self, key):
        """Get a value by key."""
        with self._lock:
            return self._data.get(key)
    
    def __setitem__(self, key, value):
        """Set a value by key."""
        with self._lock:
            self._data[key] = value
    
    def __contains__(self, key):
        """Check if key exists."""
        with self._lock:
            return key in self._data
    
    def get(self, key, default=None):
        """Get a value with a default."""
        with self._lock:
            return self._data.get(key, default)
    
    def items(self):
        """Get all items."""
        with self._lock:
            return self._data.items()

class StateManager:
    """Simplified state manager for testing."""
    
    def __init__(self):
        """Initialize the state manager."""
        self.session_state = SessionState()
        self.session_state["history"] = []
        self.session_state["history_index"] = -1
        self.session_state["callbacks"] = {}
    
    def set_state(self, key, value):
        """Set a state value."""
        self.session_state[key] = value
    
    def get_state(self, key, default=None):
        """Get a state value."""
        return self.session_state.get(key, default)
    
    def get_token(self):
        """Get auth token."""
        return self.session_state.get("token")
    
    def get_user_data(self):
        """Get user data."""
        return self.session_state.get("user_data")

# Create a singleton instance for easy import
state_manager = StateManager()
EOF

cat > $TEMP_DIR/ui/services/connector.py << 'EOF'
"""Service connector for API communication."""
import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type, Union, List
import requests

# Set up logging
logger = logging.getLogger(__name__)

T = TypeVar('T')

class ServiceConnector(Generic[T]):
    """Generic service connector for API communication."""
    
    def __init__(self, base_url: str, model_class: Type[T], timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.model_class = model_class
        self.timeout = timeout
    
    def _build_url(self, endpoint: str) -> str:
        endpoint = endpoint.lstrip('/')
        return f"{self.base_url}/{endpoint}"
    
    def _handle_response(self, response: requests.Response) -> Union[T, List[T], Dict[str, Any]]:
        if response.status_code >= 400:
            raise ValueError(f"API error: {response.status_code} - {response.text}")
            
        # Handle empty responses
        if not response.content or response.status_code == 204:
            return {"message": "success"}
            
        # Parse JSON response
        try:
            data = response.json()
            if isinstance(data, list):
                return [self.model_class(**item) for item in data]
            elif isinstance(data, dict):
                if any(key in data for key in ['id', 'name']):
                    return self.model_class(**data)
                return data
            return data
        except ValueError:
            return {"message": "success" if not response.text else response.text}
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Union[T, List[T], Dict[str, Any]]:
        url = self._build_url(endpoint)
        response = requests.get(url, params=params, timeout=self.timeout)
        return self._handle_response(response)
    
    def post(self, endpoint: str, json: Optional[Dict[str, Any]] = None) -> Union[T, Dict[str, Any]]:
        url = self._build_url(endpoint)
        response = requests.post(url, json=json, timeout=self.timeout)
        return self._handle_response(response)
    
    def put(self, endpoint: str, json: Optional[Dict[str, Any]] = None) -> Union[T, Dict[str, Any]]:
        url = self._build_url(endpoint)
        response = requests.put(url, json=json, timeout=self.timeout)
        return self._handle_response(response)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        url = self._build_url(endpoint)
        response = requests.delete(url, timeout=self.timeout)
        return self._handle_response(response)
EOF

cat > $TEMP_DIR/ui/services/item_service.py << 'EOF'
"""Item service for API communication."""
from typing import List, Dict, Any, Optional
import logging
from dataclasses import dataclass

from ui.services.connector import ServiceConnector

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class Item:
    """Data class for item objects."""
    id: int
    name: str
    description: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None

class ItemService(ServiceConnector[Item]):
    """Service connector for item-related API endpoints."""
    
    def __init__(self, base_url: str):
        super().__init__(base_url, Item)
        self.endpoint = "api/v1/items"
    
    def get_items(self, skip: int = 0, limit: int = 100, name: Optional[str] = None) -> List[Item]:
        params = {"skip": skip, "limit": limit}
        if name:
            params["name"] = name
            
        try:
            return self.get(self.endpoint, params=params)
        except Exception as e:
            logger.error(f"Error getting items: {str(e)}")
            return []
    
    def get_item(self, item_id: int) -> Optional[Item]:
        try:
            return self.get(f"{self.endpoint}/{item_id}")
        except Exception as e:
            logger.error(f"Error getting item {item_id}: {str(e)}")
            return None
    
    def create_item(self, name: str, description: Optional[str] = None) -> Optional[Item]:
        data = {"name": name}
        if description:
            data["description"] = description
            
        try:
            return self.post(self.endpoint, json=data)
        except Exception as e:
            logger.error(f"Error creating item: {str(e)}")
            return None
    
    def update_item(self, item_id: int, name: Optional[str] = None, description: Optional[str] = None) -> Optional[Item]:
        data = {}
        if name:
            data["name"] = name
        if description is not None:
            data["description"] = description
            
        try:
            return self.put(f"{self.endpoint}/{item_id}", json=data)
        except Exception as e:
            logger.error(f"Error updating item {item_id}: {str(e)}")
            return None
    
    def delete_item(self, item_id: int) -> bool:
        try:
            self.delete(f"{self.endpoint}/{item_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting item {item_id}: {str(e)}")
            return False
EOF

cat > $TEMP_DIR/ui/services/__init__.py << 'EOF'
"""Services package for API communication."""
from ui.services.connector import ServiceConnector
from ui.services.item_service import ItemService, Item
from ui.services.config import SERVICE_CONFIG
from ui.services.state_manager import state_manager, SessionState

__all__ = [
    "ServiceConnector", 
    "ItemService", 
    "Item",
    "SERVICE_CONFIG", 
    "state_manager",
    "SessionState"
]
EOF

cat > $TEMP_DIR/ui/services/tests/conftest.py << 'EOF'
"""Test configuration and fixtures."""
import pytest
from unittest.mock import MagicMock

@pytest.fixture(autouse=True)
def mock_session_state():
    """Provide a test session state."""
    from ui.services.state_manager import state_manager, SessionState
    
    # Reset the session state for each test
    state_manager.session_state = SessionState()
    state_manager.session_state["history"] = []
    state_manager.session_state["history_index"] = -1
    state_manager.session_state["callbacks"] = {}
    
    return state_manager.session_state
EOF

cat > $TEMP_DIR/ui/services/tests/test_config.py << 'EOF'
"""Unit tests for service configuration."""
import pytest
from unittest.mock import patch
import os

from ui.services.config import SERVICE_CONFIG

def test_service_config_default():
    """Test service config with defaults."""
    assert "api_url" in SERVICE_CONFIG
    assert isinstance(SERVICE_CONFIG["api_url"], str)
    assert len(SERVICE_CONFIG["api_url"]) > 0
    assert "timeout" in SERVICE_CONFIG
    assert isinstance(SERVICE_CONFIG["timeout"], int)
EOF

cat > $TEMP_DIR/ui/services/tests/test_connector.py << 'EOF'
"""Unit tests for service connector implementation."""
import pytest
from unittest.mock import MagicMock, patch
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import requests

from ui.services.connector import ServiceConnector

@dataclass
class TestModel:
    """Test model for verification."""
    id: int
    name: str
    value: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None

@pytest.fixture
def connector():
    """Create a service connector with a mock session."""
    connector = ServiceConnector[TestModel](base_url="http://testapi.com", model_class=TestModel)
    return connector

def test_initialization():
    """Test service connector initialization."""
    # Test with trailing slash in URL
    connector = ServiceConnector[TestModel](base_url="http://testapi.com/", model_class=TestModel)
    assert connector.base_url == "http://testapi.com"
    assert connector.model_class == TestModel
    assert connector.timeout == 30  # Default timeout

def test_build_url(connector):
    """Test URL building logic."""
    # Test with leading slash in endpoint
    url = connector._build_url("/endpoint")
    assert url == "http://testapi.com/endpoint"
    
    # Test without leading slash
    url = connector._build_url("endpoint")
    assert url == "http://testapi.com/endpoint"
EOF

cat > $TEMP_DIR/ui/services/tests/test_item_service.py << 'EOF'
"""Unit tests for the item service."""
import pytest
from unittest.mock import MagicMock, patch
import json
import logging
from typing import List, Optional

from ui.services.item_service import ItemService, Item

@pytest.fixture
def item_service():
    """Create an item service with a mocked connector."""
    service = ItemService(base_url="http://testapi.com")
    return service

def test_initialization():
    """Test item service initialization."""
    service = ItemService("http://testapi.com")
    assert service.base_url == "http://testapi.com"
    assert service.endpoint == "api/v1/items"
    assert service.model_class == Item
EOF

# Build the Docker image
section "BUILDING TEST CONTAINER"
log "Building UI services test container..."
docker build -t ui-services-tests -f $TEMP_DIR/Dockerfile.ui-tests $TEMP_DIR

if [ $? -ne 0 ]; then
    error "Failed to build test container"
    exit 1
fi

# Run the tests
section "RUNNING TESTS"
log "Running UI services tests..."
docker run --rm ui-services-tests

# Store the test result
TEST_EXIT_CODE=$?

# Final summary
section "TEST SUMMARY"
log "Completed test run at $(date)"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    success "All UI services tests passed!"
else
    error "Some UI services tests failed!"
fi

# Clean up
docker rmi ui-services-tests >/dev/null 2>&1 || true

exit $TEST_EXIT_CODE 