#!/bin/bash
# Script to run all UI tests and generate a summary report

# Create output directories
mkdir -p test_screenshots playwright-report test-results

# Set up colored output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo "============================================="
echo -e "${YELLOW}Running TurdParty UI Test Suite${NC}"
echo "============================================="
echo ""

# Function to run a test and record the result
run_test() {
  TEST_NAME=$1
  TEST_CMD=$2
  echo -e "${YELLOW}Running $TEST_NAME...${NC}"
  
  # Run the test
  if $TEST_CMD; then
    echo -e "${GREEN}✓ $TEST_NAME passed${NC}"
    echo "$TEST_NAME: PASSED" >> test-results/summary.txt
    return 0
  else
    echo -e "${RED}✗ $TEST_NAME failed${NC}"
    echo "$TEST_NAME: FAILED" >> test-results/summary.txt
    return 1
  fi
}

# Initialize summary file
echo "TurdParty UI Test Suite Results" > test-results/summary.txt
echo "Run on $(date)" >> test-results/summary.txt
echo "--------------------------------" >> test-results/summary.txt

# Run all test suites
FAILED_TESTS=0

run_test "Basic UI Components" "npm run test:ui -- --reporter=list" || ((FAILED_TESTS++))
run_test "Form Validation" "npm run test:forms -- --reporter=list" || ((FAILED_TESTS++))
run_test "VM Operations" "npm run test:vm -- --reporter=list" || ((FAILED_TESTS++))
run_test "Navigation Flow" "npm run test:nav -- --reporter=list" || ((FAILED_TESTS++))
run_test "Enhanced UI" "npm run test:enhanced -- --reporter=list" || ((FAILED_TESTS++))
run_test "Accessibility" "npm run test:a11y -- --reporter=list" || ((FAILED_TESTS++))

# Print summary
echo ""
echo "============================================="
echo -e "${YELLOW}Test Summary${NC}"
echo "============================================="
cat test-results/summary.txt

# Exit with failure if any tests failed
if [ $FAILED_TESTS -gt 0 ]; then
  echo ""
  echo -e "${RED}$FAILED_TESTS test suites failed${NC}"
  exit 1
else
  echo ""
  echo -e "${GREEN}All test suites passed${NC}"
  exit 0
fi 