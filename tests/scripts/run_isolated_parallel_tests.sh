#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PARALLEL_WORKERS=4
COVERAGE=false
VERBOSE="-v"
TEST_PATH="api/tests"
FAIL_FAST=false
JUNIT_REPORT=false
REPORT_DIR="test-reports"
TIMEOUT=300

# Function to show help
show_help() {
    echo "Usage: $0 [options] [test_path]"
    echo
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -p, --parallel N           Run tests in parallel with N workers (default: 4)"
    echo "  -c, --coverage             Generate coverage reports"
    echo "  -v, --verbose              Run tests in verbose mode"
    echo "  -f, --fail-fast            Stop on first failure"
    echo "  -j, --junit                Generate JUnit XML reports"
    echo "  -r, --report-dir DIR       Directory for reports (default: test-reports)"
    echo "  -t, --timeout SECONDS      Timeout for each test in seconds (default: 300)"
    echo
    echo "Examples:"
    echo "  $0 api/tests/schemas                # Run schema tests"
    echo "  $0 -p 8 -c api/tests               # Run all tests with 8 workers and coverage"
    echo "  $0 -c -j api/tests/test_unit       # Run unit tests with coverage and JUnit reports"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--parallel)
            PARALLEL_WORKERS="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE="-vv"
            shift
            ;;
        -f|--fail-fast)
            FAIL_FAST=true
            shift
            ;;
        -j|--junit)
            JUNIT_REPORT=true
            shift
            ;;
        -r|--report-dir)
            REPORT_DIR="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            TEST_PATH="$1"
            shift
            ;;
    esac
done

# Ensure test environment is running
echo -e "${YELLOW}Ensuring test environment is running...${NC}"
if ! docker ps -q -f name=turdparty_test_runner_test >/dev/null; then
    echo "Starting test environment..."
    docker compose -f docker-compose.testing.yml up -d
    
    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 5
fi

# Create report directory
mkdir -p "${REPORT_DIR}"
mkdir -p "${REPORT_DIR}/coverage"

# Function to run tests with proper isolation
run_isolated_tests() {
    local test_path=$1
    local marker=$2
    local workers=$3
    
    echo -e "${YELLOW}Running tests in ${test_path} with marker ${marker}...${NC}"
    
    # Build the pytest command
    local pytest_cmd="python -m pytest ${test_path} ${VERBOSE} -m \"${marker}\""
    
    # Add parallel execution if more than 1 worker
    if [ "$workers" -gt 1 ]; then
        pytest_cmd="${pytest_cmd} -n ${workers}"
    fi
    
    # Add fail-fast if requested
    if [ "$FAIL_FAST" = true ]; then
        pytest_cmd="${pytest_cmd} -x"
    fi
    
    # Add JUnit report if requested
    if [ "$JUNIT_REPORT" = true ]; then
        pytest_cmd="${pytest_cmd} --junitxml=${REPORT_DIR}/junit_${marker}.xml"
    fi
    
    # Add coverage if requested
    if [ "$COVERAGE" = true ]; then
        pytest_cmd="python -m coverage run --source=api --parallel-mode -m pytest ${test_path} ${VERBOSE} -m \"${marker}\""
        
        # Add parallel execution if more than 1 worker
        if [ "$workers" -gt 1 ]; then
            pytest_cmd="${pytest_cmd} -n ${workers}"
        fi
        
        # Add fail-fast if requested
        if [ "$FAIL_FAST" = true ]; then
            pytest_cmd="${pytest_cmd} -x"
        fi
        
        # Add JUnit report if requested
        if [ "$JUNIT_REPORT" = true ]; then
            pytest_cmd="${pytest_cmd} --junitxml=${REPORT_DIR}/junit_${marker}.xml"
        fi
    fi
    
    # Add timeout
    pytest_cmd="timeout ${TIMEOUT} ${pytest_cmd}"
    
    # Execute the command in the Docker container
    echo -e "${BLUE}Running command:${NC} ${pytest_cmd}"
    docker exec -it turdparty_test_runner_test bash -c "${pytest_cmd}"
    return $?
}

# Run tests with different markers to ensure isolation
echo -e "${YELLOW}Running tests with isolation...${NC}"

# Run schema tests (no database required)
run_isolated_tests "${TEST_PATH}" "schemas_only" "${PARALLEL_WORKERS}"
SCHEMAS_EXIT_CODE=$?

# Run tests that don't require database
run_isolated_tests "${TEST_PATH}" "no_db_required" "${PARALLEL_WORKERS}"
NO_DB_EXIT_CODE=$?

# Run unit tests (with database but isolated)
run_isolated_tests "${TEST_PATH}" "unit" "1"
UNIT_EXIT_CODE=$?

# Run integration tests (with database but isolated)
run_isolated_tests "${TEST_PATH}" "integration" "1"
INTEGRATION_EXIT_CODE=$?

# If coverage was enabled, combine and generate reports
if [ "$COVERAGE" = true ]; then
    echo -e "${YELLOW}Combining coverage data and generating reports...${NC}"
    docker exec -it turdparty_test_runner_test bash -c "
        python -m coverage combine
        python -m coverage report
        python -m coverage xml -o ${REPORT_DIR}/coverage/coverage.xml
        python -m coverage json -o ${REPORT_DIR}/coverage/coverage.json
        python -m coverage html -d ${REPORT_DIR}/coverage/html
    "
    
    # Copy coverage reports from container to host
    echo -e "${YELLOW}Copying coverage reports from container...${NC}"
    docker cp turdparty_test_runner_test:/app/${REPORT_DIR}/coverage ./${REPORT_DIR}/
fi

# Display test results
echo -e "${YELLOW}Test Results:${NC}"
echo -e "Schema Tests: $([ $SCHEMAS_EXIT_CODE -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "No DB Tests: $([ $NO_DB_EXIT_CODE -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "Unit Tests: $([ $UNIT_EXIT_CODE -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "Integration Tests: $([ $INTEGRATION_EXIT_CODE -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"

# Calculate overall exit code
OVERALL_EXIT_CODE=$((SCHEMAS_EXIT_CODE + NO_DB_EXIT_CODE + UNIT_EXIT_CODE + INTEGRATION_EXIT_CODE))

if [ $OVERALL_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
else
    echo -e "${RED}Some tests failed!${NC}"
fi

# If JUnit report was generated, copy it from container to host
if [ "$JUNIT_REPORT" = true ]; then
    echo -e "${YELLOW}Copying JUnit reports from container...${NC}"
    docker cp turdparty_test_runner_test:/app/${REPORT_DIR}/junit_*.xml ./${REPORT_DIR}/
fi

# Display coverage summary if available
if [ "$COVERAGE" = true ] && [ -f "${REPORT_DIR}/coverage/coverage.json" ]; then
    echo -e "${BLUE}Coverage Summary:${NC}"
    cat "${REPORT_DIR}/coverage/coverage.json" | grep -o '"percent_covered":[^,]*' | head -1
    echo -e "${BLUE}Detailed coverage report available at:${NC} ${REPORT_DIR}/coverage/html/index.html"
    
    # Run coverage analysis
    if [ -f "analyze_coverage.py" ]; then
        echo -e "${YELLOW}Running coverage analysis...${NC}"
        nix-shell -p python311 --run "python analyze_coverage.py --coverage-file ${REPORT_DIR}/coverage/coverage.json"
    fi
fi

exit $OVERALL_EXIT_CODE
