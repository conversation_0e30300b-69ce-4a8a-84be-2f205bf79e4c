#!/usr/bin/env python3
import os
import sys
import subprocess
import argparse
import shutil
import platform
from pathlib import Path

def check_dependencies():
    """Check if basic dependencies are installed"""
    required_cmds = ['python', 'pip', 'pytest']
    missing = []
    
    for cmd in required_cmds:
        if shutil.which(cmd) is None:
            missing.append(cmd)
    
    if missing:
        print(f"ERROR: Missing required commands: {', '.join(missing)}")
        print("Please install the missing dependencies before running tests.")
        return False
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print(f"ERROR: Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    
    return True

def setup_venv():
    """Set up a virtual environment if not exists"""
    venv_path = Path(".venv")
    
    if not venv_path.exists():
        print("Creating virtual environment...")
        result = subprocess.run([sys.executable, "-m", "venv", str(venv_path)], 
                               capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Failed to create virtual environment: {result.stderr}")
            return None
    
    # Get the activate script path based on platform
    if platform.system() == "Windows":
        activate_script = venv_path / "Scripts" / "activate.bat"
        python_path = venv_path / "Scripts" / "python.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        python_path = venv_path / "bin" / "python"
    
    if not python_path.exists():
        print(f"ERROR: Python not found in virtual environment at {python_path}")
        return None
    
    return {
        "path": venv_path,
        "activate": str(activate_script),
        "python": str(python_path)
    }

def install_requirements(venv):
    """Install test requirements"""
    print("Installing test requirements...")
    
    # Check for requirements files
    req_files = ["requirements-test.txt", "test-requirements.txt", "requirements.txt"]
    req_file = None
    
    for f in req_files:
        if os.path.exists(f):
            req_file = f
            break
    
    if not req_file:
        print("WARNING: No requirements file found, skipping dependency installation")
        return True
        
    print(f"Installing dependencies from {req_file}...")
    
    if platform.system() == "Windows":
        cmd = f"{venv['python']} -m pip install -r {req_file}"
    else:
        cmd = f"source {venv['activate']} && pip install -r {req_file}"
        
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"ERROR: Failed to install requirements: {result.stderr}")
        return False
        
    print("Dependencies installed successfully")
    return True

def run_tests(venv, test_path=None, verbose=False):
    """Run the tests"""
    print("\n=== Running tests ===")
    
    # Build the command
    if platform.system() == "Windows":
        cmd_prefix = f"{venv['python']} -m"
    else:
        cmd_prefix = f"source {venv['activate']} && python -m"
    
    pytest_args = ["pytest"]
    if test_path:
        pytest_args.append(test_path)
    if verbose:
        pytest_args.append("-v")
    
    cmd = f"{cmd_prefix} {' '.join(pytest_args)}"
    print(f"Running: {cmd}")
    
    # Run in a way where output is shown in real-time
    process = subprocess.Popen(cmd, shell=True)
    process.communicate()
    
    return process.returncode

def main():
    parser = argparse.ArgumentParser(description="Run tests with proper environment setup")
    parser.add_argument("--test", help="Specific test file or directory to run", default=None)
    parser.add_argument("-v", "--verbose", action="store_true", help="Run with verbose output")
    parser.add_argument("--skip-venv", action="store_true", help="Skip virtual environment setup")
    args = parser.parse_args()
    
    # Check basic dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Set up virtual environment
    venv = None
    if not args.skip_venv:
        venv = setup_venv()
        if not venv:
            print("ERROR: Failed to set up virtual environment")
            sys.exit(1)
        
        # Install requirements
        if not install_requirements(venv):
            sys.exit(1)
    else:
        print("Skipping virtual environment setup")
        venv = {"python": sys.executable, "activate": None, "path": None}
    
    # Run tests
    result = run_tests(venv, args.test, args.verbose)
    sys.exit(result)

if __name__ == "__main__":
    main() 