#!/bin/bash
# Script to run tests in Docker container with test mode enabled

# Display help message
function show_help {
    echo "Usage: $0 [OPTIONS] [TEST_PATH]"
    echo "Run tests in Docker container with authentication disabled."
    echo ""
    echo "Options:"
    echo "  -h, --help       Show this help message and exit"
    echo "  -c, --coverage   Generate coverage reports"
    echo "  -v, --verbose    Run tests in verbose mode"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run all tests"
    echo "  $0 api/tests/test_auth_bypass.py  # Run specific test file"
    echo "  $0 -c                        # Run all tests with coverage"
}

# Parse command line arguments
COVERAGE=""
VERBOSE="-v"
TEST_PATH="api/tests"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--coverage)
            COVERAGE="--cov=api --cov-report=term --cov-report=html"
            shift
            ;;
        -v|--verbose)
            VERBOSE="-v"
            shift
            ;;
        *)
            TEST_PATH="$1"
            shift
            ;;
    esac
done

# Check if Docker container is running
if ! docker ps | grep -q TurdParty-container; then
    echo "Error: TurdParty-container is not running"
    exit 1
fi

# Create a temporary Python script to enable test mode and run tests
cat > /tmp/run_tests_with_test_mode.py << 'EOF'
#!/usr/bin/env python3
"""
Script to run tests with test mode enabled.
"""
import sys
import pytest
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import and enable test mode
from api.core.test_config import test_settings

def main():
    """Run tests with test mode enabled."""
    logger.info("Enabling test mode to bypass authentication...")
    test_settings.enable_test_mode()
    
    logger.info("Running tests...")
    return pytest.main(sys.argv[1:])

if __name__ == "__main__":
    sys.exit(main())
EOF

# Copy the script to the Docker container
docker cp /tmp/run_tests_with_test_mode.py TurdParty-container:/tmp/

# Run the tests in the container using python directly
echo "Running tests in Docker container with test mode enabled..."
docker exec -it TurdParty-container python /tmp/run_tests_with_test_mode.py $VERBOSE $COVERAGE $TEST_PATH

# Clean up
rm -f /tmp/run_tests_with_test_mode.py 