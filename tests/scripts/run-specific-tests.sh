#!/bin/bash

# Script to run specific Python tests with Docker volume mounts
# Usage: ./run-specific-tests.sh [test_path_or_pattern]
# Examples: 
#   ./run-specific-tests.sh ui/services/tests
#   ./run-specific-tests.sh ./test_item.py
#   ./run-specific-tests.sh "test_*_api.py"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/specific_tests_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Check for test path argument
if [ -z "$1" ]; then
    error "No test path specified. Usage: ./run-specific-tests.sh [test_path_or_pattern]"
    exit 1
fi

TEST_PATH="$1"
log "Running tests matching: $TEST_PATH"

# Create the test container with volume mounts
section "CREATING TEST CONTAINER"
log "Creating Python test container with volume mounts..."

# Delete any existing container
docker rm -f python-test-runner 2>/dev/null || true

# Create the container with volume mounts
docker run -d --name python-test-runner \
  -v "$(pwd):/app:ro" \
  -v "$(pwd)/test_logs:/app/test_logs:rw" \
  -e PYTHONUNBUFFERED=1 \
  -e TEST_MODE=true \
  -e "DATABASE_URL=sqlite:///:memory:" \
  -e "DATABASE_URL_ASYNC=sqlite+aiosqlite:///:memory:" \
  -e PYTHONPATH=/app \
  -w /app \
  python:3.10-slim \
  tail -f /dev/null

# Install required dependencies in the container
section "INSTALLING DEPENDENCIES"
log "Installing test dependencies in container..."

docker exec python-test-runner pip install --no-cache-dir pytest pytest-asyncio pytest-mock pytest-cov \
  sqlalchemy sqlalchemy-utils aiosqlite pydantic fastapi uvicorn requests | tee -a "$LOG_FILE"

# Find matching test files
if [ -f "$TEST_PATH" ]; then
    # User specified a specific file
    log "Testing specific file: $TEST_PATH"
    PYTHON_TEST_FILES=("$TEST_PATH")
elif [ -d "$TEST_PATH" ]; then
    # User specified a directory
    log "Testing all files in directory: $TEST_PATH"
    PYTHON_TEST_FILES=($(find "$TEST_PATH" -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__"))
else
    # User specified a pattern
    log "Testing files matching pattern: $TEST_PATH"
    PYTHON_TEST_FILES=($(find . -type f -name "$TEST_PATH" | grep -v "__pycache__" | grep -v ".venv" | \
    grep -v ".pip_modules" | grep -v "node_modules" | grep -v "venv" | grep -v "site-packages"))
fi

log "Found ${#PYTHON_TEST_FILES[@]} test files matching the criteria"

if [ ${#PYTHON_TEST_FILES[@]} -eq 0 ]; then
    error "No test files found matching the pattern. Exiting."
    docker rm -f python-test-runner 2>/dev/null || true
    exit 1
fi

# Set counters
TOTAL_TESTS=${#PYTHON_TEST_FILES[@]}
PASSED_TESTS=0
FAILED_TESTS=0
STREAMLIT_FAILS=0

# Create summary file
SUMMARY_FILE="$LOG_DIR/specific_test_summary.txt"
echo "Python Test Summary (Specific Tests)" > $SUMMARY_FILE
echo "==================================" >> $SUMMARY_FILE
echo "Run at: $(date)" >> $SUMMARY_FILE
echo "Pattern: $TEST_PATH" >> $SUMMARY_FILE
echo "" >> $SUMMARY_FILE
echo "| Test File | Status | Notes |" >> $SUMMARY_FILE
echo "|-----------|--------|-------|" >> $SUMMARY_FILE

# Run each Python test
section "RUNNING SPECIFIC PYTHON TESTS"
CURRENT_TEST=0
for test_file in "${PYTHON_TEST_FILES[@]}"; do
    CURRENT_TEST=$((CURRENT_TEST+1))
    log "[${CURRENT_TEST}/${TOTAL_TESTS}] Running test: ${test_file}"
    
    # Run the test with a timeout, ensuring we're in the right directory
    timeout 30s docker exec -w /app python-test-runner python -m pytest "${test_file}" -v > "${LOG_DIR}/current_test.log" 2>&1
    exit_code=$?
    
    # Check if timeout occurred
    if [ $exit_code -eq 124 ]; then
        error "⚠ Test timed out after 30s: ${test_file}"
        echo "| ${test_file} | ⚠ TIMEOUT | Test took too long |" >> $SUMMARY_FILE
        echo "Test timed out after 30s" > "${LOG_DIR}/current_test.log"
        FAILED_TESTS=$((FAILED_TESTS+1))
    elif [ $exit_code -eq 0 ]; then
        success "✓ Test passed: ${test_file}"
        echo "| ${test_file} | ✓ PASS | |" >> $SUMMARY_FILE
        PASSED_TESTS=$((PASSED_TESTS+1))
    else
        error "✗ Test failed: ${test_file}"
        
        # Check if failure is due to Streamlit dependency
        if grep -q "streamlit\|ImportError: No module named 'streamlit'" "${LOG_DIR}/current_test.log"; then
            echo "| ${test_file} | ✗ FAIL | Streamlit dependency |" >> $SUMMARY_FILE
            STREAMLIT_FAILS=$((STREAMLIT_FAILS+1))
        else
            # Extract error message for more info
            error_msg=$(grep -A 2 "FAILED\|ERROR:" "${LOG_DIR}/current_test.log" | head -3 | tr -d '\n' | tr '\t' ' ' | sed 's/  */ /g')
            echo "| ${test_file} | ✗ FAIL | ${error_msg} |" >> $SUMMARY_FILE
        fi
        
        FAILED_TESTS=$((FAILED_TESTS+1))
    fi
    
    # Add to log file
    echo "===== TEST: ${test_file} =====" >> "$LOG_FILE"
    cat "${LOG_DIR}/current_test.log" >> "$LOG_FILE"
    echo "===============================" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
done

# Generate test summary
section "TEST SUMMARY"
log "Tests completed at $(date)"
log "----------------------------------------------"
log "Passed: ${PASSED_TESTS} / ${TOTAL_TESTS}"
log "Failed: ${FAILED_TESTS} / ${TOTAL_TESTS}"
log "Failed due to Streamlit: ${STREAMLIT_FAILS} / ${TOTAL_TESTS}"
if [ ${TOTAL_TESTS} -gt 0 ]; then
    log "Pass rate: $((PASSED_TESTS * 100 / TOTAL_TESTS))%"
fi
log "----------------------------------------------"
log "Detailed summary: ${SUMMARY_FILE}"

# Clean up
section "CLEANUP"
log "Cleaning up test container..."
docker rm -f python-test-runner 2>/dev/null || true

exit $((FAILED_TESTS > 0)) 