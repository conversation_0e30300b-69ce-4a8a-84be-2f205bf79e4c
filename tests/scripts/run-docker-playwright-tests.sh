#!/bin/bash

# <PERSON>ript to run Playwright tests in Docker

# Set script to exit on error
set -e

# Project namespacing
PROJECT_NAME="turdparty"
ENV_NAME="${PROJECT_NAME}-test"

# Print header
echo "==================================="
echo "   TurdParty Playwright Test Runner"
echo "==================================="
echo ""

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Check if docker compose is installed
if ! docker compose version &> /dev/null; then
    echo "❌ docker compose is not installed or not working properly"
    exit 1
fi

# Function to clean up containers
cleanup() {
    echo ""
    echo "Cleaning up containers..."
    docker compose -f .dockerwrapper/docker-compose.playwright.yml -p $ENV_NAME down
    echo "Done"
}

# Set up cleanup on script exit
trap cleanup EXIT

# Make sure the script directory exists
mkdir -p .dockerwrapper/scripts
mkdir -p test-results
mkdir -p test_screenshots
mkdir -p playwright-report

# Copy the network configuration script if it doesn't exist in the scripts directory
if [ ! -f "scripts/setup-network-config.sh" ] && [ -f ".dockerwrapper/scripts/setup-network-config.sh" ]; then
    echo "Copying network configuration script..."
    mkdir -p scripts
    cp .dockerwrapper/scripts/setup-network-config.sh scripts/
    chmod +x scripts/setup-network-config.sh
fi

# Check if we have the images built
if ! docker images | grep -q "turdparty_frontend"; then
    echo "⚠️ turdparty_frontend image not found"
    echo "Building frontend image..."
    docker build -t turdparty_frontend -f .dockerwrapper/Dockerfile.frontend .
fi

if ! docker images | grep -q "turdparty_api"; then
    echo "⚠️ turdparty_api image not found"
    echo "Building API image..."
    docker build -t turdparty_api -f .dockerwrapper/Dockerfile.api .
fi

# Start containers
echo "Starting test containers..."
docker compose -f .dockerwrapper/docker-compose.playwright.yml -p $ENV_NAME up --build -d

# Follow playwright logs
echo "Following test logs (Ctrl+C to stop following, tests will continue):"
docker logs "${PROJECT_NAME}_playwright" -f || true

# Wait for tests to complete
echo "Waiting for tests to complete..."
while docker ps | grep -q "${PROJECT_NAME}_playwright"; do
    sleep 2
done

# Print final status
if [ $? -eq 0 ]; then
    echo "✅ Tests completed successfully"
else
    echo "❌ Tests failed"
fi

# Show test results location
echo ""
echo "Test results are available at:"
echo "  - Reports: $(pwd)/playwright-report/index.html"
echo "  - Screenshots: $(pwd)/test_screenshots/"
echo ""

# Check if any HTML report exists and suggest opening it
if [ -f "playwright-report/index.html" ]; then
    echo "To view the report, open: playwright-report/index.html"
    
    # Try to automatically open the report if running on desktop
    if command -v xdg-open &> /dev/null; then
        read -p "Open report now? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            xdg-open playwright-report/index.html
        fi
    elif command -v open &> /dev/null; then
        read -p "Open report now? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            open playwright-report/index.html
        fi
    fi
fi

echo "Done." 