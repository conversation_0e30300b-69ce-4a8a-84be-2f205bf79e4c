#!/bin/bash

# <PERSON>ript to run all tests using volume mounts instead of copying files
# This approach is faster and keeps test files in sync between host and containers

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/volume_test_run_$(date +%Y%m%d_%H%M%S).log"

# Streamlit conversion log
STREAMLIT_LOG="$LOG_DIR/streamlit_to_flask_conversion.log"
echo "# Streamlit to Flask Conversion Log" > $STREAMLIT_LOG
echo "Generated on $(date)" >> $STREAMLIT_LOG
echo "Files that contain streamlit imports and need to be converted to Flask:" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Ensure script exits on error
set -e

section "RUNNING ALL TESTS WITH VOLUME MOUNTS"
log "Starting comprehensive test run at $(date)"
log "Log file: $LOG_FILE"
log "Streamlit conversion log: $STREAMLIT_LOG"

# Create required directories
log "Creating required directories..."
mkdir -p test_screenshots test_results/performance playwright-report test-results

# Initialize counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    error "Docker is not running. Please start Docker first."
    exit 1
fi

# Start test containers with volume mounts
section "STARTING TEST CONTAINERS"
log "Starting test containers with volume mounts..."

# First, stop any existing test containers
docker rm -f turdparty_playwright_test api-test python-test 2>/dev/null || true

# Start the containers
docker-compose -f .dockerwrapper/docker-compose-test-volumes.yml up -d

# Wait for containers to start
log "Waiting for containers to be ready..."
sleep 10

# Log Streamlit files that need conversion
section "LOGGING STREAMLIT FILES"
log "Identifying files with Streamlit imports for conversion to Flask..."

# Find all Python files with streamlit imports
STREAMLIT_FILES=($(grep -r "import streamlit" --include="*.py" . | cut -d: -f1 | sort | uniq))

log "Found ${#STREAMLIT_FILES[@]} files with Streamlit imports"

# Log the files to the conversion log
for file in "${STREAMLIT_FILES[@]}"; do
    echo "- $file" >> $STREAMLIT_LOG
    # Add extra details about the streamlit usage
    echo "  Usage details:" >> $STREAMLIT_LOG
    grep -A 2 "streamlit" "$file" | sed 's/^/    /' >> $STREAMLIT_LOG
    echo "" >> $STREAMLIT_LOG
done

# 1. Run Playwright UI Tests
section "RUNNING PLAYWRIGHT UI TESTS"

# Find all playwright test files
UI_TEST_FILES=(
    $(find tests -type f -name "*.spec.js" -o -name "*.test.js")
)

log "Found ${#UI_TEST_FILES[@]} Playwright UI test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#UI_TEST_FILES[@]}))

# Run each UI test directly without copying (tests are mounted as volumes)
UI_PASSED=0
UI_FAILED=0
for test_file in "${UI_TEST_FILES[@]}"; do
    app_path="/app/${test_file}"
    log "Running test: ${test_file}"
    
    if docker exec turdparty_playwright_test npx playwright test "${app_path}" --reporter=list >> "$LOG_FILE" 2>&1; then
        success "✓ Test passed: ${test_file}"
        UI_PASSED=$((UI_PASSED+1))
    else
        error "✗ Test failed: ${test_file}"
        UI_FAILED=$((UI_FAILED+1))
        
        # Check if failure is due to Streamlit dependency
        if docker exec turdparty_playwright_test cat /tmp/playwright-log.txt 2>/dev/null | grep -q "streamlit"; then
            echo "- UI Test Failed (Streamlit dependency): ${test_file}" >> $STREAMLIT_LOG
        fi
    fi
done

PASSED_TESTS=$((PASSED_TESTS + UI_PASSED))
FAILED_TESTS=$((FAILED_TESTS + UI_FAILED))

# 2. Run Python Tests
section "RUNNING PYTHON TESTS"

# Find all Python test files
PYTHON_TEST_FILES=(
    $(find . -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__" | grep -v ".venv")
)

log "Found ${#PYTHON_TEST_FILES[@]} Python test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#PYTHON_TEST_FILES[@]}))

# Run each Python test in the Python test container
PYTHON_PASSED=0
PYTHON_FAILED=0
for test_file in "${PYTHON_TEST_FILES[@]}"; do
    # Make the path relative to /app in the container
    container_path="${test_file}"
    
    log "Running Python test: ${test_file}"
    
    if docker exec python-test python -m pytest "${container_path}" -v >> "$LOG_FILE" 2>&1; then
        success "✓ Python test passed: ${test_file}"
        PYTHON_PASSED=$((PYTHON_PASSED+1))
    else
        error "✗ Python test failed: ${test_file}"
        PYTHON_FAILED=$((PYTHON_FAILED+1))
        
        # Check if failure is due to Streamlit dependency
        if docker exec python-test cat /tmp/pytest-log.txt 2>/dev/null | grep -q "streamlit"; then
            echo "- Python Test Failed (Streamlit dependency): ${test_file}" >> $STREAMLIT_LOG
            
            # Extract the specific streamlit-related error
            docker exec python-test cat /tmp/pytest-log.txt 2>/dev/null | grep -A 3 "streamlit" | head -4 >> $STREAMLIT_LOG
            echo "" >> $STREAMLIT_LOG
        fi
    fi
done

PASSED_TESTS=$((PASSED_TESTS + PYTHON_PASSED))
FAILED_TESTS=$((FAILED_TESTS + PYTHON_FAILED))

# 3. Run API Tests
section "RUNNING API TESTS"

# Make sure the API container is healthy
if docker exec api-test curl -s http://localhost:8000/health > /dev/null; then
    log "API container is healthy"
else
    error "API container is not healthy, checking logs..."
    docker logs api-test
    # Try to restart the container
    docker restart api-test
    sleep 5
fi

# Find all API test files
API_TEST_FILES=(
    $(find tests/playwright -type f -name "*.spec.js" -o -name "*.test.js" | grep -E 'api|integration')
)

log "Found ${#API_TEST_FILES[@]} API test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#API_TEST_FILES[@]}))

# Run each API test
API_PASSED=0
API_FAILED=0
for test_file in "${API_TEST_FILES[@]}"; do
    app_path="/app/${test_file}"
    log "Running API test: ${test_file}"
    
    if docker exec -e API_URL=http://api-test:8000 turdparty_playwright_test npx playwright test "${app_path}" --reporter=list >> "$LOG_FILE" 2>&1; then
        success "✓ API test passed: ${test_file}"
        API_PASSED=$((API_PASSED+1))
    else
        error "✗ API test failed: ${test_file}"
        API_FAILED=$((API_FAILED+1))
        
        # Check if failure is due to Streamlit dependency
        if docker exec turdparty_playwright_test cat /tmp/playwright-log.txt 2>/dev/null | grep -q "streamlit"; then
            echo "- API Test Failed (Streamlit dependency): ${test_file}" >> $STREAMLIT_LOG
        fi
    fi
done

PASSED_TESTS=$((PASSED_TESTS + API_PASSED))
FAILED_TESTS=$((FAILED_TESTS + API_FAILED))

# Generate comprehensive test summary
section "COMPREHENSIVE TEST SUMMARY"
log "UI Tests: ${UI_PASSED} passed, ${UI_FAILED} failed (total: ${#UI_TEST_FILES[@]})"
log "API Tests: ${API_PASSED} passed, ${API_FAILED} failed (total: ${#API_TEST_FILES[@]})"
log "Python Tests: ${PYTHON_PASSED} passed, ${PYTHON_FAILED} failed (total: ${#PYTHON_TEST_FILES[@]})"
log "---------------------------------------------"
log "TOTAL: ${PASSED_TESTS} passed, ${FAILED_TESTS} failed (total: ${TOTAL_TESTS})"

# Cleanup (optional - comment out to keep containers running)
section "CLEANUP"
log "Cleaning up containers..."
docker-compose -f .dockerwrapper/docker-compose-test-volumes.yml down

# Print streamlit conversion info
section "STREAMLIT TO FLASK CONVERSION"
log "Files with Streamlit imports that need conversion: ${#STREAMLIT_FILES[@]}"
log "See detailed log at: ${STREAMLIT_LOG}"

exit $((FAILED_TESTS > 0)) 