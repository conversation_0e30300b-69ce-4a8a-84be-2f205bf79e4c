#!/bin/bash

# Script to run functional tests (excluding UI-focused tests)

set -e

echo "Starting test environment..."

# Check if the containers are running
if ! docker ps | grep -q turdparty_frontend; then
  echo "Starting Docker containers..."
  nix-shell -p docker-compose --command "cd .dockerwrapper && docker-compose -f docker-compose.playwright.yml up -d"
  echo "Waiting for services to start..."
  sleep 10
fi

# Create a unique container name with timestamp
CONTAINER_NAME="turdparty_test_runner_$(date +%s)"
echo "Using container name: $CONTAINER_NAME"

# Create a new container for running tests
echo "Creating test container..."
docker run --rm -d --name $CONTAINER_NAME \
  --network=dockerwrapper_turdparty_network \
  -v "$(pwd)/tests:/app/tests" \
  -v "$(pwd)/playwright-report:/app/playwright-report" \
  -v "$(pwd)/test-results:/app/test-results" \
  -v "$(pwd)/test_screenshots:/app/test_screenshots" \
  -v "$(pwd)/playwright.config.js:/app/playwright.config.js" \
  mcr.microsoft.com/playwright:v1.39.0-focal \
  tail -f /dev/null

# Wait for the container to start
sleep 2

echo "Copying test files to container..."
docker exec $CONTAINER_NAME mkdir -p /app/tests /app/test_screenshots /app/test-results /app/playwright-report

echo "Setting up Node.js project..."
docker exec $CONTAINER_NAME bash -c "cd /app && npm init -y"

echo "Installing Playwright and dependencies..."
docker exec $CONTAINER_NAME bash -c "cd /app && npm install @playwright/test @axe-core/playwright"
docker exec $CONTAINER_NAME bash -c "cd /app && npx playwright install chromium"

echo "Testing network connectivity to services..."
docker exec $CONTAINER_NAME bash -c "ping -c 1 turdparty_frontend || echo 'Cannot reach frontend'"
docker exec $CONTAINER_NAME bash -c "ping -c 1 turdparty_api || echo 'Cannot reach API'"

# Functional test files (excluding UI-focused tests)
test_files=(
  "vm-operations.spec.js"    # VM functionality tests
  "performance.spec.js"      # Performance tests
  "security.spec.js"         # Security tests
  "file-upload-e2e.test.js"  # File upload functionality (has UI but tests core functionality)
)

# Track results
passed=0
failed=0

# Run each test separately
echo "Running functional tests..."
for test_file in "${test_files[@]}"; do
  echo "Running test: $test_file"
  
  if docker exec $CONTAINER_NAME bash -c "cd /app && PLAYWRIGHT_BROWSERS_PATH=/ms-playwright FRONTEND_URL=http://turdparty_frontend:3100/ui API_URL=http://turdparty_api:3050 npx playwright test tests/$test_file --config=playwright.config.js"; then
    echo "✅ Test passed: $test_file"
    passed=$((passed + 1))
  else
    echo "❌ Test failed: $test_file"
    failed=$((failed + 1))
  fi
done

# Report summary
echo ""
echo "Test Summary"
echo "============"
echo "Passed: $passed"
echo "Failed: $failed"
echo "Total: $((passed + failed))"
if [ $((passed + failed)) -gt 0 ]; then
  echo "Success rate: $(( (passed * 100) / (passed + failed) ))%"
else
  echo "No tests were run"
fi

# Clean up
echo "Cleaning up test container..."
docker stop $CONTAINER_NAME

# Exit with success if all tests passed
if [ $failed -eq 0 ]; then
  echo "All functional tests passed! 🎉"
  exit 0
else
  echo "Some functional tests failed. Check the logs for details."
  exit 1
fi
