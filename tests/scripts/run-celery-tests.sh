#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Flag to track if we started services
STARTED_SERVICES=false

echo -e "${GREEN}Running Celery integration tests...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
  exit 1
fi

# Check if docker compose is available
if ! docker compose version &> /dev/null; then
  echo -e "${RED}Error: docker compose is not available. Please ensure Docker is properly installed with compose support.${NC}"
  exit 1
fi

# Check if services are already running
echo -e "${YELLOW}Checking if services are already running...${NC}"
if docker ps | grep -q "turdparty_redis_1" && docker ps | grep -q "turdparty_api_1" && docker ps | grep -q "turdparty_celery_default_1"; then
  echo -e "${GREEN}Required services are already running.${NC}"
else
  echo -e "${YELLOW}Starting required services...${NC}"
  cd .dockerwrapper
  docker compose up -d redis api celery-worker-default
  cd ..

  # Set flag to indicate we started services
  STARTED_SERVICES=true

  # Wait for services to be ready
  echo -e "${YELLOW}Waiting for services to be ready...${NC}"
  sleep 10
fi

# Run the tests
echo -e "${YELLOW}Running tests...${NC}"
python -m pytest tests/integration/test_celery_integration.py -v

# Cleanup is only needed if we started the services in this script
if [ "$STARTED_SERVICES" = "true" ]; then
  echo -e "${YELLOW}Cleaning up services started by this script...${NC}"
  cd .dockerwrapper
  docker compose stop celery-worker-default
  cd ..
else
  echo -e "${GREEN}Skipping cleanup as services were already running.${NC}"
fi

echo -e "${GREEN}Tests completed!${NC}"
