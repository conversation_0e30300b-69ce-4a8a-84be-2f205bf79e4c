#!/usr/bin/env python
"""
Script to run tests and generate a component sequence diagram with pass/fail statistics.
"""
import os
import sys
import subprocess
import json
import re
from collections import defaultdict
import argparse
from pathlib import Path

# Component categories - focusing on schemas for quick testing
COMPONENTS = {
    "api": ["api/tests/schemas/test_user_schemas.py"],
    "minio": ["api/tests/schemas/test_file_selection_schemas.py"],
    "vagrant": ["api/tests/schemas/test_vagrant_vm_schemas.py"],
    "file_upload": ["api/tests/schemas/test_file_selection_schemas.py"],
    "vm_injection": ["api/tests/schemas/test_vm_injection_schemas.py"],
    "ui": []  # Skip UI tests for now
}

# Component interfaces - focusing on schemas for quick testing
INTERFACES = {
    "api_routes": [],  # Skip for now
    "api_services": [],  # Skip for now
    "api_schemas": ["api/tests/schemas"],
    "api_integration": [],  # Skip for now
    "ui_components": [],  # Skip for now
    "ui_workflows": []  # Skip for now
}

def run_pytest(test_path, verbose=True):
    """Run pytest on the specified path and return the results."""
    cmd = ["python", "-m", "pytest", test_path, "-v", "--no-header"]

    if verbose:
        print(f"Running tests: {' '.join(cmd)}")

    env = os.environ.copy()
    env["TEST_MODE"] = "true"
    env["DISABLE_FILE_LOGGING"] = "true"

    try:
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True
        )
        return result.stdout, result.stderr, result.returncode
    except Exception as e:
        return "", f"Error running tests: {str(e)}", 1

def run_playwright_tests(test_path, verbose=True):
    """Run Playwright tests on the specified path and return the results."""
    # Check if the path exists
    if not os.path.exists(test_path):
        return "", f"Test path does not exist: {test_path}", 1

    cmd = ["npx", "playwright", "test", test_path, "--reporter=list"]

    if verbose:
        print(f"Running Playwright tests: {' '.join(cmd)}")

    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )
        return result.stdout, result.stderr, result.returncode
    except Exception as e:
        return "", f"Error running Playwright tests: {str(e)}", 1

def parse_pytest_results(output):
    """Parse pytest output to extract test results."""
    passed = len(re.findall(r'PASSED', output))
    failed = len(re.findall(r'FAILED', output))
    skipped = len(re.findall(r'SKIPPED', output))
    errors = len(re.findall(r'ERROR', output))

    # If we're using mock data, the output format is different
    if passed == 0 and failed == 0 and skipped == 0 and errors == 0:
        # Count lines with test_ in them
        lines = output.split('\n')
        for line in lines:
            if 'test_' in line and 'PASSED' in line:
                passed += 1
            elif 'test_' in line and 'FAILED' in line:
                failed += 1
            elif 'test_' in line and 'SKIPPED' in line:
                skipped += 1
            elif 'test_' in line and 'ERROR' in line:
                errors += 1

    return {
        "passed": passed,
        "failed": failed + errors,
        "skipped": skipped,
        "total": passed + failed + errors + skipped
    }

def parse_playwright_results(output):
    """Parse Playwright test output to extract test results."""
    passed = len(re.findall(r'✓', output))
    failed = len(re.findall(r'✗', output))
    skipped = len(re.findall(r'-', output))

    # If we're using mock data, make sure we count each line
    if passed == 0 and failed == 0 and skipped == 0:
        lines = output.split('\n')
        for line in lines:
            if line.startswith('✓'):
                passed += 1
            elif line.startswith('✗'):
                failed += 1
            elif line.startswith('-'):
                skipped += 1

    return {
        "passed": passed,
        "failed": failed,
        "skipped": skipped,
        "total": passed + failed + skipped
    }

def generate_mermaid_diagram(component_results, interface_results):
    """Generate a Mermaid sequence diagram based on test results."""
    diagram = ["```mermaid", "sequenceDiagram"]

    # Add participants for components
    for component, results in component_results.items():
        passed = results.get("passed", 0)
        failed = results.get("failed", 0)
        total = results.get("total", 0)
        if total > 0:
            pass_rate = int((passed / total) * 100)
            diagram.append(f"    participant {component} as {component}<br/>{passed}/{total} passed ({pass_rate}%)")

    # Add interactions between components based on interfaces
    # For schema tests, add fixed interactions to show the component relationships
    diagram.append(f"    Client->>api: API Schemas")
    diagram.append(f"    api->>minio: MinIO Schemas")
    diagram.append(f"    api->>vagrant: Vagrant Schemas")
    diagram.append(f"    api->>file_upload: File Upload Schemas")
    diagram.append(f"    file_upload->>vm_injection: VM Injection Schemas")

    # Add interactions from interface tests if available
    for interface, results in interface_results.items():
        passed = results.get("passed", 0)
        failed = results.get("failed", 0)
        total = results.get("total", 0)

        if total > 0 and interface != "api_schemas":
            pass_rate = int((passed / total) * 100)

            # Determine source and target components based on interface name
            if "api_routes" in interface:
                diagram.append(f"    Client->>api: API Routes<br/>{passed}/{total} passed ({pass_rate}%)")
            elif "api_services" in interface:
                diagram.append(f"    api->>minio: Service Calls<br/>{passed}/{total} passed ({pass_rate}%)")
                diagram.append(f"    api->>vagrant: Service Calls<br/>{passed}/{total} passed ({pass_rate}%)")
            elif "api_integration" in interface:
                diagram.append(f"    file_upload->>vm_injection: Integration<br/>{passed}/{total} passed ({pass_rate}%)")
            elif "ui_components" in interface:
                diagram.append(f"    Client->>ui: UI Components<br/>{passed}/{total} passed ({pass_rate}%)")
            elif "ui_workflows" in interface:
                diagram.append(f"    ui->>api: API Calls<br/>{passed}/{total} passed ({pass_rate}%)")

    diagram.append("```")
    return "\n".join(diagram)

def main():
    """Main function to run tests and generate diagram."""
    parser = argparse.ArgumentParser(description="Run tests and generate component diagram")
    parser.add_argument("--api-only", action="store_true", help="Run only API tests")
    parser.add_argument("--ui-only", action="store_true", help="Run only UI tests")
    parser.add_argument("--output", default="test_results.md", help="Output file for results")
    args = parser.parse_args()

    component_results = {}
    interface_results = {}

    # Run tests for each component
    if not args.ui_only:
        print("Running API component tests...")
        for component, test_paths in COMPONENTS.items():
            if component == "ui" and args.api_only:
                continue

            component_results[component] = {"passed": 0, "failed": 0, "skipped": 0, "total": 0}

            for test_path in test_paths:
                if os.path.exists(test_path):
                    if "playwright" in test_path:
                        stdout, stderr, returncode = run_playwright_tests(test_path)
                        results = parse_playwright_results(stdout)
                    else:
                        stdout, stderr, returncode = run_pytest(test_path)
                        results = parse_pytest_results(stdout)

                    component_results[component]["passed"] += results["passed"]
                    component_results[component]["failed"] += results["failed"]
                    component_results[component]["skipped"] += results["skipped"]
                    component_results[component]["total"] += results["total"]

    # Run tests for each interface
    if not args.ui_only:
        print("Running API interface tests...")
        for interface, test_paths in INTERFACES.items():
            if "ui_" in interface and args.api_only:
                continue

            interface_results[interface] = {"passed": 0, "failed": 0, "skipped": 0, "total": 0}

            for test_path in test_paths:
                if os.path.exists(test_path):
                    if "playwright" in test_path:
                        stdout, stderr, returncode = run_playwright_tests(test_path)
                        results = parse_playwright_results(stdout)
                    else:
                        stdout, stderr, returncode = run_pytest(test_path)
                        results = parse_pytest_results(stdout)

                    interface_results[interface]["passed"] += results["passed"]
                    interface_results[interface]["failed"] += results["failed"]
                    interface_results[interface]["skipped"] += results["skipped"]
                    interface_results[interface]["total"] += results["total"]

    # Generate diagram
    diagram = generate_mermaid_diagram(component_results, interface_results)

    # Generate summary
    summary = ["# Test Results Summary\n"]
    summary.append("## Component Test Results\n")
    summary.append("| Component | Passed | Failed | Skipped | Total | Pass Rate |")
    summary.append("|-----------|--------|--------|---------|-------|-----------|")

    for component, results in component_results.items():
        passed = results.get("passed", 0)
        failed = results.get("failed", 0)
        skipped = results.get("skipped", 0)
        total = results.get("total", 0)
        pass_rate = f"{int((passed / total) * 100)}%" if total > 0 else "N/A"

        summary.append(f"| {component} | {passed} | {failed} | {skipped} | {total} | {pass_rate} |")

    summary.append("\n## Interface Test Results\n")
    summary.append("| Interface | Passed | Failed | Skipped | Total | Pass Rate |")
    summary.append("|-----------|--------|--------|---------|-------|-----------|")

    for interface, results in interface_results.items():
        passed = results.get("passed", 0)
        failed = results.get("failed", 0)
        skipped = results.get("skipped", 0)
        total = results.get("total", 0)
        pass_rate = f"{int((passed / total) * 100)}%" if total > 0 else "N/A"

        summary.append(f"| {interface} | {passed} | {failed} | {skipped} | {total} | {pass_rate} |")

    summary.append("\n## Component Sequence Diagram\n")
    summary.append(diagram)

    # Write results to file
    with open(args.output, "w") as f:
        f.write("\n".join(summary))

    print(f"Results written to {args.output}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
