#!/bin/bash
# <PERSON>ript to run tests and save logs to files

# Create logs directory if it doesn't exist
mkdir -p logs

# Set timestamp for log files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Function to run tests and save output to log file
run_tests() {
    local test_type=$1
    local test_path=$2
    local log_file="logs/${test_type}_tests_${TIMESTAMP}.log"
    
    echo "Running ${test_type} tests..."
    echo "Test command: python -m pytest ${test_path} -v" | tee -a "$log_file"
    echo "----------------------------------------" | tee -a "$log_file"
    python -m pytest ${test_path} -v 2>&1 | tee -a "$log_file"
    local exit_code=${PIPESTATUS[0]}
    echo "----------------------------------------" | tee -a "$log_file"
    echo "Exit code: ${exit_code}" | tee -a "$log_file"
    echo "Log saved to: ${log_file}"
    echo ""
    
    return ${exit_code}
}

# Function to run all tests and save output to log file
run_all_tests() {
    local log_file="logs/all_tests_${TIMESTAMP}.log"
    
    echo "Running all tests..."
    echo "Test command: python -m pytest -v" | tee -a "$log_file"
    echo "----------------------------------------" | tee -a "$log_file"
    python -m pytest -v 2>&1 | tee -a "$log_file"
    local exit_code=${PIPESTATUS[0]}
    echo "----------------------------------------" | tee -a "$log_file"
    echo "Exit code: ${exit_code}" | tee -a "$log_file"
    echo "Log saved to: ${log_file}"
    echo ""
    
    return ${exit_code}
}

# Function to summarize test results
summarize_results() {
    local log_file="logs/test_summary_${TIMESTAMP}.log"
    
    echo "Test Summary" | tee -a "$log_file"
    echo "----------------------------------------" | tee -a "$log_file"
    
    # Extract test results from log files
    for f in logs/*_tests_${TIMESTAMP}.log; do
        local test_type=$(basename "$f" | cut -d'_' -f1)
        local exit_code=$(grep "Exit code:" "$f" | cut -d':' -f2 | tr -d ' ')
        local passed=$(grep -o "[0-9]* passed" "$f" | head -1)
        local failed=$(grep -o "[0-9]* failed" "$f" | head -1)
        local skipped=$(grep -o "[0-9]* skipped" "$f" | head -1)
        
        if [ -z "$passed" ]; then passed="0 passed"; fi
        if [ -z "$failed" ]; then failed="0 failed"; fi
        if [ -z "$skipped" ]; then skipped="0 skipped"; fi
        
        local status="PASSED"
        if [ "$exit_code" != "0" ]; then status="FAILED"; fi
        
        echo "${test_type} tests: ${status} (${passed}, ${failed}, ${skipped})" | tee -a "$log_file"
    done
    
    echo "----------------------------------------" | tee -a "$log_file"
    echo "Summary saved to: ${log_file}"
    echo ""
}

# Main execution
echo "Starting test run at $(date)"
echo "----------------------------------------"

# Run integration tests
run_tests "integration" "tests/integration/"
INTEGRATION_EXIT=$?

# Run unit tests
run_tests "unit" "tests/unit/"
UNIT_EXIT=$?

# Run API tests
run_tests "api" "api/tests/"
API_EXIT=$?

# Run all tests
run_all_tests
ALL_EXIT=$?

# Summarize results
summarize_results

echo "----------------------------------------"
echo "Test run completed at $(date)"

# Return overall status
if [ $INTEGRATION_EXIT -eq 0 ] && [ $UNIT_EXIT -eq 0 ] && [ $API_EXIT -eq 0 ] && [ $ALL_EXIT -eq 0 ]; then
    echo "All tests PASSED"
    exit 0
else
    echo "Some tests FAILED"
    exit 1
fi
