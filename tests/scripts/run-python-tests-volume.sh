#!/bin/bash

# Script to run Python tests with volume mounts instead of copying files
# This is more efficient and keeps the files in sync

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/python_tests_volume_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Do not exit on error since we want to run all tests
set +e

section "RUNNING PYTHON TESTS WITH VOLUME MOUNTS"
log "Starting Python test run at $(date)"
log "Log file: $LOG_FILE"

# Create the test container with volume mounts
section "CREATING TEST CONTAINER"
log "Creating Python test container with volume mounts..."

# Get namespace from config file if it exists
NAMESPACE="test"
CONFIG_FILE=".dockerwrapper/config/container-network.json"
if [ -f "$CONFIG_FILE" ]; then
    if command -v jq &> /dev/null; then
        NAMESPACE=$(jq -r '.docker.namespace // "test"' "$CONFIG_FILE")
        log "Using namespace from config: $NAMESPACE"
    else
        log "jq not found, using default namespace"
    fi
fi

# Delete any existing container
docker rm -f python-test-runner 2>/dev/null || true

# Create the container with volume mounts
docker run -d --name "${NAMESPACE}_python-test-runner" \
  -v "$(pwd):/app:ro" \
  -v "$(pwd)/test_logs:/app/test_logs:rw" \
  -e PYTHONUNBUFFERED=1 \
  -e TEST_MODE=true \
  -e "DATABASE_URL=sqlite:///:memory:" \
  -e "DATABASE_URL_ASYNC=sqlite+aiosqlite:///:memory:" \
  -e PYTHONPATH=/app \
  -e DOCKER_NAMESPACE="$NAMESPACE" \
  -e CONFIG_FILE="$CONFIG_FILE" \
  -w /app \
  python:3.10-slim \
  tail -f /dev/null

# Install required dependencies in the container
section "INSTALLING DEPENDENCIES"
log "Installing test dependencies in container..."

docker exec ${NAMESPACE}_python-test-runner pip install --no-cache-dir pytest pytest-asyncio pytest-mock pytest-cov \
  sqlalchemy sqlalchemy-utils aiosqlite pydantic fastapi uvicorn requests jq | tee -a "$LOG_FILE"

# Find all Python test files, excluding dependency directories
PYTHON_TEST_FILES=(
    $(find . -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__" | grep -v ".venv" | \
    grep -v ".pip_modules" | grep -v "node_modules" | grep -v "venv" | grep -v "site-packages" | grep -v ".focus_forge")
)

log "Found ${#PYTHON_TEST_FILES[@]} Python test files"

# Ask user if they want to continue with all tests
echo -n "Do you want to run all ${#PYTHON_TEST_FILES[@]} tests? This might take a while. (y/n): "
read -r response
if [[ "$response" =~ ^[Nn]$ ]]; then
    log "User chose not to run all tests. Exiting."
    docker rm -f python-test-runner 2>/dev/null || true
    exit 0
fi

# Set counters
TOTAL_TESTS=${#PYTHON_TEST_FILES[@]}
PASSED_TESTS=0
FAILED_TESTS=0
STREAMLIT_FAILS=0

# Create summary file
SUMMARY_FILE="$LOG_DIR/test_summary.txt"
echo "Python Test Summary" > $SUMMARY_FILE
echo "===================" >> $SUMMARY_FILE
echo "Run at: $(date)" >> $SUMMARY_FILE
echo "" >> $SUMMARY_FILE
echo "| Test File | Status | Notes |" >> $SUMMARY_FILE
echo "|-----------|--------|-------|" >> $SUMMARY_FILE

# Run each Python test
section "RUNNING PYTHON TESTS"
# Add counter for progress tracking
CURRENT_TEST=0
for test_file in "${PYTHON_TEST_FILES[@]}"; do
    CURRENT_TEST=$((CURRENT_TEST+1))
    log "[${CURRENT_TEST}/${TOTAL_TESTS}] Running test: ${test_file}"
    
    # Run the test with a timeout, ensuring we're in the right directory
    timeout 30s docker exec -w /app ${NAMESPACE}_python-test-runner python -m pytest "${test_file}" -v > "${LOG_DIR}/current_test.log" 2>&1
    exit_code=$?
    
    # Check if timeout occurred
    if [ $exit_code -eq 124 ]; then
        error "⚠ Test timed out after 30s: ${test_file}"
        echo "| ${test_file} | ⚠ TIMEOUT | Test took too long |" >> $SUMMARY_FILE
        echo "Test timed out after 30s" > "${LOG_DIR}/current_test.log"
        FAILED_TESTS=$((FAILED_TESTS+1))
    elif [ $exit_code -eq 0 ]; then
        success "✓ Test passed: ${test_file}"
        echo "| ${test_file} | ✓ PASS | |" >> $SUMMARY_FILE
        PASSED_TESTS=$((PASSED_TESTS+1))
    else
        error "✗ Test failed: ${test_file}"
        
        # Check if failure is due to Streamlit dependency
        if grep -q "streamlit\|ImportError: No module named 'streamlit'" "${LOG_DIR}/current_test.log"; then
            echo "| ${test_file} | ✗ FAIL | Streamlit dependency |" >> $SUMMARY_FILE
            STREAMLIT_FAILS=$((STREAMLIT_FAILS+1))
        else
            # Extract error message for more info
            error_msg=$(grep -A 2 "FAILED\|ERROR:" "${LOG_DIR}/current_test.log" | head -3 | tr -d '\n' | tr '\t' ' ' | sed 's/  */ /g')
            echo "| ${test_file} | ✗ FAIL | ${error_msg} |" >> $SUMMARY_FILE
        fi
        
        FAILED_TESTS=$((FAILED_TESTS+1))
    fi
    
    # Add to log file
    echo "===== TEST: ${test_file} =====" >> "$LOG_FILE"
    cat "${LOG_DIR}/current_test.log" >> "$LOG_FILE"
    echo "===============================" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
done

# Generate test summary
section "PYTHON TEST SUMMARY"
log "Tests completed at $(date)"
log "----------------------------------------------"
log "Passed: ${PASSED_TESTS} / ${TOTAL_TESTS}"
log "Failed: ${FAILED_TESTS} / ${TOTAL_TESTS}"
log "Failed due to Streamlit: ${STREAMLIT_FAILS} / ${TOTAL_TESTS}"
log "Pass rate: $((PASSED_TESTS * 100 / TOTAL_TESTS))%"
log "----------------------------------------------"
log "Detailed summary: ${SUMMARY_FILE}"

# Clean up
section "CLEANUP"
log "Cleaning up test container..."
docker rm -f ${NAMESPACE}_python-test-runner 2>/dev/null || true

# Create a separate streamlit fail log
STREAMLIT_FAIL_LOG="$LOG_DIR/streamlit_test_failures.txt"
echo "# Streamlit Test Failures" > $STREAMLIT_FAIL_LOG
echo "Tests that failed due to Streamlit dependencies and need to be converted to Flask." >> $STREAMLIT_FAIL_LOG
echo "" >> $STREAMLIT_FAIL_LOG
grep "Streamlit dependency" $SUMMARY_FILE >> $STREAMLIT_FAIL_LOG
echo "" >> $STREAMLIT_FAIL_LOG
log "Streamlit failures log: $STREAMLIT_FAIL_LOG"

exit 0 