#!/bin/bash

echo "Starting test environment setup..."

# Create necessary directories if they don't exist
mkdir -p test-results test_logs uploads logs

# Clean up any existing containers
docker compose -f docker-compose.test.yml down -v

# Build and start the test environment
echo "Building and starting test environment..."
docker compose -f docker-compose.test.yml up --build -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Run tests
echo "Running tests..."
docker compose -f docker-compose.test.yml logs -f test

# Clean up
echo "Cleaning up..."
docker compose -f docker-compose.test.yml down -v

echo "Test results are available in ./test-results directory" 