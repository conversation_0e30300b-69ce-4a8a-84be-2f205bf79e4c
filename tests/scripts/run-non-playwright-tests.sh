#!/bin/bash

# Non-Playwright Test Runner Script
# This script runs all tests except Playwright UI and API tests

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/non_playwright_test_run_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a "$LOG_FILE"
}

# Ensure the script exits on error
set -e

section "NON-PLAYWRIGHT TEST SUITE"
log "Starting non-Playwright test run at $(date)"
log "This will run all tests except Playwright UI and API tests"
log "Log file: $LOG_FILE"

# Create required directories with proper permissions
log "Creating required directories..."
mkdir -p test_screenshots test_results

# Initialize counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 1. Run Python Tests
section "RUNNING PYTHON TESTS"

# Find all Python test files
PYTHON_TEST_FILES=(
    $(find . -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__" | grep -v ".venv" | grep -v ".focus_forge")
)

log "Found ${#PYTHON_TEST_FILES[@]} Python test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#PYTHON_TEST_FILES[@]}))

# Set up Python environment - use system Python if venv fails
log "Setting up Python environment..."
PYTHON_CMD="python3"

# Try to set up virtual environment
if [ ! -d ".venv" ]; then
    log "Creating Python virtual environment..."
    $PYTHON_CMD -m venv .venv || log "Failed to create venv, will use system Python"
fi

# Try to activate virtual environment, fall back to system Python if it fails
if [ -f ".venv/bin/activate" ]; then
    log "Activating virtual environment..."
    source .venv/bin/activate && log "Using Python virtual environment" || log "Failed to activate venv, using system Python"
    
    # Install dependencies in virtual environment
    if [ -f "requirements.txt" ]; then
        log "Installing requirements..."
        pip install -r requirements.txt >> "$LOG_FILE" 2>&1 || log "Failed to install requirements, tests may fail"
    fi
else
    log "No virtual environment found, using system Python"
fi

# Run each Python test
PYTHON_PASSED=0
PYTHON_FAILED=0
for test_file in "${PYTHON_TEST_FILES[@]}"; do
    log "Running Python test: ${test_file}"
    
    if $PYTHON_CMD -m pytest "${test_file}" -v >> "$LOG_FILE" 2>&1; then
        success "✓ Python test passed: ${test_file}"
        PYTHON_PASSED=$((PYTHON_PASSED+1))
    else
        error "✗ Python test failed: ${test_file}"
        PYTHON_FAILED=$((PYTHON_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + PYTHON_PASSED))
FAILED_TESTS=$((FAILED_TESTS + PYTHON_FAILED))

# 2. Run Shell Script Tests
section "RUNNING SHELL SCRIPT TESTS"

# Find all shell test scripts
SHELL_TEST_FILES=(
    $(find scripts -type f -name "*test*.sh" | grep -v "run" | grep -v ".focus_forge")
)

log "Found ${#SHELL_TEST_FILES[@]} Shell test scripts"
TOTAL_TESTS=$((TOTAL_TESTS + ${#SHELL_TEST_FILES[@]}))

# Run each shell test
SHELL_PASSED=0
SHELL_FAILED=0
for test_file in "${SHELL_TEST_FILES[@]}"; do
    log "Running Shell test: ${test_file}"
    
    if bash "${test_file}" >> "$LOG_FILE" 2>&1; then
        success "✓ Shell test passed: ${test_file}"
        SHELL_PASSED=$((SHELL_PASSED+1))
    else
        error "✗ Shell test failed: ${test_file}"
        SHELL_FAILED=$((SHELL_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + SHELL_PASSED))
FAILED_TESTS=$((FAILED_TESTS + SHELL_FAILED))

# 3. Run NodeJS Tests (non-Playwright)
section "RUNNING NODEJS TESTS"

# Find all NodeJS test files (excluding Playwright tests)
NODE_TEST_FILES=(
    $(find . -type f -name "*test*.js" | grep -v "spec.js" | grep -v "node_modules" | grep -v "playwright" | grep -v ".focus_forge")
)

log "Found ${#NODE_TEST_FILES[@]} NodeJS test files"
TOTAL_TESTS=$((TOTAL_TESTS + ${#NODE_TEST_FILES[@]}))

# Run each NodeJS test
NODE_PASSED=0
NODE_FAILED=0
for test_file in "${NODE_TEST_FILES[@]}"; do
    log "Running NodeJS test: ${test_file}"
    
    if node "${test_file}" >> "$LOG_FILE" 2>&1; then
        success "✓ NodeJS test passed: ${test_file}"
        NODE_PASSED=$((NODE_PASSED+1))
    else
        error "✗ NodeJS test failed: ${test_file}"
        NODE_FAILED=$((NODE_FAILED+1))
    fi
done

PASSED_TESTS=$((PASSED_TESTS + NODE_PASSED))
FAILED_TESTS=$((FAILED_TESTS + NODE_FAILED))

# Generate comprehensive test summary
section "NON-PLAYWRIGHT TEST SUMMARY"
log "Python Tests: ${PYTHON_PASSED} passed, ${PYTHON_FAILED} failed (total: ${#PYTHON_TEST_FILES[@]})"
log "Shell Tests: ${SHELL_PASSED} passed, ${SHELL_FAILED} failed (total: ${#SHELL_TEST_FILES[@]})"
log "NodeJS Tests: ${NODE_PASSED} passed, ${NODE_FAILED} failed (total: ${#NODE_TEST_FILES[@]})"
log "---------------------------------------------"
log "TOTAL: ${PASSED_TESTS} passed, ${FAILED_TESTS} failed (total: ${TOTAL_TESTS})"

# Create HTML summary report
cat > non-playwright-test-summary.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Non-Playwright Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .summary { margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }
        .passed { color: green; }
        .failed { color: red; }
        .test-group { margin-bottom: 30px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .progress-bar-container { width: 100%; background-color: #e0e0e0; border-radius: 4px; }
        .progress-bar { height: 24px; background-color: #4CAF50; border-radius: 4px; text-align: center; line-height: 24px; color: white; }
    </style>
</head>
<body>
    <h1>Non-Playwright Test Results</h1>
    <p>Run completed at $(date)</p>
    
    <div class="summary">
        <h2>Overall Summary</h2>
        <div class="progress-bar-container">
            <div class="progress-bar" style="width: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%;">
                $(( PASSED_TESTS * 100 / TOTAL_TESTS ))% Passed
            </div>
        </div>
        <p class="passed">Passed: ${PASSED_TESTS}</p>
        <p class="failed">Failed: ${FAILED_TESTS}</p>
        <p>Total: ${TOTAL_TESTS}</p>
    </div>
    
    <div class="test-group">
        <h2>Test Results by Category</h2>
        <table>
            <tr>
                <th>Category</th>
                <th>Passed</th>
                <th>Failed</th>
                <th>Total</th>
                <th>Pass Rate</th>
            </tr>
            <tr>
                <td>Python Tests</td>
                <td class="passed">${PYTHON_PASSED}</td>
                <td class="failed">${PYTHON_FAILED}</td>
                <td>${#PYTHON_TEST_FILES[@]}</td>
                <td>$(( PYTHON_PASSED * 100 / ${#PYTHON_TEST_FILES[@]} ))%</td>
            </tr>
            <tr>
                <td>Shell Script Tests</td>
                <td class="passed">${SHELL_PASSED}</td>
                <td class="failed">${SHELL_FAILED}</td>
                <td>${#SHELL_TEST_FILES[@]}</td>
                <td>$(( SHELL_PASSED * 100 / ${#SHELL_TEST_FILES[@]} ))%</td>
            </tr>
            <tr>
                <td>NodeJS Tests</td>
                <td class="passed">${NODE_PASSED}</td>
                <td class="failed">${NODE_FAILED}</td>
                <td>${#NODE_TEST_FILES[@]}</td>
                <td>$(( NODE_PASSED * 100 / ${#NODE_TEST_FILES[@]} ))%</td>
            </tr>
        </table>
    </div>
    
    <div class="links">
        <h2>Detailed Reports</h2>
        <ul>
            <li><a href="${LOG_FILE}">Full Test Log</a></li>
        </ul>
    </div>
</body>
</html>
EOF

success "Non-Playwright test run completed!"
success "HTML summary: ${PWD}/non-playwright-test-summary.html"
success "Log file: ${LOG_FILE}"

# Exit with overall status
if [ $FAILED_TESTS -gt 0 ]; then
    error "Some tests failed. See logs for details."
    exit 1
else
    success "All tests passed successfully!"
    exit 0
fi 