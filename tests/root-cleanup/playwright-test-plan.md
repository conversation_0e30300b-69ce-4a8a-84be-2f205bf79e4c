# Product Requirements Document (PRD): API Testing Through UI with <PERSON><PERSON>

## 1. Executive Summary

This document outlines the requirements for comprehensive end-to-end testing of the TurdParty application API endpoints through the user interface using Playwright. The goal is to create a robust test suite that verifies API functionality by interacting with the UI, ensuring that backend API changes don't break frontend functionality.

## 2. Current State Assessment

### 2.1 Existing Playwright Test Coverage

The current Playwright tests provide basic coverage in the following areas:

- **Navigation Testing**: Tests verify that main navigation links and breadcrumbs work correctly.
- **Form Validation**: Tests check form field validation for VM creation and file uploads.
- **Basic Workflows**: Tests cover simple user journeys like VM creation and file upload.
- **UI Component Testing**: Tests verify that UI elements are present and functional.
- **State Management**: Some tests check that UI state is maintained between page navigations.

### 2.2 Coverage Gaps

Despite having several tests, there are significant gaps in the current test suite:

1. **Limited API Endpoint Coverage**: Most API endpoints are not systematically tested through the UI.
2. **Insufficient Error Handling Testing**: Few tests verify proper UI handling of API errors.
3. **Lacking Comprehensive Data Validation**: Tests don't thoroughly validate that API data is correctly displayed in the UI.
4. **Minimal Authentication/Authorization Testing**: Limited testing of protected endpoints and permission levels.
5. **No Performance Testing**: No verification of UI responsiveness during API operations.

### 2.3 Identified API Routes Requiring Testing

Based on the codebase analysis, these key API routes need UI-based testing:

1. **Authentication** (auth.py)
2. **File Upload/Storage** (file_upload.py, storage.py)
3. **VM Management** (vm.py, vagrant_vm.py)
4. **File to VM Operations** (file_to_vm.py, vm_injection.py)
5. **Health Checks** (health.py, minio_health.py)
6. **System Operations** (system.py)
7. **User Management** (users.py)
8. **Documentation** (docs.py)

## 3. Testing Objectives

### 3.1 Primary Goals

1. **Complete API Coverage**: Test all critical API endpoints through UI interactions.
2. **Workflow Validation**: Verify complete user journeys involving multiple API calls.
3. **Error Handling**: Validate proper UI responses to various API error conditions.
4. **Data Integrity**: Confirm API response data is correctly displayed in the UI.
5. **Cross-browser Consistency**: Ensure functionality works across supported browsers.

### 3.2 Success Metrics

1. **Coverage**: 100% of critical API endpoints tested through UI.
2. **Reliability**: Test suite runs with <5% flaky tests.
3. **Efficiency**: Complete test suite runs in under 15 minutes.
4. **Maintainability**: Tests follow a consistent pattern and are easily updated.

## 4. Detailed Requirements

### 4.1 Authentication Testing

#### 4.1.1 Login Flow
- Test valid login with correct credentials
- Test invalid login scenarios (wrong password, nonexistent user)
- Verify session persistence across page reloads
- Test logout functionality

#### 4.1.2 Authorization
- Verify restricted pages/actions based on user roles
- Test UI elements visibility based on permissions
- Verify API request failures when unauthorized

### 4.2 File Management Testing

#### 4.2.1 File Upload
- Test uploading files of various types and sizes
- Verify progress indicators and upload status
- Test cancellation of ongoing uploads
- Verify error handling for invalid files

#### 4.2.2 File Storage
- Test file listing and pagination
- Verify file metadata display
- Test file download functionality
- Test file deletion and confirmation dialogs

#### 4.2.3 File to VM Operations
- Test attaching files to VMs
- Verify VM-file associations in the UI
- Test detaching files from VMs

### 4.3 VM Management Testing

#### 4.3.1 VM Creation
- Test VM creation with various configurations
- Verify form validation for all VM parameters
- Test VM creation status indicators
- Verify new VM appears in listings

#### 4.3.2 VM Operations
- Test VM start/stop/restart operations
- Verify VM status changes are reflected in UI
- Test VM deletion with confirmation
- Verify VM configuration updates

#### 4.3.3 VM Status and Details
- Test VM status page filtering and sorting
- Verify VM details page shows correct information
- Test VM monitoring data display (if applicable)

### 4.4 Health and System Testing

#### 4.4.1 Health Checks
- Verify system health status display
- Test UI response to component failures
- Verify MinIO health status indicators

#### 4.4.2 System Operations
- Test system configuration changes through UI
- Verify system metrics display
- Test system notifications and alerts

### 4.5 Error Handling Testing

#### 4.5.1 Network Errors
- Test UI handling of API timeouts
- Verify error message display for network failures
- Test retry mechanisms for failed operations

#### 4.5.2 Server Errors
- Test UI handling of 500-series errors
- Verify appropriate error messages for server failures
- Test recovery flows after server errors

#### 4.5.3 Input Validation Errors
- Verify field-level error messages match API validation errors
- Test form resubmission after fixing validation errors

## 5. Technical Implementation

### 5.1 Test Structure

```
tests/
  playwright/
    auth/                 # Authentication tests
      login.spec.js
      authorization.spec.js
    file-management/      # File operations tests
      upload.spec.js
      storage.spec.js
      file-to-vm.spec.js
    vm-management/        # VM operation tests
      creation.spec.js
      operations.spec.js
      status.spec.js
    system/               # System functionality tests
      health.spec.js
      configuration.spec.js
    error-handling/       # Error handling tests
      network-errors.spec.js
      server-errors.spec.js
    utils/                # Test utilities
      auth-helpers.js
      test-data.js
      intercept-helpers.js
    fixtures/             # Test fixtures
      auth.fixture.js
      test-files/
```

### 5.2 Test Approach

#### 5.2.1 Page Object Model
Implement the Page Object Model (POM) pattern to create abstractions for UI pages and components:

```javascript
// Example Page Object for VM Creation
class VMCreationPage {
  constructor(page) {
    this.page = page;
    this.nameInput = page.locator('input[name="vm_name"]');
    this.memoryInput = page.locator('input[name="memory"]');
    this.submitButton = page.locator('button[type="submit"]');
    // etc.
  }

  async navigate() {
    await this.page.goto('/vm_injection');
  }

  async createVM(name, memory, cpu) {
    await this.nameInput.fill(name);
    await this.memoryInput.fill(String(memory));
    // Fill other fields
    await this.submitButton.click();
  }

  async getErrorMessage() {
    return this.page.locator('.error-message').textContent();
  }
}
```

#### 5.2.2 API Interception
Use Playwright's network interception to mock API responses for controlled testing:

```javascript
// Example API interception
test('shows error message when VM creation fails', async ({ page }) => {
  const vmPage = new VMCreationPage(page);
  
  // Mock failed API response
  await page.route('**/api/vm/create', route => {
    route.fulfill({
      status: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    });
  });
  
  await vmPage.navigate();
  await vmPage.createVM('Test VM', 2048, 2);
  
  // Verify error is displayed correctly
  const errorMsg = await vmPage.getErrorMessage();
  expect(errorMsg).toContain('Internal server error');
});
```

#### 5.2.3 Test Data Management
Create a system for managing test data:

```javascript
// test-data.js
export const testVMs = [
  { name: 'Test VM 1', memory: 2048, cpu: 2 },
  { name: 'Test VM 2', memory: 4096, cpu: 4 },
];

export const testFiles = [
  { name: 'test-file-1.iso', size: 1024, type: 'application/octet-stream' },
  { name: 'test-file-2.txt', size: 256, type: 'text/plain' },
];
```

### 5.3 Test Environment Requirements

- Docker-based isolated test environment
- MinIO mock service for storage testing
- Test database with pre-populated fixtures
- Network configuration for cross-container communication
- Consistent cleanup between test runs

## 6. Implementation Timeline

### 6.1 Phase 1: Framework Setup (Week 1)
- Implement Page Object Model structure
- Create test fixtures and utilities
- Setup test data management
- Configure network interception helpers

### 6.2 Phase 2: Authentication and Basic Functionality (Week 2)
- Implement login/logout tests
- Create basic navigation tests
- Build permission verification tests
- Implement form validation tests

### 6.3 Phase 3: Core Functionality (Weeks 3-4)
- Implement file management tests
- Create VM management tests
- Build file-to-VM operation tests
- Implement system health tests

### 6.4 Phase 4: Error Handling and Edge Cases (Week 5)
- Implement network error tests
- Create server error tests
- Build validation error tests
- Implement edge case scenarios

### 6.5 Phase 5: Integration and Optimization (Week 6)
- Combine tests into complete workflows
- Optimize test execution time
- Implement parallel test execution
- Create comprehensive reporting

## 7. Reporting and Monitoring

### 7.1 Test Reports
- HTML reports with test results and screenshots
- JSON reports for CI integration
- Failure analysis with annotated screenshots
- Performance metrics for test execution

### 7.2 CI/CD Integration
- GitHub Actions workflow for automated testing
- Fail PR checks on test failures
- Publish test reports as artifacts
- Alert system for test suite failures

## 8. Maintenance Plan

### 8.1 Ongoing Maintenance
- Weekly review of test failures
- Monthly review of test coverage
- Quarterly performance review of test suite

### 8.2 Test Evolution
- Update tests with UI changes
- Add tests for new features
- Refactor tests for improved reliability
- Remove obsolete tests

## 9. Conclusion

This testing strategy will provide comprehensive coverage of the TurdParty application's API functionality through UI testing with Playwright. By implementing this plan, we will achieve confidence in the application's reliability, ensure API changes don't break the UI, and create a maintainable test suite that scales with the application.

The approach balances breadth of coverage with depth of testing for critical pathways, while remaining efficient and maintainable. This will significantly improve the current test coverage, focusing specifically on ensuring API endpoints function correctly when accessed through the UI.

## 10. Starter Implementation

To begin implementing this test plan, we will first set up the basic framework structure and implement tests for the two most critical areas: file upload functionality and VM management. These areas represent the core functionality of the application and involve multiple API endpoints.

### 10.1 Initial Setup Tasks

1. **Create Base Framework**:
   ```bash
   # Setup directory structure
   mkdir -p tests/playwright/{auth,file-management,vm-management,system,error-handling,utils,fixtures,fixtures/test-files,page-objects}
   
   # Install Playwright
   npm install -D @playwright/test
   npx playwright install
   
   # Create configuration file
   touch tests/playwright/playwright.config.js
   ```

2. **Configure Base Playwright Settings**:
   Add the following to `playwright.config.js`:
   ```javascript
   // @ts-check
   const { defineConfig } = require('@playwright/test');

   module.exports = defineConfig({
     testDir: './',
     timeout: 60000,
     expect: {
       timeout: 10000
     },
     fullyParallel: false,
     retries: process.env.CI ? 2 : 1,
     workers: 2,
     reporter: [
       ['html', { outputFolder: 'test-results/html' }],
       ['json', { outputFile: 'test-results/results.json' }]
     ],
     use: {
       baseURL: process.env.BASE_URL || 'http://localhost:3100',
       trace: 'on-first-retry',
       screenshot: 'only-on-failure',
       video: 'on-first-retry'
     },
     projects: [
       {
         name: 'chromium',
         use: { browserName: 'chromium' }
       }
     ]
   });
   ```

3. **Create Base Page Object Class**:
   Create `tests/playwright/page-objects/base-page.js`:
   ```javascript
   // @ts-check
   class BasePage {
     /**
      * @param {import('@playwright/test').Page} page 
      */
     constructor(page) {
       this.page = page;
       this.baseUrl = process.env.BASE_URL || 'http://localhost:3100';
     }
     
     /**
      * Navigate to a specific path
      * @param {string} path 
      */
     async navigate(path) {
       await this.page.goto(`${this.baseUrl}${path}`);
     }
     
     /**
      * Wait for network requests to complete
      */
     async waitForNetworkIdle() {
       await this.page.waitForLoadState('networkidle');
     }
     
     /**
      * Get current page URL
      * @returns {Promise<string>}
      */
     async getCurrentUrl() {
       return this.page.url();
     }
     
     /**
      * Get error message from the page
      * @returns {Promise<string|null>}
      */
     async getErrorMessage() {
       const errorElem = await this.page.$('.error-message, .ant-message-error');
       if (errorElem) {
         return errorElem.textContent();
       }
       return null;
     }
     
     /**
      * Get success message from the page
      * @returns {Promise<string|null>}
      */
     async getSuccessMessage() {
       const successElem = await this.page.$('.success-message, .ant-message-success');
       if (successElem) {
         return successElem.textContent();
       }
       return null;
     }
   }
   
   module.exports = BasePage;
   ```

4. **Create Auth Utility**:
   Create `tests/playwright/utils/auth-helpers.js`:
   ```javascript
   // @ts-check
   /**
    * Helper function to handle authentication
    * @param {import('@playwright/test').Page} page 
    * @param {string} username 
    * @param {string} password 
    */
   async function login(page, username, password) {
     await page.goto('/login');
     await page.fill('input[name="username"]', username);
     await page.fill('input[name="password"]', password);
     await page.click('button[type="submit"]');
     await page.waitForLoadState('networkidle');
   }
   
   /**
    * Helper function to log out
    * @param {import('@playwright/test').Page} page 
    */
   async function logout(page) {
     await page.click('.logout-button, button:has-text("Logout")');
     await page.waitForLoadState('networkidle');
   }
   
   module.exports = { login, logout };
   ```

### 10.2 Implementation Priorities

The first functional tests to implement should be:

1. **File Upload Tests**:
   - Basic file upload functionality
   - File upload with validation
   - File upload error handling

2. **VM Management Tests**:
   - VM creation
   - VM status visualization
   - VM operations (start/stop)

3. **Integration Tests**:
   - File to VM attachment workflow
   - VM creation with file attachment

## Appendix A: Implementation Examples

### A.1 File Upload Test Implementation

#### A.1.1 File Upload Page Object (`tests/playwright/page-objects/file-upload-page.js`)

```javascript
// @ts-check
const BasePage = require('./base-page');

class FileUploadPage extends BasePage {
  /**
   * @param {import('@playwright/test').Page} page 
   */
  constructor(page) {
    super(page);
    this.uploadButton = page.locator('button:has-text("Upload")');
    this.fileInput = page.locator('input[type="file"]');
    this.descriptionInput = page.locator('textarea[name="description"]');
    this.fileTypeSelector = page.locator('select[name="fileType"]');
    this.submitButton = page.locator('button[type="submit"]');
    this.cancelButton = page.locator('button:has-text("Cancel")');
    this.progressBar = page.locator('.ant-progress-bar');
    this.fileList = page.locator('.file-list, .ant-table');
    this.fileItems = page.locator('.file-item, .ant-table-row');
  }
  
  /**
   * Navigate to file upload page
   */
  async navigate() {
    await super.navigate('/file_upload');
  }
  
  /**
   * Upload a file with description
   * @param {string} filePath 
   * @param {string} description 
   * @param {string} fileType 
   */
  async uploadFile(filePath, description, fileType) {
    await this.uploadButton.click();
    
    // Wait for upload modal to appear
    await this.page.waitForSelector('.ant-modal, .upload-modal');
    
    // Set file
    await this.fileInput.setInputFiles(filePath);
    
    // Set description if provided
    if (description) {
      await this.descriptionInput.fill(description);
    }
    
    // Set file type if provided
    if (fileType) {
      await this.fileTypeSelector.selectOption(fileType);
    }
    
    // Submit form
    await this.submitButton.click();
    
    // Wait for progress bar to complete or timeout after 30 seconds
    try {
      await this.page.waitForSelector('.ant-progress-status-success, .upload-success', { timeout: 30000 });
    } catch (e) {
      // If timeout occurs, check for error message
      const errorMessage = await this.getErrorMessage();
      if (errorMessage) {
        console.log(`Upload failed with error: ${errorMessage}`);
      }
      throw new Error('File upload timed out or failed');
    }
  }
  
  /**
   * Cancel an ongoing upload
   */
  async cancelUpload() {
    await this.cancelButton.click();
    await this.page.waitForSelector('.ant-progress-status-exception, .upload-cancelled');
  }
  
  /**
   * Get the count of files in the list
   * @returns {Promise<number>}
   */
  async getFileCount() {
    return this.fileItems.count();
  }
  
  /**
   * Get file details by index
   * @param {number} index 
   * @returns {Promise<{name: string, size: string, type: string, description: string}>}
   */
  async getFileDetails(index) {
    const item = this.fileItems.nth(index);
    
    return {
      name: await item.locator('.file-name, td:nth-child(1)').textContent() || '',
      size: await item.locator('.file-size, td:nth-child(2)').textContent() || '',
      type: await item.locator('.file-type, td:nth-child(3)').textContent() || '',
      description: await item.locator('.file-description, td:nth-child(4)').textContent() || ''
    };
  }
  
  /**
   * Delete a file by index
   * @param {number} index 
   * @param {boolean} confirm 
   */
  async deleteFile(index, confirm = true) {
    const item = this.fileItems.nth(index);
    await item.locator('button:has-text("Delete")').click();
    
    // Wait for confirmation dialog
    await this.page.waitForSelector('.ant-modal-confirm, .confirmation-dialog');
    
    if (confirm) {
      await this.page.click('.ant-btn-primary, button:has-text("Yes")');
      await this.page.waitForResponse(response => 
        response.url().includes('/api/file/delete') && response.status() === 200
      );
    } else {
      await this.page.click('.ant-btn-default, button:has-text("Cancel")');
    }
  }
}

module.exports = FileUploadPage;
```

#### A.1.2 File Upload Test Suite (`tests/playwright/file-management/upload.spec.js`)

```javascript
// @ts-check
const { test, expect } = require('@playwright/test');
const FileUploadPage = require('../page-objects/file-upload-page');
const { login } = require('../utils/auth-helpers');
const path = require('path');

// Define test file paths
const TEST_FILES = {
  txt: path.join(__dirname, '../fixtures/test-files/sample.txt'),
  pdf: path.join(__dirname, '../fixtures/test-files/sample.pdf'),
  iso: path.join(__dirname, '../fixtures/test-files/sample.iso')
};

test.describe('File Upload Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page, 'testuser', 'password123');
  });
  
  test('should upload a text file successfully', async ({ page }) => {
    const fileUploadPage = new FileUploadPage(page);
    await fileUploadPage.navigate();
    
    // Get initial file count
    const initialCount = await fileUploadPage.getFileCount();
    
    // Upload file
    await fileUploadPage.uploadFile(
      TEST_FILES.txt,
      'Test file upload description',
      'text'
    );
    
    // Verify success message
    const successMessage = await fileUploadPage.getSuccessMessage();
    expect(successMessage).toBeTruthy();
    expect(successMessage).toContain('successfully');
    
    // Verify file count increased
    const newCount = await fileUploadPage.getFileCount();
    expect(newCount).toBe(initialCount + 1);
    
    // Verify file details
    const fileDetails = await fileUploadPage.getFileDetails(0);
    expect(fileDetails.name).toContain('sample.txt');
    expect(fileDetails.description).toContain('Test file upload description');
  });
  
  test('should show error when uploading invalid file type', async ({ page }) => {
    // Mock response for invalid file type
    await page.route('**/api/file/upload', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Invalid file type',
          message: 'The file type is not allowed'
        })
      });
    });
    
    const fileUploadPage = new FileUploadPage(page);
    await fileUploadPage.navigate();
    
    // Try to upload file
    try {
      await fileUploadPage.uploadFile(
        TEST_FILES.iso,
        'This should fail',
        'iso'
      );
    } catch (e) {
      // Expected to fail
    }
    
    // Verify error message
    const errorMessage = await fileUploadPage.getErrorMessage();
    expect(errorMessage).toBeTruthy();
    expect(errorMessage).toContain('Invalid file type');
  });
  
  test('should allow cancellation of file upload', async ({ page }) => {
    // Mock slow upload response that never completes
    await page.route('**/api/file/upload', route => {
      // Hold the response and never fulfill it
      // The test will cancel before it completes
    });
    
    const fileUploadPage = new FileUploadPage(page);
    await fileUploadPage.navigate();
    
    // Start upload
    const uploadPromise = fileUploadPage.uploadFile(
      TEST_FILES.pdf,
      'This upload will be cancelled',
      'document'
    );
    
    // Wait for upload to start
    await page.waitForSelector('.ant-progress-bar, .upload-progress');
    
    // Cancel the upload
    await fileUploadPage.cancelUpload();
    
    // Verify upload was cancelled
    const errorMessage = await fileUploadPage.getErrorMessage();
    expect(errorMessage).toBeTruthy();
    expect(errorMessage).toContain('cancelled');
    
    // Clean up the hanging promise
    try {
      await uploadPromise;
    } catch (e) {
      // Expected to fail
    }
  });
  
  test('should delete file after confirmation', async ({ page }) => {
    const fileUploadPage = new FileUploadPage(page);
    await fileUploadPage.navigate();
    
    // Get initial file count
    const initialCount = await fileUploadPage.getFileCount();
    
    // Skip if no files to delete
    if (initialCount === 0) {
      test.skip('No files available to delete');
      return;
    }
    
    // Delete the first file
    await fileUploadPage.deleteFile(0, true);
    
    // Verify success message
    const successMessage = await fileUploadPage.getSuccessMessage();
    expect(successMessage).toBeTruthy();
    expect(successMessage).toContain('deleted');
    
    // Verify file count decreased
    const newCount = await fileUploadPage.getFileCount();
    expect(newCount).toBe(initialCount - 1);
  });
});
```

### A.2 VM Management Test Implementation

#### A.2.1 VM Creation Page Object (`tests/playwright/page-objects/vm-creation-page.js`)

```javascript
// @ts-check
const BasePage = require('./base-page');

class VMCreationPage extends BasePage {
  /**
   * @param {import('@playwright/test').Page} page 
   */
  constructor(page) {
    super(page);
    this.nameInput = page.locator('input[name="vm_name"]');
    this.memoryInput = page.locator('input[name="memory"]');
    this.cpuInput = page.locator('input[name="cpu"]');
    this.diskInput = page.locator('input[name="disk"]');
    this.osTypeSelect = page.locator('select[name="os_type"]');
    this.submitButton = page.locator('button[type="submit"]');
    this.advancedOptionsButton = page.locator('button:has-text("Advanced Options")');
    this.networkTypeSelect = page.locator('select[name="network_type"]');
    this.bootOrderSelect = page.locator('select[name="boot_order"]');
    this.statusAlert = page.locator('.status-alert, .ant-alert');
  }
  
  /**
   * Navigate to VM creation page
   */
  async navigate() {
    await super.navigate('/vm_injection');
  }
  
  /**
   * Create a VM with basic configuration
   * @param {string} name 
   * @param {number} memory 
   * @param {number} cpu 
   * @param {number} disk 
   * @param {string} osType 
   */
  async createVM(name, memory, cpu, disk, osType) {
    await this.nameInput.fill(name);
    await this.memoryInput.fill(String(memory));
    await this.cpuInput.fill(String(cpu));
    await this.diskInput.fill(String(disk));
    
    if (osType) {
      await this.osTypeSelect.selectOption(osType);
    }
    
    await this.submitButton.click();
    
    // Wait for response from API
    await this.page.waitForResponse(response => 
      response.url().includes('/api/vm/create') && 
      (response.status() === 200 || response.status() === 201)
    );
  }
  
  /**
   * Create VM with advanced options
   * @param {Object} vmConfig 
   */
  async createVMWithAdvancedOptions(vmConfig) {
    // Fill basic info
    await this.nameInput.fill(vmConfig.name);
    await this.memoryInput.fill(String(vmConfig.memory));
    await this.cpuInput.fill(String(vmConfig.cpu));
    await this.diskInput.fill(String(vmConfig.disk));
    
    if (vmConfig.osType) {
      await this.osTypeSelect.selectOption(vmConfig.osType);
    }
    
    // Open advanced options
    await this.advancedOptionsButton.click();
    
    // Fill advanced options
    if (vmConfig.networkType) {
      await this.networkTypeSelect.selectOption(vmConfig.networkType);
    }
    
    if (vmConfig.bootOrder) {
      await this.bootOrderSelect.selectOption(vmConfig.bootOrder);
    }
    
    // Submit form
    await this.submitButton.click();
    
    // Wait for response from API
    await this.page.waitForResponse(response => 
      response.url().includes('/api/vm/create') && 
      (response.status() === 200 || response.status() === 201)
    );
  }
  
  /**
   * Get form field validation error
   * @param {string} fieldName 
   * @returns {Promise<string|null>}
   */
  async getFieldError(fieldName) {
    const errorElem = await this.page.$(`.ant-form-item:has(input[name="${fieldName}"]) .ant-form-item-explain, input[name="${fieldName}"] + .error-message`);
    if (errorElem) {
      return errorElem.textContent();
    }
    return null;
  }
  
  /**
   * Get status message
   * @returns {Promise<string|null>}
   */
  async getStatusMessage() {
    const statusElem = await this.statusAlert;
    if (statusElem) {
      return statusElem.textContent();
    }
    return null;
  }
}

module.exports = VMCreationPage;
```

#### A.2.2 VM Status Page Object (`tests/playwright/page-objects/vm-status-page.js`)

```javascript
// @ts-check
const BasePage = require('./base-page');

class VMStatusPage extends BasePage {
  /**
   * @param {import('@playwright/test').Page} page 
   */
  constructor(page) {
    super(page);
    this.vmTable = page.locator('.vm-table, .ant-table');
    this.vmRows = page.locator('.vm-row, .ant-table-row');
    this.refreshButton = page.locator('button:has-text("Refresh")');
    this.filterInput = page.locator('input[placeholder*="filter"], input[placeholder*="search"]');
    this.statusFilter = page.locator('select[name="status-filter"]');
  }
  
  /**
   * Navigate to VM status page
   */
  async navigate() {
    await super.navigate('/vm_status');
  }
  
  /**
   * Refresh VM list
   */
  async refreshVMList() {
    await this.refreshButton.click();
    await this.page.waitForResponse(response => 
      response.url().includes('/api/vm/list') && response.status() === 200
    );
  }
  
  /**
   * Get count of VMs
   * @returns {Promise<number>}
   */
  async getVMCount() {
    return this.vmRows.count();
  }
  
  /**
   * Filter VMs by name
   * @param {string} name 
   */
  async filterByName(name) {
    await this.filterInput.fill(name);
    // Wait for filter to apply
    await this.page.waitForTimeout(500);
  }
  
  /**
   * Filter VMs by status
   * @param {string} status 
   */
  async filterByStatus(status) {
    await this.statusFilter.selectOption(status);
    // Wait for filter to apply
    await this.page.waitForTimeout(500);
  }
  
  /**
   * Get VM details by index
   * @param {number} index 
   * @returns {Promise<{name: string, status: string, memory: string, cpu: string}>}
   */
  async getVMDetails(index) {
    const row = this.vmRows.nth(index);
    
    return {
      name: await row.locator('td:nth-child(1)').textContent() || '',
      status: await row.locator('td:nth-child(2)').textContent() || '',
      memory: await row.locator('td:nth-child(3)').textContent() || '',
      cpu: await row.locator('td:nth-child(4)').textContent() || ''
    };
  }
  
  /**
   * Check if VM with name exists
   * @param {string} name 
   * @returns {Promise<boolean>}
   */
  async vmExists(name) {
    const nameCell = this.page.locator(`td:has-text("${name}")`);
    return await nameCell.count() > 0;
  }
  
  /**
   * Start VM by index
   * @param {number} index 
   */
  async startVM(index) {
    const row = this.vmRows.nth(index);
    await row.locator('button:has-text("Start")').click();
    
    // Wait for confirmation dialog
    await this.page.waitForSelector('.ant-modal-confirm, .confirmation-dialog');
    await this.page.click('.ant-btn-primary, button:has-text("Yes")');
    
    // Wait for API response
    await this.page.waitForResponse(response => 
      response.url().includes('/api/vm/start') && response.status() === 200
    );
  }
  
  /**
   * Stop VM by index
   * @param {number} index 
   */
  async stopVM(index) {
    const row = this.vmRows.nth(index);
    await row.locator('button:has-text("Stop")').click();
    
    // Wait for confirmation dialog
    await this.page.waitForSelector('.ant-modal-confirm, .confirmation-dialog');
    await this.page.click('.ant-btn-primary, button:has-text("Yes")');
    
    // Wait for API response
    await this.page.waitForResponse(response => 
      response.url().includes('/api/vm/stop') && response.status() === 200
    );
  }
  
  /**
   * Delete VM by index
   * @param {number} index 
   */
  async deleteVM(index) {
    const row = this.vmRows.nth(index);
    await row.locator('button:has-text("Delete")').click();
    
    // Wait for confirmation dialog
    await this.page.waitForSelector('.ant-modal-confirm, .confirmation-dialog');
    await this.page.click('.ant-btn-primary, button:has-text("Yes")');
    
    // Wait for API response
    await this.page.waitForResponse(response => 
      response.url().includes('/api/vm/delete') && response.status() === 200
    );
  }
}

module.exports = VMStatusPage;
```

#### A.2.3 VM Management Test Suite (`tests/playwright/vm-management/operations.spec.js`)

```javascript
// @ts-check
const { test, expect } = require('@playwright/test');
const VMCreationPage = require('../page-objects/vm-creation-page');
const VMStatusPage = require('../page-objects/vm-status-page');
const { login } = require('../utils/auth-helpers');

test.describe('VM Management Operations', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page, 'testuser', 'password123');
  });
  
  test('should create a new VM and verify it appears in the status list', async ({ page }) => {
    // Generate unique VM name
    const vmName = `Test-VM-${Date.now()}`;
    
    // Create new VM
    const vmCreationPage = new VMCreationPage(page);
    await vmCreationPage.navigate();
    await vmCreationPage.createVM(vmName, 2048, 2, 20, 'Linux');
    
    // Verify success message
    const successMessage = await vmCreationPage.getSuccessMessage();
    expect(successMessage).toBeTruthy();
    expect(successMessage).toContain('created');
    
    // Go to VM status page
    const vmStatusPage = new VMStatusPage(page);
    await vmStatusPage.navigate();
    
    // Refresh VM list to ensure new VM appears
    await vmStatusPage.refreshVMList();
    
    // Filter by name to find our VM
    await vmStatusPage.filterByName(vmName);
    
    // Verify VM exists
    const vmExists = await vmStatusPage.vmExists(vmName);
    expect(vmExists).toBeTruthy();
    
    // Check VM details
    const vmCount = await vmStatusPage.getVMCount();
    if (vmCount > 0) {
      const vmDetails = await vmStatusPage.getVMDetails(0);
      expect(vmDetails.name).toContain(vmName);
      expect(vmDetails.memory).toContain('2048');
      expect(vmDetails.cpu).toContain('2');
    }
  });
  
  test('should start and stop a VM', async ({ page }) => {
    const vmStatusPage = new VMStatusPage(page);
    await vmStatusPage.navigate();
    
    // Get VM count
    const vmCount = await vmStatusPage.getVMCount();
    
    // Skip if no VMs available
    if (vmCount === 0) {
      test.skip('No VMs available for testing');
      return;
    }
    
    // Filter to show only stopped VMs
    await vmStatusPage.filterByStatus('stopped');
    
    // Get count of stopped VMs
    const stoppedVMCount = await vmStatusPage.getVMCount();
    
    // If stopped VMs exist, start one
    if (stoppedVMCount > 0) {
      // Get VM details before operation
      const vmDetails = await vmStatusPage.getVMDetails(0);
      const vmName = vmDetails.name;
      
      // Start the VM
      await vmStatusPage.startVM(0);
      
      // Verify success message
      const startSuccessMessage = await vmStatusPage.getSuccessMessage();
      expect(startSuccessMessage).toBeTruthy();
      expect(startSuccessMessage).toContain('start');
      
      // Refresh and filter for running VMs
      await vmStatusPage.refreshVMList();
      await vmStatusPage.filterByStatus('running');
      
      // Verify the VM is now running
      await vmStatusPage.filterByName(vmName);
      const runningVMDetails = await vmStatusPage.getVMDetails(0);
      expect(runningVMDetails.status).toContain('running');
      
      // Now stop the VM
      await vmStatusPage.stopVM(0);
      
      // Verify success message
      const stopSuccessMessage = await vmStatusPage.getSuccessMessage();
      expect(stopSuccessMessage).toBeTruthy();
      expect(stopSuccessMessage).toContain('stop');
      
      // Refresh and filter for stopped VMs
      await vmStatusPage.refreshVMList();
      await vmStatusPage.filterByStatus('stopped');
      
      // Verify the VM is now stopped
      await vmStatusPage.filterByName(vmName);
      const stoppedVMDetails = await vmStatusPage.getVMDetails(0);
      expect(stoppedVMDetails.status).toContain('stopped');
    } else {
      // Filter to show only running VMs
      await vmStatusPage.filterByStatus('running');
      
      // Get count of running VMs
      const runningVMCount = await vmStatusPage.getVMCount();
      
      // If running VMs exist, stop one
      if (runningVMCount > 0) {
        // Get VM details before operation
        const vmDetails = await vmStatusPage.getVMDetails(0);
        const vmName = vmDetails.name;
        
        // Stop the VM
        await vmStatusPage.stopVM(0);
        
        // Verify success message
        const stopSuccessMessage = await vmStatusPage.getSuccessMessage();
        expect(stopSuccessMessage).toBeTruthy();
        expect(stopSuccessMessage).toContain('stop');
        
        // Refresh and filter for stopped VMs
        await vmStatusPage.refreshVMList();
        await vmStatusPage.filterByStatus('stopped');
        
        // Verify the VM is now stopped
        await vmStatusPage.filterByName(vmName);
        const stoppedVMDetails = await vmStatusPage.getVMDetails(0);
        expect(stoppedVMDetails.status).toContain('stopped');
      } else {
        test.skip('No suitable VMs available for testing start/stop operations');
      }
    }
  });
  
  test('should delete a VM', async ({ page }) => {
    const vmStatusPage = new VMStatusPage(page);
    await vmStatusPage.navigate();
    
    // Create unique test VM
    const vmName = `Delete-VM-${Date.now()}`;
    const vmCreationPage = new VMCreationPage(page);
    await vmCreationPage.navigate();
    await vmCreationPage.createVM(vmName, 1024, 1, 10, 'Linux');
    
    // Go back to status page
    await vmStatusPage.navigate();
    await vmStatusPage.refreshVMList();
    
    // Filter to find our test VM
    await vmStatusPage.filterByName(vmName);
    
    // Verify VM exists
    const vmExists = await vmStatusPage.vmExists(vmName);
    expect(vmExists).toBeTruthy();
    
    // Delete the VM
    await vmStatusPage.deleteVM(0);
    
    // Verify success message
    const deleteSuccessMessage = await vmStatusPage.getSuccessMessage();
    expect(deleteSuccessMessage).toBeTruthy();
    expect(deleteSuccessMessage).toContain('delete');
    
    // Refresh and check VM no longer exists
    await vmStatusPage.refreshVMList();
    await vmStatusPage.filterByName(vmName);
    const vmExistsAfterDelete = await vmStatusPage.vmExists(vmName);
    expect(vmExistsAfterDelete).toBeFalsy();
  });
  
  test('should validate VM creation form fields', async ({ page }) => {
    const vmCreationPage = new VMCreationPage(page);
    await vmCreationPage.navigate();
    
    // Submit empty form
    await vmCreationPage.submitButton.click();
    
    // Verify validation errors
    const nameError = await vmCreationPage.getFieldError('vm_name');
    expect(nameError).toBeTruthy();
    expect(nameError).toContain('required');
    
    const memoryError = await vmCreationPage.getFieldError('memory');
    expect(memoryError).toBeTruthy();
    
    // Try invalid values
    await vmCreationPage.nameInput.fill('a');  // Too short
    await vmCreationPage.memoryInput.fill('-100');  // Negative number
    await vmCreationPage.cpuInput.fill('999');  // Too high
    
    // Submit form
    await vmCreationPage.submitButton.click();
    
    // Verify validation errors for invalid values
    const nameInvalidError = await vmCreationPage.getFieldError('vm_name');
    expect(nameInvalidError).toBeTruthy();
    expect(nameInvalidError).toContain('length');
    
    const memoryInvalidError = await vmCreationPage.getFieldError('memory');
    expect(memoryInvalidError).toBeTruthy();
    expect(memoryInvalidError).toContain('positive');
    
    const cpuInvalidError = await vmCreationPage.getFieldError('cpu');
    expect(cpuInvalidError).toBeTruthy();
    expect(cpuInvalidError).toContain('maximum');
  });
});
```

The filename for this test plan document is: `playwright-test-plan.md` 