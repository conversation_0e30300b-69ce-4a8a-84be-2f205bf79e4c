#!/usr/bin/env python3
import os
import sys
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Run a specific test with verbose output to diagnose failures')
    parser.add_argument('test_path', help='Path to the test file to run')
    parser.add_argument('--venv', help='Path to virtual environment to use', default=None)
    args = parser.parse_args()
    
    if not os.path.exists(args.test_path):
        print(f"Error: Test file {args.test_path} does not exist")
        sys.exit(1)
    
    cmd = []
    
    # Use virtual environment if specified
    if args.venv:
        if os.path.exists(args.venv):
            activate_script = os.path.join(args.venv, 'bin', 'activate')
            cmd.extend(['source', activate_script, '&&'])
        else:
            print(f"Warning: Virtual environment {args.venv} not found, using system Python")
    
    # Run the test with maximum verbosity
    cmd.extend(['python', '-m', 'pytest', args.test_path, '-v', '--no-header', '--showlocals'])
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(' '.join(cmd), shell=True, capture_output=True, text=True)
    
    # Print output
    print("\n=== STDOUT ===")
    print(result.stdout)
    
    print("\n=== STDERR ===")
    print(result.stderr)
    
    print(f"\nExit code: {result.returncode}")
    
if __name__ == "__main__":
    main() 