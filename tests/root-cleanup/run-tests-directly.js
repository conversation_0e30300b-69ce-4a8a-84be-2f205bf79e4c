#!/usr/bin/env node

/**
 * Direct Test Runner for TurdParty
 * 
 * This script runs the Playwright tests directly without using Docker containers,
 * making it easier to debug and fix failing tests.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testDir: './tests',
  reportDir: './playwright-report',
  screenshotDir: './test_screenshots',
  resultsDir: './test-results',
  performanceDir: './test_results/performance',
  defaultTimeout: 60000 // 1 minute
};

// Create necessary directories
function ensureDirectoriesExist() {
  const dirs = [
    config.reportDir, 
    config.screenshotDir,
    config.resultsDir,
    config.performanceDir
  ];
  
  for (const dir of dirs) {
    if (!fs.existsSync(dir)) {
      console.log(`Creating directory: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
    }
  }
}

// Print welcome message
function printWelcomeMessage() {
  console.log('\n▶️ TurdParty Test Runner');
  console.log('===========================');
  console.log(`📁 Test directory: ${config.testDir}`);
  console.log(`📊 Report directory: ${config.reportDir}`);
  console.log(`🖼️ Screenshot directory: ${config.screenshotDir}`);
  console.log(`⏱️ Default timeout: ${config.defaultTimeout}ms`);
  console.log('===========================\n');
}

// Install dependencies if needed
function installDependencies() {
  console.log('📦 Checking for required dependencies...');
  
  try {
    execSync('npm list @playwright/test', { stdio: 'ignore' });
    console.log('✅ Playwright test dependencies found.');
  } catch (error) {
    console.log('⚠️ Playwright test dependencies not found. Installing...');
    
    try {
      execSync('npm install @playwright/test', { stdio: 'inherit' });
      console.log('✅ Playwright test dependencies installed.');
    } catch (installError) {
      console.error('❌ Failed to install dependencies:', installError.message);
      process.exit(1);
    }
  }
  
  // Install browser binaries if needed
  try {
    console.log('🔍 Checking for browser binaries...');
    execSync('npx playwright --version', { stdio: 'ignore' });
    console.log('✅ Browser binaries already installed.');
  } catch (error) {
    console.log('⚠️ Installing Playwright browsers...');
    try {
      execSync('npx playwright install chromium', { stdio: 'inherit' });
      console.log('✅ Browser setup complete.');
    } catch (browserError) {
      console.error('❌ Failed to install browser:', browserError.message);
      console.warn('Tests may fail without browser binaries.');
    }
  }
}

// Run a specific test file
async function runTest(testFile) {
  return new Promise((resolve) => {
    console.log(`🧪 Running test: ${testFile}`);
    
    // Build command arguments
    const args = [
      'npx', 
      'playwright', 
      'test', 
      testFile,
      '--reporter=html'
    ];
    
    // Run the test
    const testProcess = spawn(args[0], args.slice(1), {
      env: {
        ...process.env,
        PLAYWRIGHT_DEFAULT_TIMEOUT: config.defaultTimeout,
        NODE_ENV: 'test'
      },
      stdio: 'inherit',
      shell: true
    });
    
    testProcess.on('error', (error) => {
      console.error(`❌ Error running test ${testFile}:`, error);
      resolve(false);
    });
    
    testProcess.on('close', (code) => {
      const passed = code === 0;
      if (passed) {
        console.log(`✅ Test ${testFile} passed!`);
      } else {
        console.error(`❌ Test ${testFile} failed with code ${code}`);
      }
      resolve(passed);
    });
  });
}

// Get all test files
function getTestFiles() {
  try {
    return fs.readdirSync(config.testDir)
      .filter(file => file.endsWith('.spec.js') || file.endsWith('.test.js'))
      .map(file => path.join(config.testDir, file));
  } catch (error) {
    console.error('❌ Error reading test directory:', error);
    return [];
  }
}

// Main function
async function main() {
  printWelcomeMessage();
  ensureDirectoriesExist();
  installDependencies();
  
  const testFiles = getTestFiles();
  if (testFiles.length === 0) {
    console.error('❌ No test files found in', config.testDir);
    process.exit(1);
  }
  
  console.log(`🧪 Found ${testFiles.length} test files.`);
  
  // Track results
  const results = {
    total: testFiles.length,
    passed: 0,
    failed: 0,
    skipped: 0
  };
  
  const failedTests = [];
  
  // Run tests sequentially to avoid resource conflicts
  for (const testFile of testFiles) {
    try {
      const passed = await runTest(testFile);
      if (passed) {
        results.passed++;
      } else {
        results.failed++;
        failedTests.push(path.basename(testFile));
      }
    } catch (error) {
      console.error(`❌ Error executing ${testFile}:`, error);
      results.failed++;
      failedTests.push(path.basename(testFile));
    }
  }
  
  // Print summary
  console.log('\n📊 Test Summary');
  console.log('===========================');
  console.log(`✅ Passed: ${results.passed} tests`);
  console.log(`❌ Failed: ${results.failed} tests`);
  console.log(`⏭️ Skipped: ${results.skipped} tests`);
  console.log(`📈 Pass rate: ${Math.round((results.passed / results.total) * 100)}%`);
  
  if (failedTests.length > 0) {
    console.log('\n❌ Failed tests:');
    failedTests.forEach((test, index) => {
      console.log(`  ${index + 1}. ${test}`);
    });
  }
  
  console.log('\n📝 Full test report available at:');
  console.log(`  ${path.resolve(config.reportDir, 'index.html')}`);
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Run the script
main().catch(error => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
}); 