#!/usr/bin/env python3
"""
Standalone API route testing script.
Does not depend on the UI services module.
"""
import sys
import logging
import json
import requests
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import argparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class Item:
    """Data class for item objects."""
    id: int
    name: str
    description: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None


class ItemService:
    """Standalone service for item-related API endpoints."""
    
    def __init__(self, base_url: str, timeout: int = 30):
        """Initialize the item service."""
        self.base_url = base_url.rstrip('/')
        # Check if the base URL already contains the /api/v1 prefix
        if '/api/v1' in self.base_url:
            self.endpoint = "items"
        else:
            self.endpoint = "api/v1/items"
        self.timeout = timeout
        # Keep track of the authentication token
        self.auth_token = None
    
    def authenticate(self, username: str = "admin", password: str = "admin") -> bool:
        """Authenticate with the API."""
        # Check all potential auth endpoint patterns
        potential_endpoints = [
            f"{self.base_url.replace('api/v1', '')}/api/v1/auth/login",
            f"{self.base_url.replace('api/v1', '')}/auth/login",
            f"{self.base_url.replace('api/v1', '')}/login",
            f"{self.base_url}/auth/login"
        ]
        
        for endpoint in potential_endpoints:
            try:
                logger.info(f"Trying authentication endpoint: {endpoint}")
                response = requests.post(
                    endpoint, 
                    json={"username": username, "password": password},
                    timeout=self.timeout
                )
                
                if response.status_code >= 400:
                    logger.warning(f"Authentication failed at {endpoint}: {response.status_code} - {response.text}")
                    continue
                    
                data = response.json()
                if "access_token" in data:
                    self.auth_token = data.get("access_token")
                    logger.info(f"Successfully authenticated with token: {self.auth_token[:10]}...")
                    return True
                else:
                    logger.warning(f"Authentication response did not contain access_token: {data}")
            except Exception as e:
                logger.warning(f"Error during authentication at {endpoint}: {str(e)}")
                
        # If we reach here, all authentication attempts failed
        logger.warning("All authentication attempts failed. Proceeding without authentication.")
        return False
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers with authentication token if available."""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    def _build_url(self, path: str) -> str:
        """Build a full URL."""
        path = path.lstrip('/')
        return f"{self.base_url}/{path}"
    
    def get_items(self, skip: int = 0, limit: int = 100, name: Optional[str] = None) -> List[Item]:
        """Get a list of items."""
        url = self._build_url(self.endpoint)
        params = {"skip": skip, "limit": limit}
        if name:
            params["name"] = name
            
        try:
            response = requests.get(
                url, 
                params=params, 
                headers=self._get_headers(),
                timeout=self.timeout
            )
            if response.status_code >= 400:
                logger.error(f"Error getting items: {response.status_code} - {response.text}")
                return []
                
            data = response.json()
            return [Item(**item) for item in data]
        except Exception as e:
            logger.error(f"Error getting items: {str(e)}")
            return []
    
    def get_item(self, item_id: int) -> Optional[Item]:
        """Get a specific item by ID."""
        url = self._build_url(f"{self.endpoint}/{item_id}")
        
        try:
            response = requests.get(
                url, 
                headers=self._get_headers(),
                timeout=self.timeout
            )
            if response.status_code >= 400:
                logger.error(f"Error getting item {item_id}: {response.status_code} - {response.text}")
                return None
                
            data = response.json()
            return Item(**data)
        except Exception as e:
            logger.error(f"Error getting item {item_id}: {str(e)}")
            return None
    
    def create_item(self, name: str, description: Optional[str] = None) -> Optional[Item]:
        """Create a new item."""
        url = self._build_url(self.endpoint)
        data = {"name": name}
        if description:
            data["description"] = description
            
        try:
            response = requests.post(
                url, 
                json=data, 
                headers=self._get_headers(),
                timeout=self.timeout
            )
            if response.status_code >= 400:
                logger.error(f"Error creating item: {response.status_code} - {response.text}")
                return None
                
            data = response.json()
            return Item(**data)
        except Exception as e:
            logger.error(f"Error creating item: {str(e)}")
            return None
    
    def update_item(self, item_id: int, name: Optional[str] = None, description: Optional[str] = None) -> Optional[Item]:
        """Update an existing item."""
        url = self._build_url(f"{self.endpoint}/{item_id}")
        data = {}
        if name:
            data["name"] = name
        if description is not None:  # Allow empty string
            data["description"] = description
            
        try:
            response = requests.put(
                url, 
                json=data, 
                headers=self._get_headers(),
                timeout=self.timeout
            )
            if response.status_code >= 400:
                logger.error(f"Error updating item {item_id}: {response.status_code} - {response.text}")
                return None
                
            data = response.json()
            return Item(**data)
        except Exception as e:
            logger.error(f"Error updating item {item_id}: {str(e)}")
            return None
    
    def delete_item(self, item_id: int) -> bool:
        """Delete an item."""
        url = self._build_url(f"{self.endpoint}/{item_id}")
        
        try:
            response = requests.delete(
                url, 
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return response.status_code < 400
        except Exception as e:
            logger.error(f"Error deleting item {item_id}: {str(e)}")
            return False


def test_item_routes(api_url: str, username: str = "admin", password: str = "password") -> Dict[str, Any]:
    """
    Test all item-related API routes.
    
    Args:
        api_url: Base URL for the API
        username: Username for authentication
        password: Password for authentication
        
    Returns:
        Dictionary with test results
    """
    results = {
        "success": True,
        "tests": {
            "authentication": {"status": "not run"},
            "get_items": {"status": "not run"},
            "create_item": {"status": "not run"},
            "get_item": {"status": "not run"},
            "update_item": {"status": "not run"},
            "delete_item": {"status": "not run"}
        }
    }
    
    logger.info(f"Testing API routes at {api_url}")
    item_service = ItemService(api_url)
    item_id = None
    
    try:
        # Try authentication if needed
        try:
            logger.info(f"Attempting authentication with username: {username}")
            auth_result = item_service.authenticate(username, password)
            if auth_result:
                results["tests"]["authentication"] = {
                    "status": "success"
                }
                logger.info("Authentication successful")
            else:
                # Try with test mode credentials
                logger.info("Trying with test mode credentials...")
                auth_result = item_service.authenticate("testuser", "testpassword")
                if auth_result:
                    results["tests"]["authentication"] = {
                        "status": "success"
                    }
                    logger.info("Authentication successful with test credentials")
                else:
                    # Try with a different set of credentials
                    logger.info("Trying with alternative credentials...")
                    auth_result = item_service.authenticate("admin", "admin")
                    if auth_result:
                        results["tests"]["authentication"] = {
                            "status": "success"
                        }
                        logger.info("Authentication successful with alternative credentials")
                    else:
                        logger.warning("Could not authenticate. Continuing without authentication.")
                        results["tests"]["authentication"] = {
                            "status": "skipped",
                            "warning": "Could not authenticate - trying without authentication"
                        }
        except Exception as e:
            logger.warning(f"Authentication unavailable: {str(e)}")
            results["tests"]["authentication"] = {
                "status": "skipped",
                "warning": f"Authentication unavailable: {str(e)}"
            }
        
        # Test GET /items
        logger.info("Testing GET /items")
        items = item_service.get_items()
        results["tests"]["get_items"] = {
            "status": "success",
            "items_count": len(items)
        }
        logger.info(f"Found {len(items)} items")
        
        # Test POST /items
        logger.info("Testing POST /items")
        new_item = item_service.create_item(
            name="Test Item",
            description="Created during route testing"
        )
        if new_item and new_item.id:
            item_id = new_item.id
            results["tests"]["create_item"] = {
                "status": "success",
                "item_id": item_id
            }
            logger.info(f"Created item with ID {item_id}")
        else:
            results["success"] = False
            results["tests"]["create_item"] = {
                "status": "failed",
                "error": "Could not create item"
            }
            logger.error("Failed to create item")
            return results
        
        # Test GET /items/{id}
        logger.info(f"Testing GET /items/{item_id}")
        item = item_service.get_item(item_id)
        if item and item.id == item_id:
            results["tests"]["get_item"] = {
                "status": "success",
                "item_name": item.name
            }
            logger.info(f"Got item: {item.name}")
        else:
            results["success"] = False
            results["tests"]["get_item"] = {
                "status": "failed",
                "error": "Could not get item"
            }
            logger.error(f"Failed to get item {item_id}")
            return results
        
        # Test PUT /items/{id}
        logger.info(f"Testing PUT /items/{item_id}")
        updated_item = item_service.update_item(
            item_id=item_id,
            name="Updated Test Item",
            description="Updated during route testing"
        )
        if updated_item and updated_item.name == "Updated Test Item":
            results["tests"]["update_item"] = {
                "status": "success",
                "item_name": updated_item.name
            }
            logger.info(f"Updated item: {updated_item.name}")
        else:
            results["success"] = False
            results["tests"]["update_item"] = {
                "status": "failed",
                "error": "Could not update item"
            }
            logger.error(f"Failed to update item {item_id}")
            return results
        
        # Test DELETE /items/{id}
        logger.info(f"Testing DELETE /items/{item_id}")
        delete_result = item_service.delete_item(item_id)
        if delete_result:
            results["tests"]["delete_item"] = {
                "status": "success"
            }
            logger.info(f"Deleted item {item_id}")
        else:
            results["success"] = False
            results["tests"]["delete_item"] = {
                "status": "failed",
                "error": "Could not delete item"
            }
            logger.error(f"Failed to delete item {item_id}")
            
    except Exception as e:
        logger.error(f"Error during route testing: {str(e)}")
        results["success"] = False
        results["error"] = str(e)
        
    return results


def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Test API routes")
    parser.add_argument("--api-url", default="http://localhost:8000/api/v1", help="Base URL for the API")
    parser.add_argument("--username", default="admin", help="Username for authentication")
    parser.add_argument("--password", default="password", help="Password for authentication")
    args = parser.parse_args()
    
    results = test_item_routes(args.api_url, args.username, args.password)
    
    if results["success"]:
        logger.info("All route tests passed!")
        for test_name, test_result in results["tests"].items():
            logger.info(f"{test_name}: {test_result['status']}")
    else:
        logger.error("Route tests failed!")
        for test_name, test_result in results["tests"].items():
            if test_result["status"] == "failed":
                logger.error(f"{test_name}: {test_result.get('error', 'Unknown error')}")
    
    return 0 if results["success"] else 1


if __name__ == "__main__":
    sys.exit(main()) 