#!/usr/bin/env python
"""Add a test user to the database."""
import sys
import os
import uuid
import logging

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import text
from api.db.session import sync_engine

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_test_user():
    """Add a test user to the database."""
    logger.info("Adding test user to the database")
    
    try:
        # Create a test user with a known UUID
        user_id = "e3c704f3-c398-4894-bd7b-a1d092dada04"  # This matches what's being used in uploads
        
        with sync_engine.connect() as conn:
            # Check if the user already exists
            result = conn.execute(text("SELECT id FROM users WHERE id = :id"), {"id": user_id})
            if result.fetchone():
                logger.info(f"Test user with ID {user_id} already exists")
                return
            
            # Create the user
            conn.execute(
                text("""
                INSERT INTO users (
                    id, email, full_name, password_hash, is_active
                ) VALUES (
                    :id, :email, :full_name, :password_hash, :is_active
                )
                """),
                {
                    "id": user_id,
                    "email": "<EMAIL>",
                    "full_name": "Test User",
                    "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
                    "is_active": True
                }
            )
            conn.commit()
            logger.info(f"Test user created with ID {user_id}")
            
            # Also add another test user that matches the second UUID
            user_id2 = "190f1eaf-b85c-444a-b81d-fc9f5172ab6f"
            conn.execute(
                text("""
                INSERT INTO users (
                    id, email, full_name, password_hash, is_active
                ) VALUES (
                    :id, :email, :full_name, :password_hash, :is_active
                )
                """),
                {
                    "id": user_id2,
                    "email": "<EMAIL>",
                    "full_name": "Test User 2",
                    "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
                    "is_active": True
                }
            )
            conn.commit()
            logger.info(f"Test user 2 created with ID {user_id2}")
            
    except Exception as e:
        logger.error(f"Error adding test user: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    add_test_user() 