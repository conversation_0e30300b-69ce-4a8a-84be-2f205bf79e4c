#!/bin/bash

# Set to exit on error
set -e

echo "====== TurdParty Test Environment Setup ======"
echo "This script will help set up a proper testing environment and run tests"
echo

# Function to create our docker-compose file
create_docker_compose() {
  cat > docker-compose.testing.yml << 'EOL'
services:
  test_runner:
    build:
      context: .
      dockerfile: Dockerfile.testing
    volumes:
      - ./:/app
    environment:
      - PYTHONPATH=/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - DOCKER_NETWORK=true
    depends_on:
      - postgres
      - minio
    networks:
      - test_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty_test
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - test_network
      
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - test_network

networks:
  test_network:
    driver: bridge

volumes:
  postgres_data:
  minio_data:
EOL
  echo "Created docker-compose.testing.yml"
}

# Function to create our Dockerfile
create_dockerfile() {
  cat > Dockerfile.testing << 'EOL'
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openssh-client \
    procps \
    curl \
    gcc \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Create a requirements file if it doesn't exist
RUN echo "pytest>=7.0.0\npytest-mock>=3.10.0\npytest-cov>=4.0.0\nminio>=7.1.0\nrequests>=2.28.0" > /tmp/requirements-test.txt

# Install Python dependencies
RUN pip install -r /tmp/requirements-test.txt

# Set up environment
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Default command
CMD ["pytest", "-v"]
EOL
  echo "Created Dockerfile.testing"
}

# Function to create test script
create_test_script() {
  cat > run_test.sh << 'EOL'
#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to run a specific test
run_test() {
  local test_file=$1
  echo -e "${YELLOW}Running test: ${test_file}${NC}"
  
  # Run the test and capture output
  docker compose -f docker-compose.testing.yml run --rm test_runner python -m pytest "$test_file" -v
  
  # Check exit code
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Test passed: ${test_file}${NC}"
    return 0
  else
    echo -e "${RED}Test failed: ${test_file}${NC}"
    return 1
  fi
}

# If specific test file is provided, run just that one
if [ $# -gt 0 ]; then
  run_test "$1"
  exit $?
fi

# Find all test files (excluding .focus_forge)
echo -e "${YELLOW}Finding test files...${NC}"
TEST_FILES=$(find /app -name "test_*.py" -type f -not -path "*/\.focus_forge/*" | sort)

# Display found test files
echo -e "${YELLOW}Found $(echo "$TEST_FILES" | wc -l) test files:${NC}"
echo "$TEST_FILES"
echo

# Ask user if they want to run all tests
read -p "Run all tests? (y/n): " RUN_ALL

if [ "$RUN_ALL" = "y" ]; then
  # Run all tests
  PASS_COUNT=0
  FAIL_COUNT=0
  
  for test_file in $TEST_FILES; do
    run_test "$test_file"
    if [ $? -eq 0 ]; then
      ((PASS_COUNT++))
    else
      ((FAIL_COUNT++))
    fi
  done
  
  echo
  echo -e "${GREEN}Tests passed: ${PASS_COUNT}${NC}"
  echo -e "${RED}Tests failed: ${FAIL_COUNT}${NC}"
  echo -e "Total tests: $((PASS_COUNT + FAIL_COUNT))"
else
  # Let user select a test to run
  echo "Select a test to run:"
  select test_file in $TEST_FILES "Quit"; do
    if [ "$test_file" = "Quit" ]; then
      exit 0
    fi
    
    if [ -n "$test_file" ]; then
      run_test "$test_file"
      break
    else
      echo "Invalid selection"
    fi
  done
fi
EOL
  chmod +x run_test.sh
  echo "Created run_test.sh"
}

# Main script logic
echo "Creating necessary files..."
create_docker_compose
create_dockerfile
create_test_script

echo 
echo "Building Docker container..."
docker compose -f docker-compose.testing.yml build

echo
echo "===== Setup complete! ====="
echo "You can now run tests with:"
echo "  ./run_test.sh [optional_test_file]"
echo
echo "Example:"
echo "  ./run_test.sh                      # Interactive mode to select tests"
echo "  ./run_test.sh /app/test_minio.py   # Run a specific test"
echo

# Ask if they want to run tests now
read -p "Run tests now? (y/n): " RUN_NOW
if [ "$RUN_NOW" = "y" ]; then
  ./run_test.sh
fi 