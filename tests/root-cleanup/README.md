# Root Cleanup - Test Files

This directory contains test files that were moved from the project root during the cleanup process.

## Files Moved from Root

### Test Utilities
- `add_test_user.py` - Utility to add test users
- `diagnose_test.py` - Test diagnostics utility
- `drop_test_index.py` - Database test index management
- `enable_test_mode_in_application.py` - Enable test mode in app
- `enable_test_mode_in_docker.py` - Enable test mode in Docker
- `setup_test_tools.sh` - Test environment setup
- `standalone_route_test.py` - Standalone route testing

### Test Data & Configuration
- `test_db.sqlite` - Test database file
- `test_redirect_dockerfile` - Docker test configuration
- `run-tests-directly.js` - Direct test runner

### Windows Testing
- `windows_template_test.py` - Windows template tests
- `windows_test.py` - Windows-specific tests

### Scripts & Utilities
- `identify-streamlit-tests.sh` - Streamlit test identification

## Organization Notes

These files were moved during the root folder cleanup process.
They should be reviewed and either:
1. Integrated into the proper test structure
2. Archived if no longer needed
3. Moved to appropriate directories

## Next Steps

1. Review each file for current relevance
2. Integrate useful tests into the main test suite
3. Archive or remove obsolete files
4. Update any references in documentation or scripts
