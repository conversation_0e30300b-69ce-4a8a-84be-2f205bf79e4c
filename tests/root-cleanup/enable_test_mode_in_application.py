#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to modify the application.py file in the Docker container to enable test mode.
"""
import subprocess
import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# The code to inject into application.py
INJECT_CODE = """
# Enable test mode to bypass authentication
from api.core.test_config import test_settings
test_settings.enable_test_mode()
logger.info("Test mode enabled for all requests - authentication bypassed")
"""

def main():
    """Modify the application.py file in the Docker container."""
    logger.info("Modifying application.py in Docker container...")
    
    try:
        # Check if the container is running
        check_cmd = ["docker", "ps", "-q", "-f", "name=TurdParty-container"]
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        
        if not result.stdout.strip():
            logger.error("TurdParty-container is not running")
            return 1
        
        # Create a backup of the application.py file
        backup_cmd = [
            "docker", "exec", "TurdParty-container",
            "cp", "/app/api/application.py", "/app/api/application.py.bak"
        ]
        subprocess.run(backup_cmd, check=True)
        logger.info("Created backup of application.py")
        
        # Check if the code is already in application.py
        check_content_cmd = [
            "docker", "exec", "TurdParty-container",
            "grep", "-q", "test_settings.enable_test_mode()", "/app/api/application.py"
        ]
        check_result = subprocess.run(check_content_cmd)
        
        if check_result.returncode == 0:
            logger.info("Test mode is already enabled in application.py")
            return 0
        
        # Create a temporary file with the code to inject
        with open("/tmp/test_mode_code.py", "w") as f:
            f.write(INJECT_CODE)
        
        # Copy the file to the container
        copy_cmd = ["docker", "cp", "/tmp/test_mode_code.py", "TurdParty-container:/tmp/"]
        subprocess.run(copy_cmd, check=True)
        
        # Inject the code into application.py at the beginning of the get_application function
        inject_cmd = [
            "docker", "exec", "TurdParty-container",
            "bash", "-c", 
            "sed -i '/def get_application/a\\    # Enable test mode\\n    from api.core.test_config import test_settings\\n    test_settings.enable_test_mode()\\n    logger.info(\"Test mode enabled for all requests - authentication bypassed\")' /app/api/application.py"
        ]
        subprocess.run(inject_cmd, check=True)
        
        logger.info("Test mode enabled in application.py")
        
        # Restart the container to apply changes
        restart_cmd = ["docker", "restart", "TurdParty-container"]
        subprocess.run(restart_cmd, check=True)
        logger.info("Container restarted to apply changes")
        
        return 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Error modifying application.py: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 