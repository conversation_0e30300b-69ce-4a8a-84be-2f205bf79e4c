#!/bin/bash

# Script to identify all test files that depend on Streamlit
# and organize them for conversion to Flask

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file and directory
LOG_DIR="streamlit_conversion"
mkdir -p $LOG_DIR
DATE_TAG=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$LOG_DIR/streamlit_tests_$DATE_TAG.md"
SUMMARY_FILE="$LOG_DIR/streamlit_summary_$DATE_TAG.txt"

log() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a $SUMMARY_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a $SUMMARY_FILE
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a $SUMMARY_FILE
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}" | tee -a $SUMMARY_FILE
}

section "IDENTIFYING STREAMLIT DEPENDENCIES IN TESTS"
log "Starting analysis at $(date)"
log "Detailed report will be saved to: $LOG_FILE"
log "Summary will be saved to: $SUMMARY_FILE"

# Initialize the report file
cat > $LOG_FILE << EOL
# Streamlit to Flask Test Conversion Report
Generated: $(date)

This report identifies all test files with Streamlit dependencies that need to be converted to Flask.

## Summary

EOL

# Initialize the summary file
cat > $SUMMARY_FILE << EOL
Streamlit to Flask Test Conversion Summary
Generated: $(date)

EOL

# Find all Python test files
TEST_FILES=($(find . -type f -name "test_*.py" -o -name "*_test.py" | grep -v "__pycache__" | grep -v ".venv" | \
    grep -v ".pip_modules" | grep -v "node_modules" | grep -v "venv" | grep -v "site-packages"))

log "Found ${#TEST_FILES[@]} test files to analyze"

# Find all files importing streamlit
STREAMLIT_FILES=($(grep -l "import streamlit\|from streamlit" "${TEST_FILES[@]}" 2>/dev/null))
STREAMLIT_IMPORT_FILES=($(grep -l "import streamlit" "${TEST_FILES[@]}" 2>/dev/null))
STREAMLIT_FROM_FILES=($(grep -l "from streamlit" "${TEST_FILES[@]}" 2>/dev/null))

# Count of files by dependency type
DIRECT_IMPORT_COUNT=${#STREAMLIT_FILES[@]}
STREAMLIT_DEPENDENT_COUNT=0

# Build a list of test files that interact with streamlit modules
POTENTIAL_DEPENDENT_FILES=()
for file in "${TEST_FILES[@]}"; do
    if grep -q "streamlit\|st\." "$file" && ! grep -q "import streamlit\|from streamlit" "$file"; then
        POTENTIAL_DEPENDENT_FILES+=("$file")
        ((STREAMLIT_DEPENDENT_COUNT++))
    fi
done

# Update the report with counts
cat >> $LOG_FILE << EOL
- **Total test files found:** ${#TEST_FILES[@]}
- **Files directly importing Streamlit:** $DIRECT_IMPORT_COUNT
- **Files potentially using Streamlit indirectly:** $STREAMLIT_DEPENDENT_COUNT

## Files with direct Streamlit imports

These files directly import Streamlit and will need to be converted to use Flask instead:

EOL

# Add direct import files to the report
for file in "${STREAMLIT_FILES[@]}"; do
    echo "- \`$file\`" >> $LOG_FILE
    
    # Extract streamlit import lines
    echo "  - Import statements:" >> $LOG_FILE
    echo "    \`\`\`python" >> $LOG_FILE
    grep -n "import streamlit\|from streamlit" "$file" | sed 's/^/    /' >> $LOG_FILE
    echo "    \`\`\`" >> $LOG_FILE
    
    # Extract a few examples of streamlit usage
    echo "  - Usage examples:" >> $LOG_FILE
    echo "    \`\`\`python" >> $LOG_FILE
    grep -n "st\." "$file" | head -3 | sed 's/^/    /' >> $LOG_FILE
    
    # Show count if there are more examples
    usage_count=$(grep -c "st\." "$file")
    if [ "$usage_count" -gt 3 ]; then
        echo "    # ... and $(($usage_count - 3)) more instances" >> $LOG_FILE
    fi
    echo "    \`\`\`" >> $LOG_FILE
    echo "" >> $LOG_FILE
done

# Add section for indirect dependencies
cat >> $LOG_FILE << EOL
## Files with indirect Streamlit dependencies

These files don't import Streamlit directly but reference Streamlit objects or functions:

EOL

# Add indirect dependency files
for file in "${POTENTIAL_DEPENDENT_FILES[@]}"; do
    echo "- \`$file\`" >> $LOG_FILE
    
    # Extract streamlit usage
    echo "  - References:" >> $LOG_FILE
    echo "    \`\`\`python" >> $LOG_FILE
    grep -n "streamlit\|st\." "$file" | head -5 | sed 's/^/    /' >> $LOG_FILE
    usage_count=$(grep -c "streamlit\|st\." "$file")
    if [ "$usage_count" -gt 5 ]; then
        echo "    # ... and $(($usage_count - 5)) more instances" >> $LOG_FILE
    fi
    echo "    \`\`\`" >> $LOG_FILE
    echo "" >> $LOG_FILE
done

# Create priority list based on complexity
cat >> $LOG_FILE << EOL
## Conversion Priority

Files are categorized by conversion difficulty:

### Low Complexity (Few Streamlit calls)
EOL

# Add low complexity files
for file in "${STREAMLIT_FILES[@]}" "${POTENTIAL_DEPENDENT_FILES[@]}"; do
    usage_count=$(grep -c "st\." "$file")
    if [ "$usage_count" -lt 5 ]; then
        echo "- \`$file\` ($usage_count Streamlit calls)" >> $LOG_FILE
    fi
done

cat >> $LOG_FILE << EOL

### Medium Complexity (5-15 Streamlit calls)
EOL

# Add medium complexity files
for file in "${STREAMLIT_FILES[@]}" "${POTENTIAL_DEPENDENT_FILES[@]}"; do
    usage_count=$(grep -c "st\." "$file")
    if [ "$usage_count" -ge 5 ] && [ "$usage_count" -le 15 ]; then
        echo "- \`$file\` ($usage_count Streamlit calls)" >> $LOG_FILE
    fi
done

cat >> $LOG_FILE << EOL

### High Complexity (More than 15 Streamlit calls)
EOL

# Add high complexity files
for file in "${STREAMLIT_FILES[@]}" "${POTENTIAL_DEPENDENT_FILES[@]}"; do
    usage_count=$(grep -c "st\." "$file")
    if [ "$usage_count" -gt 15 ]; then
        echo "- \`$file\` ($usage_count Streamlit calls)" >> $LOG_FILE
    fi
done

# Add conversion guide to the report
cat >> $LOG_FILE << 'EOL'

## Conversion Guide

### Common Streamlit to Flask Mapping

| Streamlit Component | Flask/HTML Equivalent |
|-------------------|------------------------|
| st.button | `<button>` or `<input type="submit">` |
| st.text_input | `<input type="text">` |
| st.number_input | `<input type="number">` |
| st.selectbox | `<select>` dropdown |
| st.multiselect | `<select multiple>` |
| st.checkbox | `<input type="checkbox">` |
| st.radio | `<input type="radio">` |
| st.form | `<form>` element |
| st.sidebar | Left navigation or sidebar with CSS |
| st.write/markdown | Rendered with Jinja2 templates |
| st.dataframe/table | HTML tables or DataTables.js |
| st.file_uploader | `<input type="file">` |
| st.session_state | Flask session |
| st.cache | Flask-Caching extension |

### Test-Specific Conversion Notes

For test files, the conversion approach will differ based on what's being tested:

1. **UI Component Tests**: These tests should be rewritten to test Flask routes and templates.
   - Use Flask's test client for making requests
   - Check for expected elements in the HTML response

2. **Session State Tests**: Tests for Streamlit's session state should use Flask's session
   - Use Flask's test client with `with client.session_transaction()` for session manipulation

3. **API Integration Tests**: These usually require minimal changes
   - Update any UI interaction code to use Flask's test client

4. **Form Submission Tests**: Replace Streamlit form interactions with Flask form submissions
   - Use Flask's test client to POST form data to routes
   - Check for redirects and flash messages

### Example Test Conversion

Streamlit test:
```python
def test_login_form():
    # Set up
    st.session_state.logged_in = False
    
    # Test the login component
    username = "test_user"
    password = "test_password"
    
    # Simulate form input and submission
    with patch('streamlit.text_input', return_value=username):
        with patch('streamlit.secret_input', return_value=password):
            with patch('streamlit.button', return_value=True):
                result = login_component()
    
    # Assert
    assert st.session_state.logged_in
    assert st.session_state.username == username
```

Flask equivalent:
```python
def test_login_form():
    # Create a test client
    client = app.test_client()
    
    # Simulate form submission
    response = client.post('/login', data={
        'username': 'test_user',
        'password': 'test_password'
    }, follow_redirects=True)
    
    # Check the response
    assert response.status_code == 200
    
    # Check session values
    with client.session_transaction() as session:
        assert session['logged_in']
        assert session['username'] == 'test_user'
```

EOL

# Update the summary
section "ANALYSIS COMPLETE"
log "Total test files: ${#TEST_FILES[@]}"
log "Files with direct Streamlit imports: $DIRECT_IMPORT_COUNT"
log "Files with indirect Streamlit dependencies: $STREAMLIT_DEPENDENT_COUNT"
log "Detailed report saved to: $LOG_FILE"

success "Analysis complete! Use the report in $LOG_FILE to plan your Streamlit to Flask conversion." 