#!/bin/bash

# Make sure the scripts are executable
chmod +x diagnose_test.py run_tests.py check_test_env.py

echo "=== Test Tools Setup ==="
echo "Test tools have been set up. Here's how to use them:"
echo

echo "1. Check your test environment:"
echo "   ./check_test_env.py"
echo "   This will show if you have all the required dependencies and services running."
echo

echo "2. Run all tests with proper setup:"
echo "   ./run_tests.py"
echo "   This will create a virtual environment, install dependencies, and run tests."
echo

echo "3. Run a specific test with proper setup:"
echo "   ./run_tests.py --test path/to/test.py"
echo

echo "4. Diagnose a specific failing test:"
echo "   ./diagnose_test.py path/to/test.py"
echo "   This will run the test with maximum verbosity to see detailed error information."
echo

echo "Optional: Run with an existing virtualenv:"
echo "   source /path/to/venv/bin/activate"
echo "   ./run_tests.py --skip-venv"
echo

echo "For more help, run each script with --help flag"
echo

# Create a requirements file if it doesn't exist
if [ ! -f "requirements-test.txt" ]; then
    echo "Creating basic requirements-test.txt file..."
    cat > requirements-test.txt << EOF
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
requests>=2.28.0
minio>=7.1.0
python-vagrant>=1.0.0
paramiko>=3.0.0  # For SSH operations
EOF
    echo "Created requirements-test.txt with basic testing dependencies"
fi

# Check if we have pytest installed
if ! command -v pytest &> /dev/null; then
    echo "Warning: pytest is not installed in the current environment."
    echo "You can install it with: pip install pytest"
fi

echo "Setup complete!" 