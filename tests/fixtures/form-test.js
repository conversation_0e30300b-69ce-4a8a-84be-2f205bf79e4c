const { chromium } = require("playwright"); (async () => { const browser = await chromium.launch({ headless: true }); const context = await browser.newContext(); const page = await context.newPage(); try { console.log("*** Testing File Upload Form ***"); await page.goto("http://**********:3000/file_upload"); console.log("File Upload page loaded"); await page.screenshot({ path: "/tmp/form_upload_start.png" }); const uploadButton = await page.$("button:has-text(\"Upload\")"); if(uploadButton) { console.log("Found upload button"); await uploadButton.click(); await page.waitForTimeout(500); await page.screenshot({ path: "/tmp/form_upload_validation.png" }); const validationMessage = await page.$(".ant-message-error, .ant-form-item-explain-error"); if(validationMessage) { console.log("Found validation message"); const messageText = await validationMessage.textContent(); console.log("Validation message:", messageText); } else { console.log("No validation message found"); } } else { console.log("No upload button found"); } const textarea = await page.$("textarea"); if(textarea) { console.log("Found textarea for description"); await textarea.fill("Test description for form validation"); await page.screenshot({ path: "/tmp/form_upload_filled.png" }); console.log("Filled textarea with test description"); } console.log("\n*** Testing VM Status Form Elements ***"); await page.goto("http://**********:3000/vm_status"); console.log("VM Status page loaded"); await page.screenshot({ path: "/tmp/form_vmstatus_start.png" }); const refreshButton = await page.$("button:has-text(\"Refresh\")"); if(refreshButton) { console.log("Found refresh button"); await refreshButton.hover(); await page.screenshot({ path: "/tmp/form_vmstatus_hover.png" }); console.log("Hovered over refresh button"); await refreshButton.click(); await page.waitForTimeout(500); await page.screenshot({ path: "/tmp/form_vmstatus_click.png" }); console.log("Clicked refresh button"); } console.log("All form tests completed successfully"); } catch (error) { console.error("Error:", error.message); } finally { await browser.close(); } })();
