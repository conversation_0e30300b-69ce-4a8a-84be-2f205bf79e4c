#!/usr/bin/env python3

"""
Simple script to test if the API is reachable.
"""

import requests
import sys
import json

# API configuration
API_BASE_URL = "http://localhost:3050"
API_HEALTH_ENDPOINT = "/health"
API_UPLOAD_ENDPOINT = "/api/v1/files/upload"
API_VM_CREATE_ENDPOINT = "/api/v1/vagrant/create"
API_VM_INJECT_ENDPOINT = "/api/v1/vagrant/inject"
API_VM_STATUS_ENDPOINT = "/api/v1/vagrant/status"
API_VM_DESTROY_ENDPOINT = "/api/v1/vagrant/destroy"

def test_endpoint(endpoint, method="GET", data=None, files=None):
    """
    Test if an endpoint is reachable.
    
    Args:
        endpoint: The endpoint to test.
        method: The HTTP method to use.
        data: The data to send.
        files: The files to upload.
        
    Returns:
        The response from the API.
    """
    url = f"{API_BASE_URL}{endpoint}"
    print(f"Testing {method} {url}...")
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=5)
        elif method == "POST":
            if files:
                response = requests.post(url, files=files, timeout=5)
            else:
                response = requests.post(url, json=data, timeout=5)
        elif method == "DELETE":
            response = requests.delete(url, timeout=5)
        else:
            print(f"Unsupported method: {method}")
            return None
        
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.text}")
            return response.json()
        else:
            print(f"Error: {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return None

def main():
    """Main function."""
    # Test the health endpoint
    print("\n=== Testing Health Endpoint ===")
    health_response = test_endpoint(API_HEALTH_ENDPOINT)
    
    if not health_response:
        print("Health endpoint test failed.")
        sys.exit(1)
    
    print("\n=== Testing File Upload Endpoint ===")
    # Create a test file
    with open("/tmp/test_file.txt", "w") as f:
        f.write("This is a test file.")
    
    # Test the file upload endpoint
    with open("/tmp/test_file.txt", "rb") as f:
        files = {"file": ("test_file.txt", f)}
        upload_response = test_endpoint(API_UPLOAD_ENDPOINT, method="POST", files=files)
    
    if not upload_response:
        print("File upload endpoint test failed.")
        sys.exit(1)
    
    file_id = upload_response.get("file_id")
    print(f"File ID: {file_id}")
    
    print("\n=== Testing VM Create Endpoint ===")
    # Test the VM create endpoint
    create_data = {"name": "test_vm"}
    create_response = test_endpoint(API_VM_CREATE_ENDPOINT, method="POST", data=create_data)
    
    if not create_response:
        print("VM create endpoint test failed.")
        sys.exit(1)
    
    vm_id = create_response.get("vm_id")
    print(f"VM ID: {vm_id}")
    
    print("\n=== Testing VM Status Endpoint ===")
    # Test the VM status endpoint
    status_response = test_endpoint(f"{API_VM_STATUS_ENDPOINT}/{vm_id}")
    
    if not status_response:
        print("VM status endpoint test failed.")
        # Clean up
        test_endpoint(f"{API_VM_DESTROY_ENDPOINT}/{vm_id}", method="DELETE")
        sys.exit(1)
    
    print("\n=== Testing VM Inject Endpoint ===")
    # Test the VM inject endpoint
    inject_data = {
        "vm_id": vm_id,
        "file_id": file_id,
        "target_path": "/tmp/test_file.txt"
    }
    inject_response = test_endpoint(API_VM_INJECT_ENDPOINT, method="POST", data=inject_data)
    
    if not inject_response:
        print("VM inject endpoint test failed.")
        # Clean up
        test_endpoint(f"{API_VM_DESTROY_ENDPOINT}/{vm_id}", method="DELETE")
        sys.exit(1)
    
    print("\n=== Testing VM Destroy Endpoint ===")
    # Test the VM destroy endpoint
    destroy_response = test_endpoint(f"{API_VM_DESTROY_ENDPOINT}/{vm_id}", method="DELETE")
    
    if not destroy_response:
        print("VM destroy endpoint test failed.")
        sys.exit(1)
    
    print("\nAll tests passed!")
    sys.exit(0)

if __name__ == "__main__":
    main()
