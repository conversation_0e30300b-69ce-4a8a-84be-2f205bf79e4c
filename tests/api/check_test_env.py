#!/usr/bin/env python3
import os
import sys
import subprocess
import platform
import socket
import json
from pathlib import Path

def check_command(cmd):
    """Check if a command is available"""
    try:
        subprocess.run([cmd, "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except FileNotFoundError:
        return False

def check_port(host, port):
    """Check if a port is open on the given host"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(1)
    result = sock.connect_ex((host, port))
    sock.close()
    return result == 0

def get_python_packages():
    """Get installed Python packages"""
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "list", "--format=json"], 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        if result.returncode != 0:
            return []
        
        packages = json.loads(result.stdout)
        return {pkg["name"].lower(): pkg["version"] for pkg in packages}
    except Exception as e:
        print(f"Error getting Python packages: {e}")
        return {}

def main():
    print(f"=== Test Environment Check ===")
    print(f"Python version: {platform.python_version()}")
    print(f"System: {platform.system()} {platform.release()}")
    
    # Check if pytest is installed
    packages = get_python_packages()
    if "pytest" in packages:
        print(f"✓ pytest {packages['pytest']} is installed")
    else:
        print("✗ pytest is NOT installed")
        
    # Check for other common test packages
    test_packages = ["pytest-cov", "pytest-xdist", "requests", "pytest-mock"]
    for pkg in test_packages:
        if pkg.lower() in packages:
            print(f"✓ {pkg} {packages[pkg.lower()]} is installed")
        else:
            print(f"✗ {pkg} is NOT installed")
    
    # Check for common external tools needed for testing
    external_tools = [
        "docker", "vagrant", "virtualbox", "ssh"
    ]
    for tool in external_tools:
        if check_command(tool):
            print(f"✓ {tool} is installed")
        else:
            print(f"✗ {tool} is NOT installed")
    
    # Check for MinIO service (commonly used in the failing tests)
    if check_port("localhost", 9000):
        print("✓ Service running on port 9000 (possibly MinIO)")
    else:
        print("✗ No service found on port 9000 (MinIO likely not running)")
    
    # Check for possible VM/virtualization related services
    other_ports = {
        8080: "Backend API",
        5432: "PostgreSQL",
        27017: "MongoDB",
        22: "SSH"
    }
    
    for port, service in other_ports.items():
        if check_port("localhost", port):
            print(f"✓ Service running on port {port} ({service})")
        else:
            print(f"✗ No service found on port {port} ({service})")
    
    # Check environment variables that might be needed
    env_vars = [
        "PYTHONPATH",
        "MINIO_ACCESS_KEY",
        "MINIO_SECRET_KEY",
        "VAGRANT_HOME",
        "VAGRANT_DEFAULT_PROVIDER"
    ]
    
    print("\nEnvironment Variables:")
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            # For sensitive values, don't print the actual value
            if "KEY" in var or "SECRET" in var:
                print(f"✓ {var} is set")
            else:
                print(f"✓ {var}={value}")
        else:
            print(f"✗ {var} is NOT set")
    
    # Check for common test directories
    test_dirs = ["tests", "test", "api/tests"]
    print("\nTest Directories:")
    for dir_name in test_dirs:
        if os.path.isdir(dir_name):
            test_count = len([f for f in Path(dir_name).glob("test_*.py")])
            print(f"✓ {dir_name}/ exists with {test_count} test files")
        else:
            print(f"✗ {dir_name}/ does NOT exist")

if __name__ == "__main__":
    main() 