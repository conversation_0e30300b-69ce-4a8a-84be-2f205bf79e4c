#!/usr/bin/env python
import logging
from typing import Dict, List, Optional, Any
from fastapi import Fast<PERSON><PERSON>, Response, HTTPException, Depends, File, UploadFile, Form, Header, Body
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from pydantic import BaseModel
import uvicorn
import os
from datetime import datetime, timedelta
import json

# Models for API requests and responses
class LoginData(BaseModel):
    username: str
    password: str

class UserData(BaseModel):
    id: int
    email: str
    username: str
    is_active: bool = True
    is_admin: bool = False

class VMData(BaseModel):
    id: str
    name: str
    status: str
    provider: str
    created_at: str

class TokenData(BaseModel):
    access_token: str
    token_type: str = "bearer"

class FileData(BaseModel):
    id: str
    name: str
    size: int
    user_id: int
    upload_date: str
    mime_type: str

class FileToVMRequest(BaseModel):
    file_id: str
    vm_name: str

class SystemInfoResponse(BaseModel):
    version: str
    uptime: int
    system_load: List[float]
    hostname: str

# Fake database
users_db = {
    "testuser": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123",
        "is_active": True,
        "is_admin": False
    },
    "admin": {
        "id": 2,
        "username": "admin",
        "email": "<EMAIL>",
        "password": "adminpass",
        "is_active": True,
        "is_admin": True
    }
}

vms_db = [
    {
        "id": "vm1",
        "name": "ubuntu-vm",
        "status": "running",
        "provider": "virtualbox",
        "created_at": "2023-01-15T12:00:00Z"
    },
    {
        "id": "vm2",
        "name": "centos-vm",
        "status": "stopped",
        "provider": "virtualbox",
        "created_at": "2023-01-20T14:30:00Z"
    }
]

files_db = [
    {
        "id": "file1",
        "name": "test.txt",
        "size": 1024,
        "user_id": 1,
        "upload_date": "2023-01-10T08:15:00Z",
        "mime_type": "text/plain"
    },
    {
        "id": "file2",
        "name": "image.jpg",
        "size": 102400,
        "user_id": 1,
        "upload_date": "2023-01-11T10:20:00Z",
        "mime_type": "image/jpeg"
    }
]

# OAuth2 setup
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login/")

# Simple token store
tokens = {}

app = FastAPI()

# --- Authentication ---

def authenticate_user(username: str, password: str):
    if username in users_db and users_db[username]["password"] == password:
        return users_db[username]
    return None

def get_current_user(token: str = Depends(oauth2_scheme)):
    if token not in tokens:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return users_db[tokens[token]]

# --- Health and Status Endpoints ---

@app.get("/api/v1/health/")
async def health_check():
    return {"status": "ok"}

@app.get("/api/v1/version/")
async def version():
    return {"version": "1.0.0", "build_date": "2023-01-01T00:00:00Z"}

# --- Auth Endpoints ---

@app.post("/api/v1/auth/login/")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=401, detail="Incorrect username or password")
    
    # Generate a simple token
    token = f"token_{user['username']}_{datetime.now().timestamp()}"
    tokens[token] = user['username']
    
    return TokenData(access_token=token)

@app.post("/api/v1/auth/logout/")
async def logout(token: str = Depends(oauth2_scheme)):
    if token in tokens:
        del tokens[token]
    return {"detail": "Successfully logged out"}

# --- User Endpoints ---

@app.get("/api/v1/users/me/")
async def read_users_me(current_user: dict = Depends(get_current_user)):
    return UserData(
        id=current_user["id"],
        email=current_user["email"],
        username=current_user["username"],
        is_active=current_user["is_active"],
        is_admin=current_user["is_admin"]
    )

# --- File Management Endpoints ---

@app.get("/api/v1/files/")
async def list_files(current_user: dict = Depends(get_current_user)):
    user_files = [f for f in files_db if f["user_id"] == current_user["id"]]
    return user_files

@app.post("/api/v1/files/upload/")
async def upload_file(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user)
):
    # Simulate file upload
    file_id = f"file_{len(files_db) + 1}"
    file_data = {
        "id": file_id,
        "name": file.filename,
        "size": 0,  # We don't actually read the file
        "user_id": current_user["id"],
        "upload_date": datetime.now().isoformat(),
        "mime_type": file.content_type
    }
    files_db.append(file_data)
    return {"file_id": file_id, "status": "uploaded"}

# --- VM Management Endpoints ---

@app.get("/api/v1/vms/")
async def list_vms(current_user: dict = Depends(get_current_user)):
    return vms_db

@app.post("/api/v1/vagrant/vms/")
async def create_vagrant_vm(
    name: str = Body(...),
    provider: str = Body(...),
    box: str = Body(...),
    current_user: dict = Depends(get_current_user)
):
    # Create a new VM
    vm_id = f"vm_{len(vms_db) + 1}"
    vm_data = {
        "id": vm_id,
        "name": name,
        "status": "created",
        "provider": provider,
        "created_at": datetime.now().isoformat()
    }
    vms_db.append(vm_data)
    return {"vm_id": vm_id, "status": "created"}

@app.get("/api/v1/vagrant/vms/operations/")
async def list_vm_operations(current_user: dict = Depends(get_current_user)):
    return {
        "operations": ["start", "stop", "destroy", "pause", "resume"]
    }

# --- File-to-VM Integration ---

@app.post("/api/v1/file-to-vm/")
async def attach_file_to_vm(
    request: FileToVMRequest,
    current_user: dict = Depends(get_current_user)
):
    # Check if file exists
    file = next((f for f in files_db if f["id"] == request.file_id), None)
    if not file:
        raise HTTPException(status_code=404, detail="File not found")
    
    # Check if VM exists
    vm = next((v for v in vms_db if v["name"] == request.vm_name), None)
    if not vm:
        raise HTTPException(status_code=404, detail="VM not found")
    
    return {
        "status": "success",
        "file_id": request.file_id,
        "vm_name": request.vm_name,
        "operation_id": f"op_{datetime.now().timestamp()}"
    }

# --- System Information ---

@app.get("/api/v1/system/info/")
async def system_info(current_user: dict = Depends(get_current_user)):
    return SystemInfoResponse(
        version="1.0.0",
        uptime=3600,
        system_load=[0.1, 0.2, 0.1],
        hostname="api-test-server"
    )

# --- Storage Endpoints ---

@app.get("/api/v1/storage/status/")
async def storage_status(current_user: dict = Depends(get_current_user)):
    return {
        "total_space": **********,  # 1GB
        "used_space": 250000000,    # 250MB
        "free_space": 750000000     # 750MB
    }

# --- MinIO Endpoints ---

@app.get("/api/v1/minio/health/")
async def minio_health(current_user: dict = Depends(get_current_user)):
    return {"status": "healthy"}

@app.get("/api/v1/minio/status/")
async def minio_status(current_user: dict = Depends(get_current_user)):
    return {
        "buckets": 2,
        "objects": 15,
        "storage_used": "125MB",
        "uptime": "3d 5h 12m"
    }

# --- Static Analysis Endpoints ---

@app.get("/api/v1/static-analysis/")
async def static_analysis(current_user: dict = Depends(get_current_user)):
    return {
        "enabled": True,
        "analyzers": ["malware", "vulnerability", "sensitive_data"],
        "queue_size": 0
    }

# --- Docker Endpoints ---

@app.get("/api/v1/docker/status/")
async def docker_status(current_user: dict = Depends(get_current_user)):
    return {
        "running_containers": 5,
        "available_images": 10,
        "status": "running"
    }

# --- Documentation ---

@app.get("/api/v1/docs/")
async def api_docs():
    return Response(
        content=json.dumps({"openapi": "3.0.0", "info": {"title": "API", "version": "1.0.0"}}),
        media_type="application/json"
    )

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    # Get port from environment variable or use default
    port = int(os.environ.get("PORT", 8000))
    
    # Start server
    uvicorn.run(app, host="0.0.0.0", port=port) 