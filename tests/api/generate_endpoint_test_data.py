#!/usr/bin/env python3
"""
Script to generate test data for the endpoint monitoring dashboard.

This script simulates requests to both old and new VM endpoints to generate
log entries for the monitoring dashboard.
"""
import os
import time
import random
import datetime
import argparse
import requests
from urllib.parse import urljoin

# Define the base URL
BASE_URL = "http://host.docker.internal:3050"

# Define the old endpoints
OLD_ENDPOINTS = [
    "/api/v1/vagrant/status/test-vm",
    "/api/v1/vagrant/info/test-vm",
    "/api/v1/vagrant/connection/status",
    "/api/v1/vagrant/boxes",
    "/api/v1/vagrant_vm/templates",
    "/api/v1/vagrant_vm/test-vm/status",
    "/api/v1/vagrant_vm/test-vm/logs",
    "/api/v1/vm_injection",
    "/api/v1/vm_injection/test-injection/status",
]

# Define the new endpoints
NEW_ENDPOINTS = [
    "/api/v1/virtual-machines/test-vm/status",
    "/api/v1/virtual-machines/test-vm/info",
    "/api/v1/virtual-machines/connection",
    "/api/v1/virtual-machines/boxes",
    "/api/v1/virtual-machines/templates",
    "/api/v1/virtual-machines/test-vm/logs",
    "/api/v1/virtual-machines/injections",
    "/api/v1/virtual-machines/injections/test-injection/status",
]

# Define the POST endpoints
OLD_POST_ENDPOINTS = [
    "/api/v1/vagrant/up/test-vm",
    "/api/v1/vagrant/halt/test-vm",
    "/api/v1/vagrant_vm/test-vm/start",
    "/api/v1/vagrant_vm/test-vm/stop",
    "/api/v1/vm_injection",
]

NEW_POST_ENDPOINTS = [
    "/api/v1/virtual-machines/test-vm/start",
    "/api/v1/virtual-machines/test-vm/stop",
    "/api/v1/virtual-machines/test-vm/execute",
    "/api/v1/virtual-machines/injections",
]

def simulate_request(url, method="GET"):
    """
    Simulate a request to the given URL.

    Args:
        url: The URL to request
        method: The HTTP method to use

    Returns:
        The response object
    """
    try:
        if method == "GET":
            response = requests.get(url, timeout=5)
        elif method == "POST":
            response = requests.post(url, json={}, timeout=5)
        else:
            raise ValueError(f"Unsupported method: {method}")

        print(f"{method} {url} -> {response.status_code}")
        return response
    except Exception as e:
        print(f"Error requesting {url}: {e}")
        return None

def generate_test_data(num_requests, old_ratio=0.7, days=7):
    """
    Generate test data by simulating requests to both old and new endpoints.

    Args:
        num_requests: Number of requests to simulate
        old_ratio: Ratio of old endpoint requests to total requests
        days: Number of days to spread the requests over
    """
    print(f"Generating {num_requests} test requests (old ratio: {old_ratio}, days: {days})...")

    # Calculate the number of old and new requests
    num_old_requests = int(num_requests * old_ratio)
    num_new_requests = num_requests - num_old_requests

    # Simulate requests to old endpoints
    print(f"Simulating {num_old_requests} requests to old endpoints...")
    for _ in range(num_old_requests):
        # Randomly select an endpoint
        if random.random() < 0.8:  # 80% GET, 20% POST
            endpoint = random.choice(OLD_ENDPOINTS)
            method = "GET"
        else:
            endpoint = random.choice(OLD_POST_ENDPOINTS)
            method = "POST"

        # Simulate the request
        url = urljoin(BASE_URL, endpoint)
        simulate_request(url, method)

        # Sleep to avoid overwhelming the server
        time.sleep(0.1)

    # Simulate requests to new endpoints
    print(f"Simulating {num_new_requests} requests to new endpoints...")
    for _ in range(num_new_requests):
        # Randomly select an endpoint
        if random.random() < 0.8:  # 80% GET, 20% POST
            endpoint = random.choice(NEW_ENDPOINTS)
            method = "GET"
        else:
            endpoint = random.choice(NEW_POST_ENDPOINTS)
            method = "POST"

        # Simulate the request
        url = urljoin(BASE_URL, endpoint)
        simulate_request(url, method)

        # Sleep to avoid overwhelming the server
        time.sleep(0.1)

    print("Test data generation complete!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate test data for endpoint monitoring")
    parser.add_argument("--num-requests", type=int, default=100, help="Number of requests to simulate")
    parser.add_argument("--old-ratio", type=float, default=0.7, help="Ratio of old endpoint requests to total requests")
    parser.add_argument("--days", type=int, default=7, help="Number of days to spread the requests over")

    args = parser.parse_args()

    generate_test_data(args.num_requests, args.old_ratio, args.days)
