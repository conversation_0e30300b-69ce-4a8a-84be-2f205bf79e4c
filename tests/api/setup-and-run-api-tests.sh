#!/bin/bash

# Script to set up the API test environment and run tests
# This script sets up the necessary Docker containers and networks,
# then runs the integration tests using Playwright

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}==================================================${NC}"
echo -e "${YELLOW}    Setting up API Integration Test Environment    ${NC}"
echo -e "${YELLOW}==================================================${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Error: Docker is not running.${NC}"
  exit 1
fi

# Create Docker network if it doesn't exist
echo -e "${YELLOW}Creating test network...${NC}"
docker network create test-network 2>/dev/null || true

# Stop and remove old containers if they exist
echo -e "${YELLOW}Cleaning up old containers...${NC}"
docker stop api-test 2>/dev/null || true
docker rm api-test 2>/dev/null || true

# Build API test server
echo -e "${YELLOW}Building API test server...${NC}"
docker build -t api-test-server -f Dockerfile.simple .

# Start API container
echo -e "${YELLOW}Starting API container...${NC}"
docker run -d -p 3055:8000 --name api-test --network test-network api-test-server

# Wait for API to start
echo -e "${YELLOW}Waiting for API to start...${NC}"
sleep 2

# Check if API is running
if ! docker ps | grep -q "api-test"; then
  echo -e "${RED}Error: API container failed to start.${NC}"
  exit 1
fi

# Connect Playwright container to network
echo -e "${YELLOW}Connecting Playwright container to network...${NC}"
if docker ps | grep -q "turdparty_playwright"; then
  docker network connect test-network turdparty_playwright 2>/dev/null || true
else
  echo -e "${RED}Warning: Playwright container not found. Starting it...${NC}"
  .dockerwrapper/persistent-test-env.sh
  docker network connect test-network turdparty_playwright 2>/dev/null || true
fi

# Check API health manually
echo -e "${YELLOW}Checking API health manually...${NC}"
if curl -s http://localhost:3055/api/v1/health/ | grep -q "ok"; then
  echo -e "${GREEN}API is healthy!${NC}"
else
  echo -e "${RED}Warning: API health check failed.${NC}"
  echo -e "${YELLOW}This might be expected if the API is not responding to localhost.${NC}"
  echo -e "${YELLOW}The test will continue and try to connect from within the Docker network.${NC}"
fi

# Run tests
echo -e "${YELLOW}Running API integration tests...${NC}"
.dockerwrapper/run-test-direct.sh tests/playwright/api-health.spec.js

# Capture test result
TEST_RESULT=$?

# Show test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}==================================================${NC}"
  echo -e "${GREEN}    Integration tests completed successfully!      ${NC}"
  echo -e "${GREEN}==================================================${NC}"
else
  echo -e "${RED}==================================================${NC}"
  echo -e "${RED}    Integration tests failed with code $TEST_RESULT     ${NC}"
  echo -e "${RED}==================================================${NC}"
fi

# Ask if user wants to clean up
read -p "Clean up containers? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo -e "${YELLOW}Cleaning up containers...${NC}"
  docker stop api-test 2>/dev/null || true
  docker rm api-test 2>/dev/null || true
  echo -e "${GREEN}Cleanup complete.${NC}"
else
  echo -e "${YELLOW}Leaving containers running.${NC}"
  echo -e "${YELLOW}You can clean up later with: docker stop api-test && docker rm api-test${NC}"
fi

exit $TEST_RESULT
