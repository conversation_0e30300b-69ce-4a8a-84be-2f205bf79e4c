#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run a test command without authentication.
"""
import sys
import requests
import logging
import argparse

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Run a test command without authentication."""
    parser = argparse.ArgumentParser(description="Run a test command without authentication")
    parser.add_argument(
        "--endpoint",
        default="/api/auth/me",
        help="Endpoint to test"
    )
    parser.add_argument(
        "--method",
        default="GET",
        help="HTTP method to use"
    )
    parser.add_argument(
        "--host",
        default="http://localhost:8000",
        help="Host to connect to"
    )
    
    args = parser.parse_args()
    
    url = f"{args.host}{args.endpoint}"
    logger.info(f"Testing endpoint: {url}")
    
    try:
        if args.method.upper() == "GET":
            response = requests.get(url)
        elif args.method.upper() == "POST":
            response = requests.post(url)
        else:
            logger.error(f"Unsupported method: {args.method}")
            return 1
        
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response body: {response.text}")
        
        if response.status_code == 401:
            logger.error("Authentication was not bypassed")
            return 1
        else:
            logger.info("Authentication was bypassed successfully")
            return 0
    except Exception as e:
        logger.error(f"Error testing endpoint: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 