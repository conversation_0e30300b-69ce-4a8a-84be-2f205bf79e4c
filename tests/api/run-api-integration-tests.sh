#!/bin/bash
# Create network
docker network create test-network 2>/dev/null || true

# Clean up old containers
docker stop api-test 2>/dev/null || true
docker rm api-test 2>/dev/null || true

# Build and start API container
docker build -t api-test-server -f Dockerfile.simple .
docker run -d -p 3055:8000 --name api-test --network test-network api-test-server

# Connect Playwright container to network
docker network connect test-network turdparty_playwright 2>/dev/null || true

# Run tests
.dockerwrapper/run-test-direct.sh tests/playwright/api-health.spec.js
