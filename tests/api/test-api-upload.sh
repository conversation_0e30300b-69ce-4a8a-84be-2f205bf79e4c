#!/bin/bash

# Test API upload endpoints directly with curl
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}   API Upload Endpoints Test              ${NC}"
echo -e "${BLUE}============================================${NC}"

# Default settings
API_BASE_URL="http://localhost:3050"
API_VERSION="v1"
TEST_FILE="test-api-upload.txt"
TOKEN=""

# Check if input token is provided
if [ -n "$1" ]; then
  TOKEN="$1"
  echo -e "${GREEN}Using provided authentication token${NC}"
else
  echo -e "${YELLOW}No auth token provided. You'll be prompted to enter one if needed.${NC}"
fi

# Create test file with unique content
echo -e "${YELLOW}Creating test file: $TEST_FILE${NC}"
echo "Test file for API upload testing" > "$TEST_FILE"
echo "Created at: $(date)" >> "$TEST_FILE"
echo "Random ID: $(date +%s)" >> "$TEST_FILE"
echo "Testing upload functionality" >> "$TEST_FILE"

# Check API health endpoint
echo -e "${YELLOW}Checking API health...${NC}"
HEALTH_RESPONSE=$(curl -s $API_BASE_URL/api/$API_VERSION/health/)
if [[ $HEALTH_RESPONSE == *"ok"* ]]; then
  echo -e "${GREEN}API is healthy!${NC}"
else
  echo -e "${RED}API health check failed!${NC}"
  echo "Response: $HEALTH_RESPONSE"
  exit 1
fi

# Test endpoints without auth token first
echo -e "${YELLOW}Testing endpoints without auth token to verify auth is working...${NC}"

# Try uploading without auth
NO_AUTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -F "file=@$TEST_FILE" -F "description=Test upload without auth" $API_BASE_URL/api/$API_VERSION/file_upload/)

if [[ $NO_AUTH_RESPONSE == "401" || $NO_AUTH_RESPONSE == "403" ]]; then
  echo -e "${GREEN}Authentication check passed! (Got $NO_AUTH_RESPONSE as expected)${NC}"
else
  echo -e "${RED}Authentication check failed! Expected 401/403, got $NO_AUTH_RESPONSE${NC}"
  if [[ $NO_AUTH_RESPONSE == "200" || $NO_AUTH_RESPONSE == "201" ]]; then
    echo -e "${YELLOW}Warning: Endpoint allowed upload without authentication!${NC}"
  fi
fi

# If no token provided, prompt for one now
if [ -z "$TOKEN" ]; then
  echo -e "${YELLOW}Please enter an authentication token to continue testing:${NC}"
  read -r TOKEN
  if [ -z "$TOKEN" ]; then
    echo -e "${RED}No token provided, exiting test.${NC}"
    exit 1
  fi
fi

# Test all upload endpoints with auth token
echo -e "${YELLOW}Testing upload endpoints with auth token...${NC}"

# Test 1: Direct file_upload endpoint
echo -e "${YELLOW}Test 1: Direct file_upload endpoint${NC}"
UPLOAD_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" -F "file=@$TEST_FILE" -F "description=Test upload via api/file_upload" $API_BASE_URL/api/$API_VERSION/file_upload/)
echo "$UPLOAD_RESPONSE" | grep -q "id" && echo -e "${GREEN}✓ Upload successful!${NC}" || echo -e "${RED}✗ Upload failed${NC}"
echo "$UPLOAD_RESPONSE" | grep -q "download_url" && echo -e "${GREEN}✓ Download URL returned${NC}" || echo -e "${RED}✗ No download URL${NC}"
echo "Response:"
echo "$UPLOAD_RESPONSE" | head -20

# Extract ID if available for download test
FILE_ID=$(echo "$UPLOAD_RESPONSE" | grep -o '"id":"[^"]*' | sed 's/"id":"//' | head -1)
if [ -n "$FILE_ID" ]; then
  echo -e "${GREEN}Extracted file ID: $FILE_ID${NC}"
fi

# Test 2: Upload alias endpoint
echo -e "${YELLOW}Test 2: Upload alias endpoint${NC}"
ALIAS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" -F "file=@$TEST_FILE" -F "description=Test upload via api/upload" $API_BASE_URL/api/$API_VERSION/upload/)
echo "$ALIAS_RESPONSE" | grep -q "id" && echo -e "${GREEN}✓ Upload successful!${NC}" || echo -e "${RED}✗ Upload failed${NC}"
echo "Response:"
echo "$ALIAS_RESPONSE" | head -20

# Test 3: Download the uploaded file
if [ -n "$FILE_ID" ]; then
  echo -e "${YELLOW}Test 3: Downloading uploaded file${NC}"
  DOWNLOAD_URL="$API_BASE_URL/api/$API_VERSION/file_upload/download/$FILE_ID"
  echo -e "${YELLOW}Download URL: $DOWNLOAD_URL${NC}"
  
  DOWNLOAD_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" -o "downloaded-$TEST_FILE" -w "%{http_code}" "$DOWNLOAD_URL")
  
  if [[ $DOWNLOAD_RESPONSE == "200" ]]; then
    echo -e "${GREEN}✓ File download successful!${NC}"
    echo -e "${YELLOW}Comparing downloaded file with original...${NC}"
    
    if cmp -s "$TEST_FILE" "downloaded-$TEST_FILE"; then
      echo -e "${GREEN}✓ Files are identical!${NC}"
    else
      echo -e "${RED}✗ Files are different!${NC}"
      echo -e "${YELLOW}Original file:${NC}"
      cat "$TEST_FILE" | head -5
      echo -e "${YELLOW}Downloaded file:${NC}"
      cat "downloaded-$TEST_FILE" | head -5
    fi
  else
    echo -e "${RED}✗ File download failed with status $DOWNLOAD_RESPONSE${NC}"
  fi
fi

# Test 4: List uploaded files
echo -e "${YELLOW}Test 4: Listing uploaded files${NC}"
LIST_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" $API_BASE_URL/api/$API_VERSION/file_upload/)
echo "$LIST_RESPONSE" | grep -q "id" && echo -e "${GREEN}✓ File listing successful!${NC}" || echo -e "${RED}✗ File listing failed${NC}"

if [ -n "$FILE_ID" ]; then
  echo "$LIST_RESPONSE" | grep -q "$FILE_ID" && echo -e "${GREEN}✓ Uploaded file found in listing!${NC}" || echo -e "${RED}✗ Uploaded file not found in listing${NC}"
fi

echo "Response (truncated):"
echo "$LIST_RESPONSE" | head -30

# Test 5: Folder upload endpoint
echo -e "${YELLOW}Test 5: Folder upload endpoint${NC}"
# Create a second test file for folder upload
SECOND_FILE="test-api-upload-2.txt"
echo "Second test file for folder upload testing" > "$SECOND_FILE"
echo "Created at: $(date)" >> "$SECOND_FILE"

# Attempt folder upload
FOLDER_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  -F "files=@$TEST_FILE" \
  -F "files=@$SECOND_FILE" \
  -F "paths=file1.txt" \
  -F "paths=subfolder/file2.txt" \
  -F "description=Test folder upload" \
  $API_BASE_URL/api/$API_VERSION/file_upload/folder/)

echo "$FOLDER_RESPONSE" | grep -q "folder_id" && echo -e "${GREEN}✓ Folder upload successful!${NC}" || echo -e "${RED}✗ Folder upload failed${NC}"
echo "Response:"
echo "$FOLDER_RESPONSE" | head -20

# Clean up
echo -e "${YELLOW}Cleaning up test files...${NC}"
rm -f "$TEST_FILE" "downloaded-$TEST_FILE" "$SECOND_FILE"

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}   API Upload Endpoints Test Complete     ${NC}"
echo -e "${BLUE}============================================${NC}" 