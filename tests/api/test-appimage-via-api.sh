#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing AppImage Operations via Application API${NC}"
echo -e "${YELLOW}=========================================${NC}"

# Configuration
TEST_DIR="/tmp/appimage-api-test"
APPIMAGE_FILE="${TEST_DIR}/test-appimage.AppImage"
API_URL="http://localhost:3050"  # Updated port based on docker-compose ps output (3050)
API_VERSION="v1"
API_BASE="${API_URL}/api/${API_VERSION}"
DOCKERWRAPPER_DIR=".dockerwrapper"

# Check if API is running
echo -e "${YELLOW}Checking if API is running at ${API_URL}...${NC}"
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${API_URL}/api/${API_VERSION}/health/ 2>/dev/null || echo "failed")

if [ "$API_STATUS" != "200" ]; then
    echo -e "${RED}Error: API is not running or accessible at ${API_URL}${NC}"
    echo -e "${YELLOW}Please start the API service manually and try again.${NC}"
    echo
    echo -e "${BLUE}API Service Options:${NC}"
    echo -e "1. Start the API directly on your host system"
    echo -e "2. If using Docker, verify your Docker containers are working properly first"
    echo -e "   You may need to rebuild or fix container issues before testing"
    echo
    echo -e "${BLUE}Troubleshooting:${NC}"
    echo -e "- Check if the API service is running: ${YELLOW}ps aux | grep api${NC}"
    echo -e "- Check Docker container status: ${YELLOW}docker ps${NC}"
    echo -e "- Check Docker container logs: ${YELLOW}docker logs <container-id>${NC}"
    echo -e "- Verify API port is accessible - Current port: ${YELLOW}3050${NC}"
    echo -e "- Check for database issues: ${YELLOW}docker logs regrigor-api${NC}"
    echo -e "- For SQL type mismatch errors, fix the database models"
    echo
    echo -e "After fixing the issues, run this script again."
    exit 1
else
    echo -e "${GREEN}API is running!${NC}"
fi

# Create test directory if it doesn't exist
mkdir -p "${TEST_DIR}"

# Create a dummy AppImage file for testing
echo -e "${YELLOW}Creating dummy AppImage file for testing...${NC}"
cat > ${APPIMAGE_FILE} << 'EOF'
#!/bin/bash
echo "This is a dummy AppImage executable for API testing - $(date)"
echo "Environment:"
env
echo "System info:"
uname -a
echo "Current directory: $(pwd)"
echo "AppImage API test complete!"
EOF

# Make the dummy AppImage executable
chmod +x ${APPIMAGE_FILE}
echo -e "${GREEN}Created dummy AppImage at ${APPIMAGE_FILE}${NC}"

# Create Python script for API testing
cat > ${TEST_DIR}/api_test.py << 'EOF'
#!/usr/bin/env python3
"""Test AppImage operations through the application API."""

import os
import sys
import json
import time
import requests
import argparse
from datetime import datetime

# Configuration from environment variables
API_URL = os.environ.get("API_URL", "http://localhost:3050")
API_VERSION = os.environ.get("API_VERSION", "v1")
API_BASE = f"{API_URL}/api/{API_VERSION}"
APPIMAGE_FILE = os.environ.get("APPIMAGE_FILE", "/tmp/appimage-api-test/test-appimage.AppImage")

def log(message):
    """Print message with timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_api_health():
    """Check if API is running and accessible."""
    try:
        resp = requests.get(f"{API_BASE}/health/")
        if resp.status_code == 200:
            log(f"API health check successful: {resp.status_code}")
            return True
        else:
            log(f"API health check failed: {resp.status_code}")
            return False
    except Exception as e:
        log(f"API health check error: {str(e)}")
        return False

def get_auth_token():
    """Get authentication token from API."""
    try:
        # Try test token endpoint for development
        resp = requests.post(f"{API_BASE}/auth/test-token")
        if resp.status_code == 200:
            token = resp.json().get("access_token")
            log("Test token obtained")
            return token
        
        # Try login with default credentials
        log("Trying login with default credentials...")
        data = {"username": "<EMAIL>", "password": "password123"}
        resp = requests.post(f"{API_BASE}/auth/login", json=data)
        if resp.status_code == 200:
            token = resp.json().get("access_token")
            log("Login successful, token obtained")
            return token
        
        log(f"Authentication failed: {resp.status_code}")
        return None
    except Exception as e:
        log(f"Authentication error: {str(e)}")
        return None

def upload_appimage(token):
    """Upload AppImage through file upload API."""
    if not os.path.exists(APPIMAGE_FILE):
        log(f"Error: AppImage file not found at {APPIMAGE_FILE}")
        return None
    
    log(f"Uploading AppImage: {APPIMAGE_FILE}")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Get file info
        file_size = os.path.getsize(APPIMAGE_FILE)
        filename = os.path.basename(APPIMAGE_FILE)
        
        # Upload file
        with open(APPIMAGE_FILE, "rb") as f:
            files = {"file": (filename, f, "application/x-executable")}
            data = {"description": "Test AppImage uploaded via API"}
            
            # Start upload
            log(f"Starting upload of {file_size} bytes...")
            resp = requests.post(f"{API_BASE}/file_upload", headers=headers, files=files, data=data)
            
            if resp.status_code not in [200, 201]:
                log(f"Upload failed: {resp.status_code}")
                log(resp.text)
                return None
            
            result = resp.json()
            log(f"Upload successful! Response: {json.dumps(result, indent=2)}")
            return result
    except Exception as e:
        log(f"Upload error: {str(e)}")
        return None

def get_file_list(token):
    """Get list of uploaded files."""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(f"{API_BASE}/file_upload", headers=headers)
        
        if resp.status_code != 200:
            log(f"Failed to get file list: {resp.status_code}")
            log(resp.text)
            return None
        
        files = resp.json()
        log(f"Found {len(files)} files in the system")
        
        # Display file info
        for i, file_info in enumerate(files):
            log(f"{i+1}. {file_info.get('filename', 'Unknown')} ({file_info.get('id', 'No ID')})")
            log(f"   Size: {file_info.get('file_size', 0)} bytes")
            log(f"   Uploaded: {file_info.get('created_at', 'Unknown')}")
            log(f"   Download URL: {file_info.get('download_url', 'No URL')}")
        
        return files
    except Exception as e:
        log(f"Error getting file list: {str(e)}")
        return None

def download_file(token, file_id):
    """Download a file by ID."""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(f"{API_BASE}/file_upload/{file_id}/download", headers=headers)
        
        if resp.status_code != 200:
            log(f"Download failed: {resp.status_code}")
            log(resp.text)
            return False
        
        # Save the file
        download_path = f"{os.path.dirname(APPIMAGE_FILE)}/downloaded-appimage.AppImage"
        with open(download_path, "wb") as f:
            f.write(resp.content)
        
        log(f"File downloaded to: {download_path}")
        log(f"Download size: {os.path.getsize(download_path)} bytes")
        
        # Make executable
        os.chmod(download_path, 0o755)
        log("Made downloaded file executable")
        
        # Clean up
        os.remove(download_path)
        log("Removed downloaded file")
        
        return True
    except Exception as e:
        log(f"Download error: {str(e)}")
        return False

def test_minio_status(token):
    """Test MinIO status API."""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(f"{API_BASE}/minio-status", headers=headers)
        
        if resp.status_code != 200:
            log(f"MinIO status check failed: {resp.status_code}")
            log(resp.text)
            return False
        
        status = resp.json()
        log(f"MinIO status: {json.dumps(status, indent=2)}")
        return True
    except Exception as e:
        log(f"MinIO status error: {str(e)}")
        return False

def test_minio_health(token):
    """Test MinIO health API."""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(f"{API_BASE}/minio-health", headers=headers)
        
        if resp.status_code != 200:
            log(f"MinIO health check failed: {resp.status_code}")
            log(resp.text)
            return False
        
        health = resp.json()
        log(f"MinIO health: {json.dumps(health, indent=2)}")
        return True
    except Exception as e:
        log(f"MinIO health error: {str(e)}")
        return False

def main():
    """Main function to run all API tests."""
    parser = argparse.ArgumentParser(description="Test AppImage API operations")
    parser.add_argument("--health-only", action="store_true", help="Only test API health")
    parser.add_argument("--upload-only", action="store_true", help="Only test file upload")
    parser.add_argument("--no-upload", action="store_true", help="Skip file upload test")
    parser.add_argument("--no-download", action="store_true", help="Skip file download test")
    args = parser.parse_args()
    
    log("Starting AppImage API tests...")
    
    # Check API health
    if not check_api_health():
        log("API is not accessible. Exiting.")
        sys.exit(1)
    
    if args.health_only:
        log("Health check completed. Exiting as requested.")
        return
    
    # Get auth token
    token = get_auth_token()
    if not token:
        log("Failed to get authentication token. Exiting.")
        sys.exit(1)
    
    # Test MinIO status and health endpoints
    log("\nTesting MinIO status endpoint...")
    test_minio_status(token)
    
    log("\nTesting MinIO health endpoint...")
    test_minio_health(token)
    
    # Upload test
    uploaded_file = None
    if not args.no_upload:
        log("\nTesting AppImage upload...")
        uploaded_file = upload_appimage(token)
        if not uploaded_file and not args.upload_only:
            log("Upload failed but continuing with other tests...")
    elif args.upload_only:
        log("Skipping upload as requested.")
        return
    else:
        log("Skipping upload as requested.")
    
    # Get file list
    log("\nTesting file listing...")
    files = get_file_list(token)
    
    # Download test
    if not args.no_download and files and len(files) > 0:
        log("\nTesting file download...")
        file_to_download = uploaded_file if uploaded_file else files[0]
        file_id = file_to_download.get("id")
        if file_id:
            download_file(token, file_id)
        else:
            log("No file ID available for download test")
    elif args.no_download:
        log("Skipping download as requested.")
    
    log("\nAPI tests completed!")

if __name__ == "__main__":
    main()
EOF

# Make the script executable
chmod +x ${TEST_DIR}/api_test.py

# Set environment variables
export API_URL="${API_URL}"
export API_VERSION="${API_VERSION}"
export APPIMAGE_FILE="${APPIMAGE_FILE}"

# Run the API tests
echo -e "${YELLOW}Running AppImage API tests...${NC}"
cd ${TEST_DIR}
python3 api_test.py "$@"
cd - > /dev/null

# Deactivate virtual environment
deactivate

# Clean up
echo -e "${YELLOW}Cleaning up test files...${NC}"
rm -f "${APPIMAGE_FILE}"
rm -f "${TEST_DIR}/api_test.py"
rmdir "${TEST_DIR}" 2>/dev/null || true

echo -e "${GREEN}Test complete!${NC}" 