# AI Reference Scanner

This tool scans the codebase for references to AI tools like <PERSON> or <PERSON><PERSON><PERSON> that should be reviewed and potentially removed before production deployment.

## Purpose

When developing with AI assistance, it's common to leave comments, instructions, or references to specific AI tools in the codebase. These references should be removed or sanitized before production deployment to:

1. Maintain a professional codebase
2. Remove potentially sensitive information
3. Eliminate references to specific tools or vendors
4. Ensure the codebase is self-contained and doesn't rely on external context

## Features

- Scans for references to common AI tools (Claude, Cursor, etc.)
- Identifies AI-related terminology in code and documentation
- Generates a comprehensive Markdown report of findings
- Provides statistics on reference types and locations
- Optionally attempts to automatically comment out references

## Usage

```bash
# Basic scan with default settings
python scan_ai_references.py

# Scan a specific directory
python scan_ai_references.py --path ./src

# Generate report with a custom filename
python scan_ai_references.py --output ai_scan_results.md

# Enable verbose output
python scan_ai_references.py --verbose

# Attempt to automatically comment out references
python scan_ai_references.py --fix
```

## Report Format

The generated report includes:

1. **Summary statistics**: Total files scanned, files with references, total references found
2. **Category breakdown**: References by AI tool/category
3. **File type breakdown**: References by file extension
4. **Detailed findings**: Line-by-line listing of each reference with context
5. **Recommendations**: Suggested actions for addressing the findings

## Configuration

The scanner is configured with several predefined lists:

- **AI_TERMS**: Patterns to search for, organized by category
- **FILE_EXTENSIONS**: File types to scan
- **EXCLUDE_DIRS**: Directories to exclude from scanning
- **EXCLUDE_FILES**: Specific files to exclude
- **ALLOWED_REFERENCES**: Patterns that are allowed (e.g., in documentation about the project structure)

These can be modified in the script to customize the scanning behavior.

## Integration with CI/CD

This scanner can be integrated into CI/CD pipelines to:

1. Fail builds that contain unauthorized AI references
2. Generate reports as build artifacts
3. Track reference counts over time
4. Automatically sanitize references in development branches

Example GitHub Actions workflow:

```yaml
name: AI Reference Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'
      - name: Scan for AI references
        run: python scan_ai_references.py --output ai_scan_results.md
      - name: Upload scan results
        uses: actions/upload-artifact@v2
        with:
          name: ai-scan-results
          path: ai_scan_results.md
      - name: Check for unauthorized references
        run: |
          REFERENCE_COUNT=$(grep -c "Total references found:" ai_scan_results.md | cut -d ":" -f2 | tr -d " ")
          if [ "$REFERENCE_COUNT" -gt "0" ]; then
            echo "Found $REFERENCE_COUNT AI references that need review"
            exit 1
          fi
```

## Best Practices

1. **Run regularly**: Scan the codebase regularly during development
2. **Pre-commit hook**: Set up a pre-commit hook to catch references early
3. **Review context**: Always review the context of each reference before removing
4. **Documentation**: Update documentation to use generic terms instead of specific AI tools
5. **Allowed references**: Maintain a list of allowed references for legitimate use cases

## License

This tool is released under the MIT License. See the LICENSE file for details.
