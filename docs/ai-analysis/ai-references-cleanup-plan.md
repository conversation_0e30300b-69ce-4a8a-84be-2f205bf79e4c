# AI References & Markdown Cleanup Plan

**Date**: 2025-06-04  
**Status**: Ready for Execution  
**Scope**: Clean up AI references outside `.ai/` directory and archive redundant markdown files

## 🔍 AI References Analysis Summary

### Scan Results
- **Total files scanned**: 1,847
- **Files with AI references**: 67
- **Total AI references**: 1,097
- **Categories**: 
  - Cursor: 794 references (IDE configuration)
  - Claude: 163 references (AI assistant mentions)
  - LLM: 140 references (general AI/ML terms)

## 📋 Cleanup Categories

### 1. **KEEP - Functional AI Tools** ⚠️
**Files to preserve** (functional development tools):
- `.cursor/rules/*.mdc` - Cursor IDE configuration files
- `.focus_forge/.cursor/rules/*.mdc` - Focus Forge Cursor integration
- `scan_ai_references.py` - AI reference scanner utility
- `AI_SCANNER_README.md` / `ai_scanner_README.md` - Scanner documentation
- `ai_references_report.md` - Generated scan report

**Rationale**: These are functional development tools and utilities.

### 2. **CLEAN - Documentation References** 🧹
**Files to clean** (replace AI-specific terms with generic ones):
- `CHANGELOG.md` - Contains `.claude/` path references
- Various docs mentioning specific AI tools in examples
- Configuration files with AI tool names

**Action**: Replace specific AI tool names with generic terms like "AI assistant" or "development tool".

### 3. **ARCHIVE - Redundant Markdown Files** 🗑️
**Files identified for archival** (temporary, output, or redundant):

#### Upload Documentation (Redundant)
- `upload-implementation-todos.md` - TODO list (likely outdated)
- `upload-status-diagram.md` - Status diagram
- `upload-testing-findings.md` - Test findings
- `upload-test-summary.md` - Test summary
- `file-upload-e2e-status.md` - E2E status report
- `file-upload-testing.md` - Testing documentation

#### Implementation Status Files (Temporary)
- `cachet-implementation-complete.md` - Implementation complete notice
- `cachet-implementation-summary.md` - Implementation summary
- `INTEGRATION-TEST-SUMMARY.md` - Integration test summary
- `remediation-plan.md` - Remediation plan
- `refactor_friday.md` - Refactor notes

#### Test Output Files (Logs)
- `test_logs/streamlit_to_flask_conversion_*.md` - Conversion logs
- `test_logs/*.txt` - Test output files
- `test_logs/*.log` - Log files

#### Analysis Output Files (Generated)
- `model_analysis.md` - Keep (valuable technical analysis)
- `target_model_state.md` - Keep (technical specification)

### 4. **REVIEW - Potentially Valuable** 🔍
**Files requiring manual review**:
- `health_checks_proposal.md` - Health checks proposal
- `api-frontend-alignment.md` - API alignment documentation
- `folder-structure-cleanup-prd.md` - Cleanup PRD
- `cleanup-phase-checklists.md` - Cleanup checklists
- `playwright-test-plan.md` - Test plan

## 🎯 Execution Plan

### Phase 1: Archive Redundant Files
1. Create `_archive_/markdown-cleanup-2025-06-04/` directory
2. Move redundant files to archive with documentation
3. Update any references to moved files

### Phase 2: Clean AI References
1. Replace specific AI tool names with generic terms
2. Update documentation to use neutral language
3. Preserve functional configurations

### Phase 3: Verify and Document
1. Run AI scanner again to verify cleanup
2. Update project structure PRD with cleanup results
3. Document archived files and reasons

## 📁 Archive Structure

```
_archive_/
└── markdown-cleanup-2025-06-04/
    ├── upload-docs/
    │   ├── upload-implementation-todos.md
    │   ├── upload-status-diagram.md
    │   ├── upload-testing-findings.md
    │   ├── upload-test-summary.md
    │   ├── file-upload-e2e-status.md
    │   └── file-upload-testing.md
    ├── implementation-status/
    │   ├── cachet-implementation-complete.md
    │   ├── cachet-implementation-summary.md
    │   ├── INTEGRATION-TEST-SUMMARY.md
    │   ├── remediation-plan.md
    │   └── refactor_friday.md
    ├── test-logs/
    │   ├── streamlit_to_flask_conversion_20250407_115225.md
    │   ├── streamlit_to_flask_conversion_20250407_115358.md
    │   └── [other test output files]
    └── README.md (documentation of archived files)
```

## ✅ Success Criteria

1. **AI References Reduced**: Significant reduction in AI tool references outside `.ai/`
2. **Functional Tools Preserved**: All functional development tools remain intact
3. **Documentation Cleaned**: Generic language used instead of specific AI tool names
4. **Redundant Files Archived**: Temporary and output files moved to archive
5. **Project Structure Improved**: Cleaner root directory with better organization

## 🚨 Risks and Mitigations

### Risks
1. **Accidentally removing functional files**
2. **Breaking references to moved files**
3. **Losing valuable documentation**

### Mitigations
1. **Archive instead of delete** - All files moved to `_archive_/` for recovery
2. **Comprehensive review** - Manual review of each file before archival
3. **Documentation** - Detailed documentation of all changes made
4. **Git tracking** - All changes committed with detailed commit messages

## 📊 Expected Results

- **Cleaner repository structure** with reduced clutter
- **Improved developer experience** with better organized documentation
- **Reduced AI tool dependencies** in documentation
- **Preserved functionality** of all development tools
- **Better maintainability** of the codebase

---

**Next Steps**: Execute Phase 1 (Archive Redundant Files) upon approval.
