# Docker Compose Health Checks Proposal

This document outlines a proposal for adding health checks to all services in the Docker Compose configuration for the TurdParty project.

## Current Status

The current Docker Compose configuration already includes health checks for the following services:
- postgres
- minio
- redis

However, the following services do not have health checks:
- api
- minio-ssh
- celery-worker
- celery-flower

## Benefits of Health Checks

Adding health checks to all services provides several benefits:

1. **Improved Reliability**: Docker can automatically restart unhealthy containers
2. **Better Dependency Management**: Services can depend on the health status of other services, not just their existence
3. **Enhanced Monitoring**: Health status can be monitored through Docker's API or CLI
4. **Simplified Debugging**: Easier to identify which service is causing issues
5. **Graceful Deployment**: Ensures services are fully ready before dependent services start

## Proposed Health Checks

Here are the proposed health checks for each service that currently lacks one:

### API Service

```yaml
api:
  # ... existing configuration ...
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
    interval: 10s
    timeout: 5s
    retries: 3
    start_period: 20s
```

This health check:
- Uses curl to make a request to the API's health endpoint
- Runs every 10 seconds
- Times out after 5 seconds
- Retries 3 times before marking the container as unhealthy
- Allows a 20-second start period for the API to initialize

### MinIO SSH Service

```yaml
minio-ssh:
  # ... existing configuration ...
  healthcheck:
    test: ["CMD", "ssh", "-o", "StrictHostKeyChecking=no", "localhost", "echo OK"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 10s
```

This health check:
- Attempts to establish an SSH connection to localhost
- Runs every 30 seconds
- Times out after 10 seconds
- Retries 3 times before marking the container as unhealthy
- Allows a 10-second start period for SSH to initialize

### Celery Worker Service

```yaml
celery-worker:
  # ... existing configuration ...
  healthcheck:
    test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s
```

This health check:
- Uses Celery's inspect ping command to check if workers are responding
- Runs every 30 seconds
- Times out after 10 seconds
- Retries 3 times before marking the container as unhealthy
- Allows a 30-second start period for Celery workers to initialize

### Celery Flower Service

```yaml
celery-flower:
  # ... existing configuration ...
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:5555/api/workers"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s
```

This health check:
- Uses curl to make a request to Flower's API endpoint
- Runs every 30 seconds
- Times out after 10 seconds
- Retries 3 times before marking the container as unhealthy
- Allows a 30-second start period for Flower to initialize

## Implementation Plan

1. Update the docker-compose.yml file with the proposed health checks
2. Test each health check individually to ensure it correctly identifies healthy and unhealthy states
3. Update service dependencies to use condition: service_healthy where appropriate
4. Document the health check endpoints and their expected responses

## Example of Updated Dependencies

```yaml
api:
  # ... existing configuration ...
  depends_on:
    postgres:
      condition: service_healthy
    minio:
      condition: service_healthy
    redis:
      condition: service_healthy

celery-worker:
  # ... existing configuration ...
  depends_on:
    redis:
      condition: service_healthy
    api:
      condition: service_healthy

celery-flower:
  # ... existing configuration ...
  depends_on:
    redis:
      condition: service_healthy
    celery-worker:
      condition: service_healthy
```

This ensures that services only start when their dependencies are actually healthy and ready to accept connections, not just when the container has started.
