# Database Model Analysis (T11)

## 1. Current Model Structure

### 1.1 Base Model

The database uses SQLAlchemy with a `BaseModel` abstract class that provides common fields and functionality:

```python
class BaseModel(Base):
    __abstract__ = True
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_on = Column(DateTime, nullable=False, default=datetime.utcnow, server_default=func.now())
    modified_on = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, server_default=func.now())
    deleted_on = Column(DateTime, nullable=True)
    
    # Methods for soft delete functionality
    def soft_delete(self) -> None
    def is_deleted(self) -> bool  # property
    def restore(self) -> None
```

### 1.2 Model Inventory

| Model Name | Table Name | Purpose | Inherits from BaseModel |
|------------|------------|---------|-------------------------|
| User | users | Store user information | Yes |
| Item | items | Example item data | Yes |
| Vagrant_vm | vagrant_vm | VM instances | No (inherits directly from Base) |
| FileUpload | file_uploads | Uploaded files | No (inherits directly from Base) |
| FileSelection | file_selections | Selected files | Unknown |
| Session | sessions | User sessions | Unknown |
| Vm_injection | vm_injection | VM file injections | Unknown |
| Audit | audit_logs | Audit logging | Unknown |
| HashReport | hash_reports | File hash reports | Unknown |

## 2. Model Details

### 2.1 User Model

```python
class User(BaseModel):
    __tablename__ = "users"
    __table_args__ = {'extend_existing': True}

    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=False)
    password_hash = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)

    # Relationships
    sessions = relationship("Session", back_populates="user", cascade="all, delete-orphan")
    vagrant_vms = relationship("Vagrant_vm", back_populates="owner", cascade="all, delete-orphan")
    vm_injections = relationship("Vm_injection", back_populates="owner", cascade="all, delete-orphan")
    file_uploads = relationship("FileUpload", back_populates="owner", cascade="all, delete-orphan")
    file_selections = relationship("FileSelection", back_populates="owner", cascade="all, delete-orphan")
    items = relationship("Item", back_populates="owner", cascade="all, delete-orphan")
```

### 2.2 Item Model

```python
class Item(BaseModel):
    __tablename__ = "items"
    __table_args__ = {'extend_existing': True}
    
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Foreign key
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), index=True)
    
    # Relationship
    owner = relationship("User", back_populates="items")
```

### 2.3 Vagrant_vm Model

```python
class Vagrant_vm(Base):  # Note: Inherits from Base, not BaseModel
    __tablename__ = "vagrant_vm"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    template = Column(String, nullable=False)
    memory_mb = Column(Integer, nullable=False, default=1024)
    cpus = Column(Integer, nullable=False, default=1)
    disk_gb = Column(Integer, nullable=False, default=20)
    status = Column(String, nullable=False, default="pending")  # Should be an enum
    ip_address = Column(String, nullable=True)
    ssh_port = Column(Integer, nullable=True)
    vagrant_id = Column(String, nullable=True)
    created_on = Column(DateTime, nullable=False, default=datetime.utcnow)
    modified_on = Column(DateTime, nullable=True, onupdate=datetime.utcnow)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    last_action = Column(String, nullable=True)
    last_action_time = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    domain = Column(String, nullable=False, default="TurdParty")
    
    # Relationships
    owner = relationship("User", back_populates="vagrant_vms")
    vm_injections = relationship("Vm_injection", back_populates="vagrant_vm", cascade="all, delete-orphan")
```

### 2.4 FileUpload Model

```python
class FileUpload(Base):  # Note: Inherits from Base, not BaseModel
    __tablename__ = "file_uploads"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    filename = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String, nullable=False)
    file_hash = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_folder_path = Column(String, nullable=True)
    folder_id = Column(String, nullable=True, index=True)
    description = Column(Text, nullable=True)
    download_url = Column(String, nullable=False)
    created_on = Column(DateTime, default=datetime.datetime.utcnow, nullable=False)
    modified_on = Column(DateTime, onupdate=datetime.datetime.utcnow)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, 
                     default=uuid.UUID("e3c704f3-c398-4894-bd7b-a1d092dada04"))
    is_active = Column(Boolean, default=True, nullable=False)
    vm_id = Column(UUID(as_uuid=True), ForeignKey("vagrant_vm.id"), nullable=True)
    
    # Relationships
    owner = relationship("User", back_populates="file_uploads")
    selections = relationship("FileSelection", back_populates="file_upload", cascade="all, delete-orphan")
```

## 3. Relationships Analysis

### 3.1 Relationship Overview

```
User
  ├── sessions (one-to-many)
  ├── vagrant_vms (one-to-many)
  ├── vm_injections (one-to-many)
  ├── file_uploads (one-to-many)
  ├── file_selections (one-to-many)
  └── items (one-to-many)

Vagrant_vm
  ├── owner (many-to-one with User)
  └── vm_injections (one-to-many)

FileUpload
  ├── owner (many-to-one with User)
  ├── selections (one-to-many with FileSelection)
  └── [implicit] vagrant_vm (many-to-one)

Item
  └── owner (many-to-one with User)
```

### 3.2 Detailed Relationship Analysis

#### 3.2.1 User Relationships

| Relationship | Type | Foreign Key | Cascade | Comments |
|--------------|------|-------------|---------|----------|
| sessions | one-to-many | Unknown | all, delete-orphan | Back-populates "user" |
| vagrant_vms | one-to-many | vagrant_vm.owner_id | all, delete-orphan | Back-populates "owner" |
| vm_injections | one-to-many | Unknown | all, delete-orphan | Back-populates "owner" |
| file_uploads | one-to-many | file_uploads.owner_id | all, delete-orphan | Back-populates "owner" |
| file_selections | one-to-many | Unknown | all, delete-orphan | Back-populates "owner" |
| items | one-to-many | items.owner_id | all, delete-orphan | Back-populates "owner" |

The User model functions as a central entity with relationships to all other main entities in the system. All relationships use the same cascade behavior of "all, delete-orphan", which means when a User is deleted:
- All related records in dependent tables are deleted (delete-orphan)
- Changes to the user (updates, deletes) propagate to related records (all)

#### 3.2.2 Item Relationships

| Relationship | Type | Foreign Key | Cascade | Comments |
|--------------|------|-------------|---------|----------|
| owner | many-to-one | items.owner_id | None specified | Back-populates "items" |

The Item model has a straightforward many-to-one relationship with User.

#### 3.2.3 Vagrant_vm Relationships

| Relationship | Type | Foreign Key | Cascade | Comments |
|--------------|------|-------------|---------|----------|
| owner | many-to-one | vagrant_vm.owner_id | None specified | Back-populates "vagrant_vms" |
| vm_injections | one-to-many | Unknown | all, delete-orphan | Back-populates "vagrant_vm" |

The Vagrant_vm model has a bidirectional relationship with User and acts as a parent to Vm_injection entities.

#### 3.2.4 FileUpload Relationships

| Relationship | Type | Foreign Key | Cascade | Comments |
|--------------|------|-------------|---------|----------|
| owner | many-to-one | file_uploads.owner_id | None specified | Back-populates "file_uploads" |
| selections | one-to-many | Unknown | all, delete-orphan | Back-populates "file_upload" |

The FileUpload model has a missing relationship definition for the vm_id foreign key. There's a foreign key to vagrant_vm.id but no relationship property defined.

### 3.3 Relationship Patterns

1. **Ownership Pattern**: Most entities have an "owner" relationship to User
   - Consistent use of owner/owner_id naming
   - All owned entities use cascade="all, delete-orphan"
   - Foreign keys consistently point to users.id

2. **Bidirectional Relationships**: All defined relationships are bidirectional
   - All use back_populates for bidirectional linking
   - No use of backref (older SQLAlchemy pattern)

3. **Cascade Behavior**: 
   - Parent/Owner side: All use "all, delete-orphan" cascade
   - Child/Owned side: None specify cascade behavior

## 4. Issues Identified

### 4.1 Inheritance Inconsistency

- Models inconsistently inherit from either `BaseModel` or directly from `Base`
- `Vagrant_vm` and `FileUpload` don't inherit from `BaseModel` despite having similar needs
- Inconsistent timestamps and field naming pattern across models

### 4.2 Missing Type Hints

- Most models lack proper type annotations/hints
- Parameter and return type hints missing from methods
- Inconsistent use of typing imports

### 4.3 Validation Issues

#### 4.3.1 Data Type Validation

| Model | Field | Current | Issue | Recommendation |
|-------|-------|---------|-------|---------------|
| User | username | String | No length limit | String(50), min_length validation |
| User | email | String | No format validation | String(255), email validator |
| User | password_hash | String | No length requirements | String(255), min_length validation |
| Item | title | String(255) | No min length | Add min_length validation |
| Vagrant_vm | name | String | No length limit | String(100), min_length validation |
| Vagrant_vm | status | String | Should be enum | Create VMStatus enum, use Enum type |
| FileUpload | filename | String | No length limit | String(255), validate characters |
| FileUpload | content_type | String | No validation | Validate against allowed MIME types |
| FileUpload | file_hash | String | No validation | Validate hash format |

#### 4.3.2 Business Logic Validation

The models lack validators for business rules. Examples of missing validations:

1. **User Model**:
   - No email format validation
   - No username format validation (allowed characters)
   - No password complexity validation
   - No checks for active status changes

2. **Vagrant_vm Model**:
   - No validation for resource limits (min/max values)
   - No state transition validation for status field
   - No validation for SSH port range
   - No IP address format validation

3. **FileUpload Model**:
   - No validation for allowed file types
   - No validation for maximum file size
   - No validation for secure file paths
   - Hard-coded UUID value as default for owner_id

#### 4.3.3 Database-Level Constraints

Missing database constraints that should be defined in `__table_args__`:

```python
__table_args__ = (
    # Example constraints that should be added
    CheckConstraint('LENGTH(username) >= 3', name='check_username_length'),
    CheckConstraint('email ~* \'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$\'', name='check_email_format'),
    # More constraints...
)
```

#### 4.3.4 Missing Pre/Post Validation Hooks

No hooks for validation before/after operations:

```python
# Example of pre-validation hook that should be implemented
@validates('email')
def validate_email(self, key, email):
    if not re.match(r"[^@]+@[^@]+\.[^@]+", email):
        raise ValueError("Invalid email format")
    return email.lower()  # Normalize email
```

### 4.4 Naming Inconsistencies

- Snake case (`vagrant_vm`) vs. camel case (`FileUpload`) in class names
- Field naming inconsistencies (`created_on` vs `created_at`)
- Table naming inconsistencies (plural vs. singular)

### 4.5 Relationship Issues

- **Missing Relationships**: 
  - FileUpload has vm_id FK but no relationship to Vagrant_vm
  - Potential missing back-references in other models not examined

- **Inconsistent Cascade Behavior**:
  - All relationships from User specify cascade="all, delete-orphan"
  - No cascade behavior specified on the many-to-one side
  - No explicit lazy loading strategy specified (lazy='select' is default)

- **Relationship Loading Strategy**:
  - No use of joinedload or selectinload for optimization
  - Potential N+1 query problems with default loading strategies

- **Circular Dependencies**:
  - Potential circular imports due to relationship definitions

### 4.6 Schema Definition Issues

- Missing indexes on frequently queried fields
- Missing constraints on data fields
- Status field as string instead of enum for `Vagrant_vm`
- Hard-coded default values instead of configuration-based defaults

## 5. Missing Features

### 5.1 Pydantic Integration

The codebase lacks integration with Pydantic, which would provide significant benefits:

#### 5.1.1 API Validation Models

No Pydantic models exist for API request/response validation. Examples of missing models:

```python
# Example of Pydantic models that should be implemented
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str

class UserCreate(UserBase):
    password: str

    @validator('password')
    def password_complexity(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        # More validations...
        return v

class UserResponse(UserBase):
    id: UUID
    is_active: bool
    created_on: datetime
```

#### 5.1.2 Schema Definitions

No consistent schema definitions for API documentation, leaving potential gaps:

```python
# Example schema for model documentation
model_config = {
    "json_schema_extra": {
        "example": {
            "username": "johndoe",
            "email": "<EMAIL>",
            "full_name": "John Doe"
        }
    }
}
```

#### 5.1.3 Serialization/Deserialization

No consistent serialization/deserialization mechanisms, which should include:

- Input validation for API requests
- Output serialization for API responses
- Data transformation between API and database models
- Custom serialization for specific types (e.g., dates, UUIDs)

### 5.2 Audit/Tracking Features

The audit system appears incomplete across models:

#### 5.2.1 Inconsistent Audit Implementation

- BaseModel has created_on/modified_on, but not implemented in all models
- No standardized approach for capturing user actions
- No clear separation between system and user-generated changes

#### 5.2.2 Missing History Tracking

Missing features for robust history tracking:

```python
# Example of history tracking mixin that should be implemented
class HistoryMixin:
    """Mixin for tracking history of changes to a model."""
    
    @classmethod
    def __declare_last__(cls):
        """Hook called after mapper setup."""
        event.listen(cls, 'after_update', cls.track_history)
    
    @staticmethod
    def track_history(mapper, connection, target):
        """Track changes after an update."""
        state = inspect(target)
        changes = {}
        
        for attr in state.attrs:
            hist = state.get_history(attr.key, True)
            if hist.has_changes():
                changes[attr.key] = {
                    'old': hist.deleted[0] if hist.deleted else None,
                    'new': hist.added[0] if hist.added else None
                }
        
        if changes:
            # Log changes to history table
            history_table = Table('model_history', Base.metadata,
                Column('id', UUID, primary_key=True, default=uuid.uuid4),
                Column('model', String, nullable=False),
                Column('record_id', UUID, nullable=False),
                Column('changes', JSON, nullable=False),
                Column('changed_by_id', UUID, ForeignKey('users.id')),
                Column('changed_at', DateTime, default=datetime.utcnow)
            )
            
            connection.execute(
                history_table.insert().values(
                    model=target.__class__.__name__,
                    record_id=target.id,
                    changes=changes,
                    changed_by_id=current_user.id if hasattr(current_user, 'id') else None
                )
            )
```

#### 5.2.3 No User Action Tracking

Missing functionality for tracking user actions:

- No audit logging for sensitive operations
- No tracking of read operations for sensitive data
- No tracking of administrative actions
- No user session activity monitoring

### 5.3 Security Features

#### 5.3.1 Missing Encryption

The codebase lacks encryption for sensitive data:

```python
# Example of encryption handling that should be implemented
from cryptography.fernet import Fernet

class EncryptedColumn(TypeDecorator):
    """Column type for encrypted data."""
    impl = String
    
    def __init__(self, *args, **kwargs):
        self.key = os.environ.get('ENCRYPTION_KEY')
        self.fernet = Fernet(self.key.encode())
        super().__init__(*args, **kwargs)
    
    def process_bind_param(self, value, dialect):
        if value is not None:
            return self.fernet.encrypt(value.encode()).decode()
        return None
    
    def process_result_value(self, value, dialect):
        if value is not None:
            return self.fernet.decrypt(value.encode()).decode()
        return None
```

#### 5.3.2 Authentication Model Improvements

Missing features for robust authentication:

- No Multi-Factor Authentication (MFA) support
- No password reset mechanism
- No account lockout functionality
- No login attempt tracking
- No session expiration logic

#### 5.3.3 Data Protection

Missing data protection features:

- No data masking for sensitive fields
- No rate limiting on sensitive operations
- No input sanitization mechanisms
- No protection against SQL injection (beyond ORM)

### 5.4 Soft Delete Support

#### 5.4.1 Inconsistent Implementation

The soft delete pattern is only partially implemented:

- BaseModel has soft_delete() method and deleted_on field
- No automatic filtering of soft-deleted records
- Inconsistent use across models (not all models inherit from BaseModel)

#### 5.4.2 Missing Global Filter

Missing global filtering for soft deleted records:

```python
# Example of global filter that should be implemented
class SoftDeleteMixin:
    """Mixin providing soft delete functionality."""
    deleted_on = Column(DateTime, nullable=True)
    
    def soft_delete(self) -> None:
        """Mark record as deleted."""
        self.deleted_on = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_on = None
    
    @declared_attr
    def __mapper_args__(cls):
        """Add query filtering to exclude soft-deleted records by default."""
        return {
            'polymorphic_identity': cls.__name__,
            'with_polymorphic': '*'
        }

# Apply global filter
@event.listens_for(Query, "before_compile", retval=True)
def filter_soft_deleted(query):
    """Automatically filter out soft-deleted records."""
    for desc in query.column_descriptions:
        entity = desc['entity']
        if hasattr(entity, 'deleted_on'):
            query = query.filter(entity.deleted_on.is_(None))
    return query
```

#### 5.4.3 Missing Restore Functionality

No consistent implementation for restoring soft-deleted records:

- BaseModel has restore() method but no tracking of who restored
- No history of soft deletions and restorations
- No permissions checking for restoration

### 5.5 Data Integrity Features

#### 5.5.1 Missing Data Integrity Constraints

The codebase lacks comprehensive data integrity constraints:

```python
# Example constraints that should be implemented
__table_args__ = (
    # Primary key constraint
    PrimaryKeyConstraint('id', name='pk_users'),
    
    # Unique constraints
    UniqueConstraint('username', name='uq_users_username'),
    UniqueConstraint('email', name='uq_users_email'),
    
    # Check constraints
    CheckConstraint('LENGTH(username) BETWEEN 3 AND 50', name='ck_username_length'),
    CheckConstraint('LENGTH(password_hash) > 0', name='ck_password_not_empty'),
    
    # Example of index definition
    Index('ix_users_username_email', 'username', 'email'),
    
    # Schema definition (optional)
    {'schema': 'auth'}
)
```

#### 5.5.2 Missing Composite Keys/Indexes

No use of composite keys or indexes for performance optimization:

```python
# Example composite index that could be implemented
Index('ix_file_uploads_owner_date', 'owner_id', 'created_on')
```

#### 5.5.3 Missing Referential Actions

No explicit referential actions on foreign keys:

```python
# Example with explicit referential actions
owner_id = Column(
    UUID(as_uuid=True), 
    ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"),
    nullable=False,
    index=True
)
```

## 6. Conclusion and Recommendations

Based on the analysis above, the database models require significant improvements in several areas:

1. **Standardize model inheritance**: Ensure all models inherit from BaseModel
2. **Add proper typing**: Implement comprehensive type hints
3. **Implement validation**: Add field validation at all levels
4. **Fix naming inconsistencies**: Adopt a consistent naming convention
5. **Complete relationship definitions**: Ensure all FKs have proper relationships
6. **Add missing features**: Implement Pydantic integration, audit trails, etc.

Detailed recommendations are provided in each section of this analysis. 