# API and Frontend Alignment Best Practices

This document outlines best practices to ensure consistent alignment between backend API endpoints and frontend React routing to avoid costly mismatches.

## Current Issues

We encountered misalignment between backend and frontend routing:

1. Backend used `/files` while frontend expected `/file_upload`
2. URL patterns in response payloads didn't match actual API routes
3. Alias endpoints were inconsistently registered

## Best Practices for API-Frontend Alignment

### 1. Centralized API Configuration

#### Backend (FastAPI)

```python
# api/core/endpoints.py

class APIEndpoints:
    """Central source of truth for all API endpoints"""
    
    # API version prefix
    PREFIX = "/api/v1"
    
    # File upload endpoints
    FILE_UPLOAD = {
        "BASE": f"{PREFIX}/file_upload",
        "DOWNLOAD": lambda file_id: f"{PREFIX}/file_upload/download/{file_id}",
        "FOLDER": f"{PREFIX}/file_upload/folder",
    }
    
    # Authentication endpoints
    AUTH = {
        "LOGIN": f"{PREFIX}/auth/login",
        "REFRESH": f"{PREFIX}/auth/refresh",
        "VERIFY": f"{PREFIX}/auth/verify",
    }
    
    # Additional endpoints...
```

#### Frontend (React/TypeScript)

```typescript
// src/utils/apiConfig.ts

/**
 * Centralized API endpoint configuration
 * Must be kept in sync with backend's api/core/endpoints.py
 */
export const API_ENDPOINTS = {
  // File upload endpoints
  FILE_UPLOAD: {
    BASE: '/api/v1/file_upload',
    DOWNLOAD: (fileId: string) => `/api/v1/file_upload/download/${fileId}`,
    FOLDER: '/api/v1/file_upload/folder',
  },
  
  // Authentication endpoints
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    REFRESH: '/api/v1/auth/refresh',
    VERIFY: '/api/v1/auth/verify',
  },
  
  // Additional endpoints...
};
```

### 2. API Version Control

1. **Use Explicit Versioning**: Always include version in paths (`/api/v1/...`)
2. **Maintain Backward Compatibility**: Avoid changing existing endpoint paths
3. **Deprecate Instead of Removing**: Mark old endpoints as deprecated before removal

### 3. Route Registration Best Practices

#### Backend (FastAPI)

1. **Consistent Router Registration**:

```python
# When registering routers in application.py:
from api.core.endpoints import APIEndpoints

# Remove the prefix from the path when the router already defines it
api_v1_router.include_router(
    file_upload.router,  # router defined with prefix="/file_upload"
    tags=["files"]
)

# Include the prefix when the router doesn't define it
api_v1_router.include_router(
    users.router, 
    prefix=APIEndpoints.USERS["BASE"].replace(APIEndpoints.PREFIX, ""),
    tags=["users"]
)
```

2. **URL Generation in Services**:

```python
# Always use the centralized endpoint definitions when creating URLs
from api.core.endpoints import APIEndpoints

# In file_upload_service.py
return {
    "id": file_id,
    "filename": file_name,
    # Use centralized definition for download URL
    "download_url": APIEndpoints.FILE_UPLOAD["DOWNLOAD"](file_id)
}
```

### 4. Automated Testing for API-Frontend Alignment

1. **API Schema Validation Tests**:

```python
# tests/api/test_endpoint_alignment.py

def test_api_endpoints_match_openapi_schema():
    """Test that the centralized API endpoints match the OpenAPI schema"""
    from api.core.endpoints import APIEndpoints
    from api.application import get_application
    
    app = get_application()
    openapi_schema = app.openapi()
    
    # Check that each endpoint in APIEndpoints exists in the OpenAPI schema
    for endpoint_group in [APIEndpoints.FILE_UPLOAD, APIEndpoints.AUTH]:
        for endpoint_key, endpoint_path in endpoint_group.items():
            if callable(endpoint_path):
                # Skip endpoint generators (lambdas)
                continue
            
            # Remove API prefix for comparison
            path = endpoint_path.replace(APIEndpoints.PREFIX, "")
            assert path in openapi_schema["paths"], f"Endpoint {path} not found in OpenAPI schema"
```

2. **Frontend-Backend Alignment Test**:

```typescript
// tests/frontend/apiEndpoints.test.ts

import { API_ENDPOINTS } from '../src/utils/apiConfig';
import * as backendEndpoints from '../api-endpoints.json'; // Generate this from backend

describe('API Endpoints Alignment', () => {
  test('Frontend endpoints match backend endpoints', () => {
    // Test file upload endpoints
    expect(API_ENDPOINTS.FILE_UPLOAD.BASE).toEqual(backendEndpoints.FILE_UPLOAD.BASE);
    expect(API_ENDPOINTS.FILE_UPLOAD.FOLDER).toEqual(backendEndpoints.FILE_UPLOAD.FOLDER);
    
    // Test auth endpoints
    expect(API_ENDPOINTS.AUTH.LOGIN).toEqual(backendEndpoints.AUTH.LOGIN);
    expect(API_ENDPOINTS.AUTH.REFRESH).toEqual(backendEndpoints.AUTH.REFRESH);
    // Additional endpoint checks...
  });
});
```

### 5. API Documentation and OpenAPI Integration

1. **Generate OpenAPI Schema**:
   - FastAPI automatically generates OpenAPI schema
   - Use this as a reference for frontend development

2. **Export API Endpoints as JSON**:
   ```python
   # tools/generate_api_endpoints.py
   import json
   from api.core.endpoints import APIEndpoints
   
   # Convert callable endpoints to string representation
   def serialize_endpoints(endpoints_dict):
       result = {}
       for key, value in endpoints_dict.items():
           if callable(value):
               # Create a template string for callable endpoints
               result[key] = f"TEMPLATE:{value('{{id}}')}"
           else:
               result[key] = value
       return result
   
   # Export all endpoint groups
   endpoints_json = {
       "FILE_UPLOAD": serialize_endpoints(APIEndpoints.FILE_UPLOAD),
       "AUTH": serialize_endpoints(APIEndpoints.AUTH),
       # Additional endpoint groups...
   }
   
   # Write to file that can be imported by frontend
   with open("api-endpoints.json", "w") as f:
       json.dump(endpoints_json, f, indent=2)
   ```

3. **Frontend API Client Generator**:
   - Generate TypeScript API client from OpenAPI spec
   - Tools like [openapi-typescript-codegen](https://github.com/ferdikoomen/openapi-typescript-codegen)

### 6. Development Workflow

1. **API-First Development**:
   - Define API contract first
   - Document in central endpoint file
   - Implement backend endpoints
   - Implement frontend components

2. **Pre-commit Hooks**:
   - Run alignment tests before commits
   - Validate OpenAPI schema
   - Check for endpoint mismatches

3. **Continuous Integration**:
   - Verify API endpoint alignment in CI pipeline
   - Fail builds when misalignments are detected

## Implementation Plan

1. **Create Central Endpoint Definition**:
   - Implement `api/core/endpoints.py` on backend
   - Create corresponding `src/utils/apiConfig.ts` on frontend

2. **Update Existing Code**:
   - Refactor backend services to use centralized URLs
   - Update frontend API calls to use centralized config

3. **Add Automated Testing**:
   - Implement alignment test suite
   - Add pre-commit hooks

4. **Documentation**:
   - Document API contract
   - Create guides for adding new endpoints

## Conclusion

By implementing these practices, we can avoid API/frontend misalignments, reduce errors, and simplify the development of new features. A centralized source of truth for API endpoints is the key to maintaining consistent alignment between backend and frontend. 