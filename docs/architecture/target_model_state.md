# Target Database Model State (T12)

This document defines the target state for database models in the TurdParty application, addressing all issues identified in the model analysis (T11).

## 1. Model Architecture

### 1.1 Base Classes and Mixins

The following base classes and mixins will provide consistent functionality across all models:

#### 1.1.1 Base Model

```python
from datetime import datetime
from typing import Optional, ClassVar, Dict, Any, Type, List, Union
from sqlalchemy import Column, DateTime, Boolean, String, Text, Integer, ForeignKey, Table, event
from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint, CheckConstraint, Index
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship, validates, Query
from sqlalchemy.sql import func
import uuid
import re

Base = declarative_base()

class BaseModel(Base):
    """Base model for all database models with common fields and functionality."""
    __abstract__: ClassVar[bool] = True
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_on = Column(DateTime, nullable=False, default=datetime.utcnow, server_default=func.now())
    modified_on = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, server_default=func.now())
    deleted_on = Column(DateTime, nullable=True)
    
    def soft_delete(self) -> None:
        """Mark the record as soft-deleted."""
        self.deleted_on = datetime.utcnow()
    
    @hybrid_property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_on is not None
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_on = None
    
    @classmethod
    def default_query(cls, query: Optional[Query] = None) -> Query:
        """Return a query that filters out soft-deleted records."""
        q = query or cls.query
        return q.filter(cls.deleted_on.is_(None))
```

#### 1.1.2 Validation Mixin

```python
class ValidationMixin:
    """Mixin for field validation."""
    
    @validates('email')
    def validate_email(self, key: str, value: str) -> str:
        """Validate and normalize email addresses."""
        if not value:
            raise ValueError("Email cannot be empty")
        
        if not re.match(r"[^@]+@[^@]+\.[^@]+", value):
            raise ValueError("Invalid email format")
        
        return value.lower().strip()
    
    @validates('username')
    def validate_username(self, key: str, value: str) -> str:
        """Validate username."""
        if not value:
            raise ValueError("Username cannot be empty")
        
        if len(value) < 3:
            raise ValueError("Username must be at least 3 characters")
        
        if not re.match(r"^[a-zA-Z0-9_]+$", value):
            raise ValueError("Username can only contain letters, numbers, and underscores")
        
        return value
```

#### 1.1.3 History Tracking Mixin

```python
class HistoryMixin:
    """Mixin for tracking history of changes to a model."""
    
    @classmethod
    def __declare_last__(cls) -> None:
        """Hook called after mapper setup."""
        event.listen(cls, 'after_update', cls._track_history)
    
    @staticmethod
    def _track_history(mapper: Any, connection: Any, target: Any) -> None:
        """Track changes after an update."""
        from sqlalchemy.orm import object_mapper, object_session
        from sqlalchemy.orm.attributes import instance_state
        
        state = instance_state(target)
        changes = {}
        
        for attr in state.attrs:
            hist = attr.load_history()
            if hist.has_changes():
                changes[attr.key] = {
                    'old': hist.deleted[0] if hist.deleted else None,
                    'new': hist.added[0] if hist.added else None
                }
        
        if changes:
            # Insert into history table
            history_table = Table('model_history', target.metadata,
                Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
                Column('model', String, nullable=False),
                Column('record_id', UUID(as_uuid=True), nullable=False),
                Column('changes', JSON, nullable=False),
                Column('changed_by_id', UUID(as_uuid=True), ForeignKey('users.id'), nullable=True),
                Column('changed_at', DateTime, default=datetime.utcnow),
                extend_existing=True
            )
            
            session = object_session(target)
            current_user_id = None
            
            # Try to get current user ID if available
            if hasattr(session, 'info') and 'user_id' in session.info:
                current_user_id = session.info['user_id']
            
            connection.execute(
                history_table.insert().values(
                    model=target.__class__.__name__,
                    record_id=target.id,
                    changes=changes,
                    changed_by_id=current_user_id,
                    changed_at=datetime.utcnow()
                )
            )
```

#### 1.1.4 Type Definitions (in a separate types.py file)

```python
from enum import Enum
from typing import List, Optional, Dict, Any, Union, TypeVar, Generic
from pydantic import BaseModel, EmailStr, Field, validator
from datetime import datetime
from uuid import UUID

T = TypeVar('T')

class VMStatus(str, Enum):
    """Enum for VM status values."""
    CREATED = "created"
    STARTING = "starting"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"

class ItemStatus(str, Enum):
    """Enum for Item status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"
```

### 1.2 Global Filters for Soft Delete

```python
@event.listens_for(Query, "before_compile", retval=True)
def filter_soft_deleted(query: Query) -> Query:
    """
    Automatically filter out soft-deleted records unless explicitly included.
    
    To include soft-deleted records, use:
    query = query.execution_options(include_deleted=True)
    """
    if query._execution_options.get('include_deleted', False):
        return query
    
    for ent in query.column_descriptions:
        entity = ent['entity']
        if hasattr(entity, 'deleted_on'):
            query = query.filter(entity.deleted_on.is_(None))
    
    return query
```

## 2. Improved Model Definitions

### 2.1 User Model

```python
class User(BaseModel, ValidationMixin, HistoryMixin):
    """User model for storing user information."""
    __tablename__ = "users"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_users'),
        UniqueConstraint('username', name='uq_users_username'),
        UniqueConstraint('email', name='uq_users_email'),
        CheckConstraint('LENGTH(username) BETWEEN 3 AND 50', name='ck_username_length'),
        CheckConstraint('LENGTH(password_hash) > 10', name='ck_password_hash_length'),
        Index('ix_users_username_email', 'username', 'email'),
        {'extend_existing': True}
    )
    
    # User identification and authentication
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    password_hash = Column(String(255), nullable=False)
    
    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # MFA support
    mfa_enabled = Column(Boolean, default=False, nullable=False)
    mfa_secret = Column(String(32), nullable=True)
    
    # Account security
    last_login = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    
    # Relationships
    sessions = relationship("Session", back_populates="user", cascade="all, delete-orphan", lazy="selectin")
    vagrant_vms = relationship("VagrantVM", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    vm_injections = relationship("VMInjection", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    file_uploads = relationship("FileUpload", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    file_selections = relationship("FileSelection", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    items = relationship("Item", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    
    # Password validation and management
    @validates('password_hash')
    def validate_password_hash(self, key: str, value: str) -> str:
        """Validate password hash."""
        if not value or len(value) < 10:
            raise ValueError("Invalid password hash")
        return value
    
    def __repr__(self) -> str:
        """Return string representation of the user."""
        return f"<User(id={self.id}, username={self.username}, email={self.email}, is_active={self.is_active})>"
```

### 2.2 Item Model

```python
class Item(BaseModel, ValidationMixin, HistoryMixin):
    """Item model for storing item data."""
    __tablename__ = "items"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_items'),
        CheckConstraint('LENGTH(title) BETWEEN 3 AND 255', name='ck_title_length'),
        Index('ix_items_owner_id', 'owner_id'),
        {'extend_existing': True}
    )
    
    # Item properties
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    status = Column(String(20), default=ItemStatus.ACTIVE.value, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Ownership
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, index=True)
    
    # Relationships
    owner = relationship("User", back_populates="items", lazy="joined")
    
    # Validation
    @validates('title')
    def validate_title(self, key: str, value: str) -> str:
        """Validate item title."""
        if not value:
            raise ValueError("Title cannot be empty")
        
        if len(value) < 3:
            raise ValueError("Title must be at least 3 characters")
        
        return value
    
    @validates('status')
    def validate_status(self, key: str, value: str) -> str:
        """Validate item status."""
        try:
            return ItemStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in ItemStatus]
            raise ValueError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
    
    def __repr__(self) -> str:
        """Return string representation of the item."""
        return f"<Item(id={self.id}, title={self.title}, status={self.status})>"
```

### 2.3 VagrantVM Model

```python
class VagrantVM(BaseModel, ValidationMixin, HistoryMixin):
    """Model for Vagrant VM instances."""
    __tablename__ = "vagrant_vms"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_vagrant_vms'),
        CheckConstraint('LENGTH(name) BETWEEN 3 AND 100', name='ck_vm_name_length'),
        CheckConstraint('cpus BETWEEN 1 AND 16', name='ck_cpus_range'),
        CheckConstraint('memory_mb BETWEEN 512 AND 32768', name='ck_memory_range'),
        CheckConstraint('disk_gb BETWEEN 1 AND 500', name='ck_disk_range'),
        Index('ix_vagrant_vms_owner_id', 'owner_id'),
        Index('ix_vagrant_vms_status', 'status'),
        {'extend_existing': True}
    )
    
    # VM identification
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    template = Column(String(100), nullable=False)
    
    # VM resources
    memory_mb = Column(Integer, nullable=False, default=1024)
    cpus = Column(Integer, nullable=False, default=1)
    disk_gb = Column(Integer, nullable=False, default=20)
    
    # VM state
    status = Column(String(20), nullable=False, default=VMStatus.CREATED.value, index=True)
    ip_address = Column(String(50), nullable=True)
    ssh_port = Column(Integer, nullable=True)
    vagrant_id = Column(String(100), nullable=True)
    
    # VM operations
    last_action = Column(String(50), nullable=True)
    last_action_time = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    domain = Column(String(100), nullable=False, default="TurdParty")
    
    # Ownership
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, index=True)
    
    # Relationships
    owner = relationship("User", back_populates="vagrant_vms", lazy="joined")
    vm_injections = relationship("VMInjection", back_populates="vagrant_vm", cascade="all, delete-orphan", lazy="selectin")
    file_uploads = relationship("FileUpload", back_populates="vagrant_vm", cascade="all, delete-orphan", lazy="selectin")
    
    # Validation
    @validates('name')
    def validate_name(self, key: str, value: str) -> str:
        """Validate VM name."""
        if not value:
            raise ValueError("VM name cannot be empty")
        
        if len(value) < 3:
            raise ValueError("VM name must be at least 3 characters")
        
        if not re.match(r"^[a-zA-Z0-9_-]+$", value):
            raise ValueError("VM name can only contain letters, numbers, underscores, and hyphens")
        
        return value
    
    @validates('status')
    def validate_status(self, key: str, value: str) -> str:
        """Validate VM status."""
        try:
            return VMStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in VMStatus]
            raise ValueError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
    
    @validates('cpus')
    def validate_cpus(self, key: str, value: int) -> int:
        """Validate CPU count."""
        if value < 1:
            raise ValueError("CPU count must be at least 1")
        if value > 16:
            raise ValueError("CPU count cannot exceed 16")
        return value
    
    @validates('memory_mb')
    def validate_memory(self, key: str, value: int) -> int:
        """Validate memory allocation."""
        if value < 512:
            raise ValueError("Memory must be at least 512 MB")
        if value > 32768:
            raise ValueError("Memory cannot exceed 32 GB (32768 MB)")
        return value
    
    def __repr__(self) -> str:
        """Return string representation of the VM."""
        return f"<VagrantVM(id={self.id}, name={self.name}, status={self.status})>"
```

### 2.4 FileUpload Model

```python
class FileUpload(BaseModel, ValidationMixin, HistoryMixin):
    """Model for uploaded files."""
    __tablename__ = "file_uploads"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_file_uploads'),
        CheckConstraint('LENGTH(filename) BETWEEN 1 AND 255', name='ck_filename_length'),
        CheckConstraint('file_size >= 0', name='ck_file_size_positive'),
        Index('ix_file_uploads_owner_id', 'owner_id'),
        Index('ix_file_uploads_vm_id', 'vm_id'),
        Index('ix_file_uploads_folder_id', 'folder_id'),
        {'extend_existing': True}
    )
    
    # File metadata
    filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    file_hash = Column(String(64), nullable=False)
    
    # File storage
    file_path = Column(String(500), nullable=False)
    file_folder_path = Column(String(500), nullable=True)
    folder_id = Column(String(100), nullable=True, index=True)
    description = Column(Text, nullable=True)
    download_url = Column(String(500), nullable=False)
    
    # File status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Ownership and relationships
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, index=True)
    vm_id = Column(UUID(as_uuid=True), ForeignKey("vagrant_vms.id", ondelete="SET NULL", onupdate="CASCADE"), nullable=True, index=True)
    
    # Relationships
    owner = relationship("User", back_populates="file_uploads", lazy="joined")
    vagrant_vm = relationship("VagrantVM", back_populates="file_uploads", lazy="joined")
    selections = relationship("FileSelection", back_populates="file_upload", cascade="all, delete-orphan", lazy="selectin")
    
    # Validation
    @validates('filename')
    def validate_filename(self, key: str, value: str) -> str:
        """Validate filename."""
        if not value:
            raise ValueError("Filename cannot be empty")
        
        # Remove potentially dangerous characters
        value = re.sub(r'[^\w\-\. ]', '_', value)
        return value
    
    @validates('content_type')
    def validate_content_type(self, key: str, value: str) -> str:
        """Validate content type."""
        if not value:
            raise ValueError("Content type cannot be empty")
        
        # Add additional validation for allowed content types if needed
        return value
    
    @validates('file_hash')
    def validate_file_hash(self, key: str, value: str) -> str:
        """Validate file hash."""
        if not value:
            raise ValueError("File hash cannot be empty")
        
        if not re.match(r"^[a-fA-F0-9]{32,128}$", value):
            raise ValueError("Invalid hash format")
        
        return value
    
    def __repr__(self) -> str:
        """Return string representation of the file upload."""
        return f"<FileUpload(id={self.id}, filename={self.filename}, size={self.file_size})>"
```

## 3. Pydantic Models for API Validation

### 3.1 Base Pydantic Models

```python
import re
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, EmailStr, Field, validator, ConfigDict

class PydanticConfig:
    """Configuration for all Pydantic models."""
    model_config = ConfigDict(
        populate_by_name=True,
        extra="ignore",
        json_encoders={
            datetime: lambda dt: dt.isoformat(),
            UUID: lambda uid: str(uid),
        }
    )

class ResponseBase(BaseModel, PydanticConfig):
    """Base model for all API responses."""
    id: UUID
    created_on: datetime
    modified_on: datetime

class PaginatedResponse(BaseModel, PydanticConfig):
    """Generic paginated response."""
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int
```

### 3.2 User API Models

```python
class UserBase(BaseModel, PydanticConfig):
    """Base model for user data."""
    username: str = Field(..., min_length=3, max_length=50, pattern="^[a-zA-Z0-9_]+$")
    email: EmailStr
    full_name: str = Field(..., min_length=1, max_length=100)
    is_active: bool = True
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "username": "johndoe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "is_active": True
            }
        }
    )

class UserCreate(UserBase):
    """Model for creating a new user."""
    password: str = Field(..., min_length=8, max_length=100)
    
    @validator("password")
    def password_complexity(cls, value: str) -> str:
        """Validate password complexity."""
        if len(value) < 8:
            raise ValueError("Password must be at least 8 characters")
        
        if not re.search(r"[A-Z]", value):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not re.search(r"[a-z]", value):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not re.search(r"[0-9]", value):
            raise ValueError("Password must contain at least one digit")
        
        if not re.search(r"[^a-zA-Z0-9]", value):
            raise ValueError("Password must contain at least one special character")
        
        return value

class UserUpdate(BaseModel, PydanticConfig):
    """Model for updating user data."""
    full_name: Optional[str] = Field(None, min_length=1, max_length=100)
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None

class UserResponse(UserBase, ResponseBase):
    """Model for user response."""
    is_superuser: bool
    last_login: Optional[datetime] = None
    mfa_enabled: bool
```

### 3.3 Item API Models

```python
class ItemBase(BaseModel, PydanticConfig):
    """Base model for item data."""
    title: str = Field(..., min_length=3, max_length=255)
    description: Optional[str] = None
    status: str = Field(default="active", pattern="^(active|inactive|archived)$")
    is_active: bool = True
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "title": "Sample Item",
                "description": "This is a sample item description",
                "status": "active",
                "is_active": True
            }
        }
    )

class ItemCreate(ItemBase):
    """Model for creating a new item."""
    pass

class ItemUpdate(BaseModel, PydanticConfig):
    """Model for updating item data."""
    title: Optional[str] = Field(None, min_length=3, max_length=255)
    description: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(active|inactive|archived)$")
    is_active: Optional[bool] = None

class ItemResponse(ItemBase, ResponseBase):
    """Model for item response."""
    owner_id: UUID
```

### 3.4 VagrantVM API Models

```python
class VagrantVMBase(BaseModel, PydanticConfig):
    """Base model for VM data."""
    name: str = Field(..., min_length=3, max_length=100, pattern="^[a-zA-Z0-9_-]+$")
    description: Optional[str] = None
    template: str = Field(..., min_length=1, max_length=100)
    memory_mb: int = Field(default=1024, ge=512, le=32768)
    cpus: int = Field(default=1, ge=1, le=16)
    disk_gb: int = Field(default=20, ge=1, le=500)
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "dev-vm-1",
                "description": "Development VM",
                "template": "ubuntu-20.04",
                "memory_mb": 2048,
                "cpus": 2,
                "disk_gb": 40
            }
        }
    )

class VagrantVMCreate(VagrantVMBase):
    """Model for creating a new VM."""
    pass

class VagrantVMUpdate(BaseModel, PydanticConfig):
    """Model for updating VM data."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, pattern="^[a-zA-Z0-9_-]+$")
    description: Optional[str] = None
    memory_mb: Optional[int] = Field(None, ge=512, le=32768)
    cpus: Optional[int] = Field(None, ge=1, le=16)

class VagrantVMStatusUpdate(BaseModel, PydanticConfig):
    """Model for updating VM status."""
    status: str = Field(..., pattern="^(created|starting|running|stopped|error)$")
    error_message: Optional[str] = None

class VagrantVMResponse(VagrantVMBase, ResponseBase):
    """Model for VM response."""
    status: str
    ip_address: Optional[str] = None
    ssh_port: Optional[int] = None
    last_action: Optional[str] = None
    last_action_time: Optional[datetime] = None
    error_message: Optional[str] = None
    owner_id: UUID
```

### 3.5 FileUpload API Models

```python
class FileUploadBase(BaseModel, PydanticConfig):
    """Base model for file upload data."""
    filename: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "filename": "document.pdf",
                "description": "Important document"
            }
        }
    )

class FileUploadCreate(FileUploadBase):
    """Model for creating a new file upload."""
    folder_id: Optional[str] = None
    vm_id: Optional[UUID] = None

class FileUploadUpdate(BaseModel, PydanticConfig):
    """Model for updating file upload data."""
    filename: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    folder_id: Optional[str] = None
    vm_id: Optional[UUID] = None

class FileUploadResponse(FileUploadBase, ResponseBase):
    """Model for file upload response."""
    file_size: int
    content_type: str
    file_hash: str
    download_url: str
    is_active: bool
    owner_id: UUID
    vm_id: Optional[UUID] = None
    folder_id: Optional[str] = None
```

## 4. Data Access Layer

### 4.1 Repository Pattern

To ensure consistent data access and encapsulate query logic, we'll implement the repository pattern:

```python
from typing import Generic, TypeVar, Type, List, Optional, Union, Dict, Any
from sqlalchemy.orm import Session
from uuid import UUID
from pydantic import BaseModel

T = TypeVar('T')
CreateSchema = TypeVar('CreateSchema', bound=BaseModel)
UpdateSchema = TypeVar('UpdateSchema', bound=BaseModel)

class Repository(Generic[T, CreateSchema, UpdateSchema]):
    """Generic repository for database operations."""
    
    def __init__(self, model: Type[T], db: Session):
        self.model = model
        self.db = db
    
    def get(self, id: UUID) -> Optional[T]:
        """Get a record by ID."""
        return self.db.query(self.model).filter(self.model.id == id).first()
    
    def list(self, skip: int = 0, limit: int = 100, **filters) -> List[T]:
        """List records with optional filtering."""
        query = self.db.query(self.model)
        
        # Apply filters
        for field, value in filters.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)
        
        return query.offset(skip).limit(limit).all()
    
    def create(self, schema: CreateSchema, **extra_data) -> T:
        """Create a new record."""
        data = {**schema.model_dump(exclude_unset=True), **extra_data}
        db_item = self.model(**data)
        self.db.add(db_item)
        self.db.commit()
        self.db.refresh(db_item)
        return db_item
    
    def update(self, id: UUID, schema: UpdateSchema) -> Optional[T]:
        """Update a record."""
        db_item = self.get(id)
        if not db_item:
            return None
        
        update_data = schema.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_item, field, value)
        
        self.db.commit()
        self.db.refresh(db_item)
        return db_item
    
    def delete(self, id: UUID, soft: bool = True) -> bool:
        """Delete a record (soft delete by default)."""
        db_item = self.get(id)
        if not db_item:
            return False
        
        if soft and hasattr(db_item, 'soft_delete'):
            db_item.soft_delete()
            self.db.commit()
        else:
            self.db.delete(db_item)
            self.db.commit()
        
        return True
    
    def count(self, **filters) -> int:
        """Count records with optional filtering."""
        query = self.db.query(self.model)
        
        # Apply filters
        for field, value in filters.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)
        
        return query.count()
```

### 4.2 Database Session Management

```python
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from fastapi import Depends
from api.core.config import settings

# Synchronous engine and session
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Asynchronous engine and session (for FastAPI)
async_engine = create_async_engine(
    settings.ASYNC_DATABASE_URL,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600
)
AsyncSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=async_engine
)

@contextmanager
def get_db() -> Generator:
    """Get database session - synchronous version."""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()

async def get_async_db() -> AsyncGenerator:
    """Get database session - asynchronous version for FastAPI."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
```

## 5. Migration Strategy

### 5.1 Alembic Configuration

Enhance the Alembic configuration to support:

1. Auto-generation of migrations
2. Data migrations
3. Support for SQL enums
4. Better collision detection

```python
# In env.py
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from api.db.base_model import Base
from api.db.models import *  # Import all models

config = context.config
fileConfig(config.config_file_name)
target_metadata = Base.metadata

def run_migrations_online():
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix='sqlalchemy.',
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            # Enable these options for better migrations
            compare_type=True,          # Compare column types
            compare_server_default=True,  # Compare server defaults
            include_schemas=True,       # Include schema in migrations
            render_as_batch=True,       # Use batch operations
            user_module_prefix='sa.'    # Use sa. as prefix
        )

        with context.begin_transaction():
            context.run_migrations()
```

### 5.2 Migration Script Template

Update the Alembic script.py.mako to support data migrations:

```python
"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# Data migration imports
from uuid import uuid4
from sqlalchemy.orm import Session
from datetime import datetime

# Revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade():
    ${upgrades if upgrades else "pass"}
    
    # Data migration
    # Uncomment this block if you need to perform data migration
    # connection = op.get_bind()
    # session = Session(bind=connection)
    # try:
    #     # Perform data migration here
    #     # Example:
    #     # for row in session.execute("SELECT id FROM table"):
    #     #     session.execute("UPDATE table SET new_column = 'value' WHERE id = :id", {'id': row[0]})
    #     session.commit()
    # except Exception as e:
    #     session.rollback()
    #     raise e
    # finally:
    #     session.close()


def downgrade():
    ${downgrades if downgrades else "pass"}
```

## 6. Unit and Integration Testing

### 6.1 Test Database Setup

```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from api.db.base_model import Base
from api.db.session import get_db

@pytest.fixture
def test_db():
    """Create a test database."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(engine)

@pytest.fixture
def override_get_db(test_db):
    """Override the get_db dependency."""
    def _override_get_db():
        try:
            yield test_db
        finally:
            pass
    
    return _override_get_db
```

### 6.2 Model Tests

Example test for the User model:

```python
import pytest
from uuid import uuid4
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from api.db.models.user import User

class TestUserModel:
    """Tests for the User model."""
    
    def test_create_user(self, test_db):
        """Test creating a new user."""
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            password_hash="hashed_password"
        )
        
        test_db.add(user)
        test_db.commit()
        test_db.refresh(user)
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.full_name == "Test User"
        assert user.is_active is True
        assert user.is_superuser is False
        assert user.created_on is not None
        assert user.modified_on is not None
        assert user.deleted_on is None
    
    def test_user_unique_constraints(self, test_db):
        """Test unique constraints on the User model."""
        user1 = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            password_hash="hashed_password"
        )
        test_db.add(user1)
        test_db.commit()
        
        # Try to create user with same username
        user2 = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Different User",
            password_hash="hashed_password"
        )
        test_db.add(user2)
        with pytest.raises(IntegrityError):
            test_db.commit()
        test_db.rollback()
        
        # Try to create user with same email
        user3 = User(
            username="different",
            email="<EMAIL>",
            full_name="Different User",
            password_hash="hashed_password"
        )
        test_db.add(user3)
        with pytest.raises(IntegrityError):
            test_db.commit()
    
    def test_soft_delete(self, test_db):
        """Test soft delete functionality."""
        user = User(
            username="deletetest",
            email="<EMAIL>",
            full_name="Delete Test",
            password_hash="hashed_password"
        )
        test_db.add(user)
        test_db.commit()
        
        # Soft delete the user
        user.soft_delete()
        test_db.commit()
        
        # Check that the user is marked as deleted
        assert user.deleted_on is not None
        assert user.is_deleted is True
        
        # Check that the user is excluded from default queries
        result = test_db.query(User).filter(User.username == "deletetest").first()
        assert result is None
        
        # Check that the user can be found when explicitly including deleted
        result = test_db.query(User).execution_options(include_deleted=True).filter(User.username == "deletetest").first()
        assert result is not None
        assert result.id == user.id
```

## 7. Conclusion

This document defines the target state for the database models in the TurdParty application, addressing all issues identified in our analysis:

1. **Standardized Base Model**: All models now inherit from a consistent BaseModel with improved functionality
2. **Type Hints**: Comprehensive type annotations added throughout all models and methods
3. **Validation**: Robust validation at multiple levels (SQLAlchemy model, Pydantic, database constraints)
4. **Naming Consistency**: Consistent naming conventions for classes, tables, and fields
5. **Complete Relationships**: All relationships properly defined with appropriate cascade behavior
6. **Added Features**: Pydantic integration, audit trails, enhanced security, and more

The implementation should follow a phased approach:

1. First, implement the base classes and mixins (BaseModel, ValidationMixin, etc.)
2. Next, update each model one by one, starting with User and Item
3. Then, create Alembic migrations to update the database schema
4. Finally, implement the Pydantic models and repository pattern

This approach ensures minimal disruption to existing functionality while progressively improving the codebase. 