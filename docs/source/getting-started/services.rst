Services Overview
==================

TurdParty consists of multiple microservices that work together to provide a comprehensive platform for VM management, file processing, and service monitoring.

Service Architecture
--------------------

The TurdParty platform runs the following services:

Core Services
~~~~~~~~~~~~~

**TurdParty API** - ``http://localhost:3050``
   The main FastAPI application providing REST endpoints for all functionality.
   
   - Health endpoint: ``/api/v1/health``
   - API documentation: ``/api/v1/docs``
   - Status: ✅ Healthy

**Frontend Application** - ``http://localhost:3100``
   React-based web interface served via Nginx.
   
   - Status: ✅ Healthy

**PostgreSQL Database** - ``localhost:3200``
   Primary database for application data.
   
   - Status: ✅ Healthy

**Redis** - ``localhost:3400``
   Message broker and cache for Celery task processing.
   
   - Status: ✅ Healthy

Storage Services
~~~~~~~~~~~~~~~~

**MinIO Object Storage** - ``http://localhost:3300`` (API) / ``http://localhost:3301`` (Console)
   S3-compatible object storage for file uploads and management.

   - API endpoint: ``http://localhost:3300``
   - Web console: ``http://localhost:3301``
   - SSH access: ``localhost:2223`` - ✅ Healthy
   - Status: ✅ Healthy

Background Processing
~~~~~~~~~~~~~~~~~~~~~

**Celery Default Worker**
   Handles general background tasks.
   
   - Queue: ``default``
   - Status: ✅ Healthy

**Celery File Operations Worker**
   Processes file-related tasks including uploads and analysis.

   - Queue: ``file_ops``
   - Status: ✅ Healthy

**Celery VM Operations Worker**
   Manages VM-related tasks including file injection.

   - Queue: ``vm_ops``
   - Status: ✅ Healthy

**Celery Flower Dashboard** - ``http://localhost:3450``
   Web interface for monitoring Celery workers and tasks.

   - Status: ✅ Healthy

Monitoring Services
~~~~~~~~~~~~~~~~~~~

**Cachet Status Dashboard** - ``http://localhost:3501``
   Service status page showing real-time status of all TurdParty services.
   
   - Status: ✅ Healthy and configured
   - Features: Incident management, maintenance scheduling, metrics

**Cachet PostgreSQL Database**
   Dedicated database for Cachet dashboard data.
   
   - Status: ✅ Healthy

Starting All Services
---------------------

To start all TurdParty services including Cachet:

.. code-block:: bash

   # Start main TurdParty services
   docker compose -f .dockerwrapper/docker-compose.yml up -d
   
   # Start Cachet dashboard
   docker compose -f .dockerwrapper/docker-compose.cachet.yml up -d

Or use the comprehensive startup script:

.. code-block:: bash

   # This will start all services and show status
   ./.dockerwrapper/start-turdparty.sh

Service Health Monitoring
-------------------------

All TurdParty services are currently healthy and operational. Check the status of all services:

.. code-block:: bash

   # View all running containers
   docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

   # Check specific service logs
   docker logs turdparty_api
   docker logs turdparty_cachet

Current Status: **12/12 services healthy** ✅

Access Points
-------------

Once all services are running, you can access:

- **Main Application**: http://localhost:3100
- **API Documentation**: http://localhost:3050/api/v1/docs
- **Service Status**: http://localhost:3501
- **MinIO Console**: http://localhost:3301
- **Celery Monitor**: http://localhost:3450

Service Dependencies
-------------------

The services have the following dependency relationships:

.. code-block:: text

   PostgreSQL ← API ← Frontend
   Redis ← Celery Workers ← API
   MinIO ← API
   PostgreSQL (Cachet) ← Cachet Dashboard

Troubleshooting
---------------

If services show as unhealthy:

1. **Check logs**: ``docker logs <container_name>``
2. **Restart service**: ``docker restart <container_name>``
3. **Check network**: Ensure all containers are on the same network
4. **Verify ports**: Make sure no port conflicts exist

For detailed troubleshooting, see the :doc:`../troubleshooting/index` section.
