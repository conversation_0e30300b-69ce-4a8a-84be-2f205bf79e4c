Celery API Endpoints
====================

The TurdParty API provides endpoints for monitoring and managing Celery workers and tasks.

Base URL
--------

All Celery endpoints are available under:

.. code-block:: text

   /api/v1/celery/

Health and Status Endpoints
---------------------------

Check Celery Health
~~~~~~~~~~~~~~~~~~~

**GET** ``/api/v1/celery/health``

Check the overall health of Celery workers and broker.

   **Example Request:**

   .. code-block:: bash

      curl http://localhost:3050/api/v1/celery/health

   **Example Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "broker": "connected",
        "workers": {
          "active": 3,
          "total": 3
        },
        "queues": [
          "default",
          "file_ops",
          "vm_ops"
        ]
      }

   **Response Codes:**
   
   - ``200``: Celery is healthy
   - ``503``: Celery is unhealthy

Get Worker Statistics
~~~~~~~~~~~~~~~~~~~~~

**GET** ``/api/v1/celery/stats``

Get detailed statistics about Celery workers.

   **Example Request:**

   .. code-block:: bash

      curl http://localhost:3050/api/v1/celery/stats

   **Example Response:**

   .. code-block:: json

      {
        "workers": {
          "celery@worker1": {
            "status": "online",
            "active": 2,
            "processed": 150,
            "load": [0.1, 0.2, 0.3],
            "queues": ["default", "file_ops"]
          }
        },
        "broker": {
          "transport": "redis",
          "hostname": "redis:6379"
        }
      }

Get Active Tasks
~~~~~~~~~~~~~~~~

**GET** ``/api/v1/celery/active``

Get list of currently active tasks.

   **Example Request:**

   .. code-block:: bash

      curl http://localhost:3050/api/v1/celery/active

   **Example Response:**

   .. code-block:: json

      {
        "active_tasks": [
          {
            "id": "task-uuid-123",
            "name": "process_file_upload",
            "worker": "celery@worker1",
            "queue": "file_ops",
            "started": "2025-06-03T20:30:00Z"
          }
        ],
        "total": 1
      }

Task Management Endpoints
-------------------------

Submit Task
~~~~~~~~~~~

**POST** ``/api/v1/celery/tasks``

Submit a new task to the Celery queue.

   **Request Body:**

   .. code-block:: json

      {
        "task_name": "process_file_upload",
        "args": ["file_id_123"],
        "kwargs": {"priority": "high"},
        "queue": "file_ops"
      }

   **Example Request:**

   .. code-block:: bash

      curl -X POST \
        -H "Content-Type: application/json" \
        -d '{"task_name": "process_file_upload", "args": ["file_123"], "queue": "file_ops"}' \
        http://localhost:3050/api/v1/celery/tasks

   **Example Response:**

   .. code-block:: json

      {
        "task_id": "task-uuid-456",
        "status": "PENDING",
        "queue": "file_ops",
        "submitted_at": "2025-06-03T20:35:00Z"
      }

Get Task Status
~~~~~~~~~~~~~~~

**GET** ``/api/v1/celery/tasks/(task_id)``

Get the status and result of a specific task.

   **Parameters:**
   
   - ``task_id`` (string): The unique task identifier

   **Example Request:**

   .. code-block:: bash

      curl http://localhost:3050/api/v1/celery/tasks/task-uuid-456

   **Example Response:**

   .. code-block:: json

      {
        "task_id": "task-uuid-456",
        "status": "SUCCESS",
        "result": {
          "file_id": "file_123",
          "processed": true,
          "size": 1024
        },
        "started_at": "2025-06-03T20:35:00Z",
        "completed_at": "2025-06-03T20:35:30Z"
      }

   **Task Status Values:**
   
   - ``PENDING``: Task is waiting to be processed
   - ``STARTED``: Task has been started by a worker
   - ``SUCCESS``: Task completed successfully
   - ``FAILURE``: Task failed with an error
   - ``RETRY``: Task is being retried
   - ``REVOKED``: Task was revoked/cancelled

Cancel Task
~~~~~~~~~~~

**DELETE** ``/api/v1/celery/tasks/(task_id)``

Cancel a pending or running task.

   **Parameters:**
   
   - ``task_id`` (string): The unique task identifier

   **Example Request:**

   .. code-block:: bash

      curl -X DELETE http://localhost:3050/api/v1/celery/tasks/task-uuid-456

   **Example Response:**

   .. code-block:: json

      {
        "task_id": "task-uuid-456",
        "status": "REVOKED",
        "message": "Task cancelled successfully"
      }

Queue Management Endpoints
--------------------------

Get Queue Information
~~~~~~~~~~~~~~~~~~~~~

**GET** ``/api/v1/celery/queues``

Get information about all available queues.

   **Example Request:**

   .. code-block:: bash

      curl http://localhost:3050/api/v1/celery/queues

   **Example Response:**

   .. code-block:: json

      {
        "queues": [
          {
            "name": "default",
            "messages": 5,
            "consumers": 2,
            "workers": ["celery@worker1"]
          },
          {
            "name": "file_ops",
            "messages": 2,
            "consumers": 1,
            "workers": ["celery@worker2"]
          },
          {
            "name": "vm_ops",
            "messages": 0,
            "consumers": 1,
            "workers": ["celery@worker3"]
          }
        ]
      }

Purge Queue
~~~~~~~~~~~

**DELETE** ``/api/v1/celery/queues/(queue_name)``

Purge all messages from a specific queue.

   **Parameters:**
   
   - ``queue_name`` (string): Name of the queue to purge

   **Example Request:**

   .. code-block:: bash

      curl -X DELETE http://localhost:3050/api/v1/celery/queues/file_ops

   **Example Response:**

   .. code-block:: json

      {
        "queue": "file_ops",
        "purged_messages": 5,
        "status": "purged"
      }

Async Task Endpoints
--------------------

The API also provides async endpoints for common operations:

File Processing
~~~~~~~~~~~~~~~

**POST** ``/api/v1/async/tasks/file-processing``

Submit a file for asynchronous processing.

   **Request Body:**

   .. code-block:: json

      {
        "file_id": "file_123",
        "processing_type": "analysis",
        "options": {
          "scan_malware": true,
          "extract_metadata": true
        }
      }

VM Operations
~~~~~~~~~~~~~

**POST** ``/api/v1/async/tasks/vm-operations``

Submit VM operation tasks.

   **Request Body:**

   .. code-block:: json

      {
        "operation": "inject_file",
        "vm_id": "vm_456",
        "file_path": "/path/to/file",
        "target_path": "/target/path"
      }

Error Handling
--------------

Celery API endpoints return standard HTTP status codes:

- ``200``: Success
- ``202``: Task accepted (for async operations)
- ``400``: Bad request (invalid parameters)
- ``404``: Task or resource not found
- ``500``: Internal server error
- ``503``: Service unavailable (Celery unhealthy)

Error Response Format:

.. code-block:: json

   {
     "error": "task_not_found",
     "message": "Task with ID 'invalid-id' not found",
     "details": {
       "task_id": "invalid-id",
       "timestamp": "2025-06-03T20:40:00Z"
     }
   }

Authentication
--------------

Celery endpoints use the same authentication as other API endpoints. In test mode, authentication is bypassed.

For production use, include the appropriate authentication headers with your requests.

Rate Limiting
-------------

Celery endpoints are subject to rate limiting to prevent abuse:

- **Task submission**: 100 requests per minute
- **Status queries**: 1000 requests per minute
- **Queue operations**: 10 requests per minute

Monitoring Integration
----------------------

The Celery API integrates with the monitoring system:

- Task metrics are reported to Cachet dashboard
- Worker health is monitored continuously
- Alerts are generated for failed tasks or unhealthy workers

For more information on monitoring, see the :doc:`../architecture/cachet` documentation.
