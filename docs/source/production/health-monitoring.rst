Health Monitoring
=================

TurdParty includes comprehensive health monitoring with 40+ automated checks covering all system components and services.

Overview
--------

The health monitoring system provides:

- **40+ comprehensive health checks**
- **Real-time service status monitoring**
- **Container health verification**
- **Database connectivity testing**
- **API endpoint validation**
- **Performance monitoring**
- **Resource usage tracking**

Health Check Script
------------------

The main health check script is located at ``scripts/monitoring/healthcheck.sh`` and provides comprehensive system validation.

Usage
~~~~~

.. code-block:: bash

   # Run comprehensive health check
   ./scripts/monitoring/healthcheck.sh

   # Using the development workflow
   ./scripts/ci/dev-workflow.sh health

Health Check Categories
----------------------

1. Container Status
~~~~~~~~~~~~~~~~~~

Verifies all TurdParty containers are running and healthy:

- **turdparty_api** - Main FastAPI application
- **turdparty_postgres** - PostgreSQL database
- **turdparty_redis** - Redis cache and message broker
- **turdparty_minio** - MinIO object storage
- **turdparty_minio_ssh** - MinIO SSH wrapper
- **turdparty_celery_default** - Default Celery worker
- **turdparty_celery_file_ops** - File operations worker
- **turdparty_celery_vm_ops** - VM operations worker
- **turdparty_celery_flower** - Celery monitoring

2. Service Endpoints
~~~~~~~~~~~~~~~~~~~

Tests critical service endpoints:

- **API Health** (``http://localhost:3050/api/v1/health``)
- **MinIO Health** (``http://localhost:3300/minio/health/live``)
- **Celery Flower API** (``http://localhost:3450/api/workers``)

3. Database Connectivity
~~~~~~~~~~~~~~~~~~~~~~~~

Validates database connections:

- **PostgreSQL** - Connection acceptance testing
- **Redis** - Ping response validation

4. File Structure Verification
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Ensures proper project organization:

- **Essential root files** - Validates core project files exist
- **Organized directories** - Confirms proper directory structure
- **Configuration files** - Verifies config file locations

5. Configuration Validation
~~~~~~~~~~~~~~~~~~~~~~~~~~

Checks configuration file integrity:

- **Docker configuration** - docker-compose.yml validation
- **Python configuration** - pyproject.toml validation
- **Node.js configuration** - package.json validation
- **Development configuration** - shell.nix and other configs

Health Check Output
------------------

The health check provides colored, detailed output:

.. code-block:: text

   🔍 TurdParty Health Check - Post Cleanup Verification
   ==================================================================

   📦 Phase 1: Container Status
   --------------------------------
   ✅ PASS: API container running
   ✅ PASS: API container healthy
   ✅ PASS: PostgreSQL container running
   ✅ PASS: Redis container running
   ✅ PASS: MinIO container running
   ✅ PASS: Celery Default Worker container running
   ✅ PASS: Celery File Ops Worker container running
   ✅ PASS: Celery VM Ops Worker container running
   ✅ PASS: Celery Flower container running

   🌐 Phase 2: Service Endpoints
   --------------------------------
   ✅ PASS: API Health endpoint responding
   ✅ PASS: MinIO Health endpoint responding
   ✅ PASS: Celery Flower API endpoint responding

   🗄️  Phase 3: Database Connectivity
   --------------------------------
   ✅ PASS: PostgreSQL accepting connections
   ✅ PASS: Redis responding to ping

   📁 Phase 4: File Structure Verification
   --------------------------------
   ✅ PASS: Essential file exists: main.py
   ✅ PASS: Essential file exists: README.md
   ✅ PASS: Organized directory exists: tests
   ✅ PASS: Organized directory exists: docs
   ✅ PASS: Organized directory exists: scripts
   ✅ PASS: Organized directory exists: config

   ⚙️  Phase 5: Configuration Files
   --------------------------------
   ✅ PASS: Config file exists: config/docker/docker-compose.yml
   ✅ PASS: Config file exists: config/python/pyproject.toml
   ✅ PASS: Config file exists: config/node/package.json

   ==================================================================
   📊 HEALTH CHECK SUMMARY
   ==================================================================
   Total Checks: 40
   Passed: 40
   Failed: 0

   🎉 ALL CHECKS PASSED! TurdParty is healthy and ready for production!

Integration with CI/CD
----------------------

Health monitoring is integrated into the CI/CD pipeline:

- **Pre-deployment checks** - Validates system health before deployment
- **Post-deployment verification** - Confirms successful deployment
- **Continuous monitoring** - Regular health check execution
- **Automated alerts** - Failure notifications and reporting

Performance Monitoring
---------------------

The health monitoring system also tracks performance metrics:

Response Time Monitoring
~~~~~~~~~~~~~~~~~~~~~~~

- **API response times** - Tracks endpoint performance
- **Database query times** - Monitors database performance
- **Service startup times** - Measures container initialization

Resource Usage Tracking
~~~~~~~~~~~~~~~~~~~~~~~

- **CPU usage** - Per-container CPU consumption
- **Memory usage** - Memory allocation and usage patterns
- **Disk usage** - Storage consumption monitoring
- **Network usage** - Network traffic analysis

Container Health Checks
-----------------------

Docker containers include built-in health checks where applicable:

.. code-block:: yaml

   # Example health check configuration
   healthcheck:
     test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
     interval: 30s
     timeout: 10s
     retries: 3
     start_period: 40s

Monitoring Best Practices
-------------------------

**Regular Execution**
   Run health checks before and after deployments

**Automated Integration**
   Include health checks in CI/CD pipelines

**Threshold Monitoring**
   Set performance thresholds and alert on violations

**Trend Analysis**
   Track health metrics over time for trend analysis

**Documentation**
   Keep health check documentation up to date

Troubleshooting
--------------

Common health check failures and solutions:

**Container Not Running**
   - Check Docker daemon status
   - Verify container configuration
   - Review container logs

**Service Endpoint Failures**
   - Verify service is listening on correct port
   - Check firewall and network configuration
   - Review service logs for errors

**Database Connectivity Issues**
   - Verify database container is running
   - Check database credentials and configuration
   - Test network connectivity between containers

**Configuration File Issues**
   - Verify file paths and permissions
   - Check configuration syntax
   - Ensure all required configuration files exist

The health monitoring system provides comprehensive visibility into system status and helps ensure reliable operation of the TurdParty application.
