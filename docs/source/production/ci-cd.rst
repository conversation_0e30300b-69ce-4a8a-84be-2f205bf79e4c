Local CI/CD Pipeline
===================

TurdParty includes a comprehensive local CI/CD pipeline that provides an alternative to GitHub Actions, running entirely on your local machine.

Overview
--------

The local CI/CD system provides:

- **9-stage comprehensive testing pipeline**
- **40+ automated health checks**
- **Pre-commit hooks for quality gates**
- **Development workflow automation**
- **Performance and security monitoring**

Why Local CI/CD?
----------------

Advantages over GitHub Actions:

✅ **Faster Feedback**
   No waiting for GitHub runners - immediate results

✅ **Cost Effective**
   No GitHub Actions minutes consumed

✅ **Privacy**
   All code stays on your machine

✅ **Customizable**
   Full control over the pipeline

✅ **Offline Capable**
   Works without internet connection

✅ **Development Friendly**
   Integrated with local workflow

Pipeline Stages
--------------

The CI/CD pipeline consists of 9 comprehensive stages:

1. **Environment Setup & Validation**
   - Tool availability checks
   - Container status verification
   - Project structure validation

2. **Code Quality & Linting**
   - Python syntax validation
   - Debug statement detection
   - Code style checks

3. **Security Scanning**
   - Sensitive data detection
   - Docker security validation
   - Vulnerability scanning

4. **Service Health Verification**
   - 40+ comprehensive health checks
   - Container status monitoring
   - Service endpoint validation

5. **API Integration Testing**
   - Endpoint response validation
   - API documentation checks
   - Performance testing

6. **Database Integration Testing**
   - PostgreSQL connectivity
   - Redis connectivity
   - Data integrity checks

7. **File Upload & Storage Testing**
   - MinIO health checks
   - File operation validation
   - Storage integration tests

8. **Performance Validation**
   - Response time monitoring
   - Resource usage tracking
   - Performance threshold validation

9. **Documentation Validation**
   - Documentation completeness
   - Structure validation
   - Content quality checks

Usage
-----

Quick Commands
~~~~~~~~~~~~~~

.. code-block:: bash

   # Start development environment
   ./scripts/ci/dev-workflow.sh start

   # Run health checks
   ./scripts/ci/dev-workflow.sh health

   # Run full CI pipeline
   ./scripts/ci/local-ci.sh

   # Using workflow helper
   ./scripts/ci/dev-workflow.sh ci

Development Workflow
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Start services
   ./scripts/ci/dev-workflow.sh start

   # 2. Make code changes
   # ... edit files ...

   # 3. Run CI before commit
   ./scripts/ci/local-ci.sh

   # 4. Commit (pre-commit hook runs automatically)
   git commit -m "feat: add new feature"

   # 5. Deploy if CI passes
   ./scripts/ci/dev-workflow.sh restart

Pre-commit Hooks
---------------

Automated quality gates that run before each commit:

- **Python syntax validation**
- **Debug statement detection**
- **Sensitive data scanning**
- **Security checks**

The pre-commit hook prevents commits that don't meet quality standards.

Configuration
------------

CI/CD configuration is stored in:

- ``scripts/ci/local-ci.conf`` - Main configuration
- ``scripts/ci/hooks/pre-commit`` - Pre-commit hook
- ``scripts/ci/templates/commit-template.txt`` - Commit message template

Makefile Integration
-------------------

Common development tasks are available via Makefile:

.. code-block:: bash

   make help      # Show all available commands
   make start     # Start development environment
   make ci        # Run local CI pipeline
   make health    # Run health checks
   make test      # Run test suite
   make clean     # Clean up containers and volumes

Results and Reporting
--------------------

The CI/CD pipeline provides:

- **Colored output** for easy status identification
- **Detailed stage reporting** with pass/fail status
- **Performance metrics** including response times
- **Resource usage monitoring**
- **Comprehensive summary** with actionable feedback

Example output:

.. code-block:: text

   🔄 Stage 1: Environment Setup & Validation
   ✅ PASS: docker is available
   ✅ PASS: python3 available in container
   
   🔄 Stage 2: Code Quality & Linting
   ✅ PASS: Python syntax check
   ⚠️  WARN: Debug print statements found in code
   
   📊 LOCAL CI/CD PIPELINE SUMMARY
   Total Stages: 9
   Passed: 8
   Failed: 0
   
   🎉 ALL STAGES PASSED! Ready for deployment!

This local CI/CD system ensures production-ready code quality while maintaining fast development cycles.
