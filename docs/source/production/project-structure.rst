Project Structure
=================

TurdParty has been completely refactored with a clean, production-ready project structure. The root directory has been reduced from 181+ files to just 8 essential files (94% reduction).

Root Directory Structure
------------------------

The root directory now contains only essential files:

.. code-block:: text

   /
   ├── main.py                    # Main application entry point
   ├── README.md                  # Project overview and setup
   ├── CHANGELOG.md               # Version history
   ├── ROADMAP.md                 # Project roadmap
   ├── SUMMARY.md                 # Project summary
   ├── ROOT_CLEANUP_SUMMARY.md    # Cleanup documentation
   ├── Vagrantfile                # VM infrastructure
   └── .gitignore                 # Git ignore rules

Organized Directory Structure
----------------------------

All files have been systematically organized into logical directories:

Application Code
~~~~~~~~~~~~~~~

.. code-block:: text

   api/                    # FastAPI application source code
   ├── models/            # Database models
   ├── routes/            # API route handlers
   ├── services/          # Business logic services
   ├── utils/             # Utility functions
   └── tests/             # API-specific tests

Frontend & UI
~~~~~~~~~~~~

.. code-block:: text

   frontend/              # Frontend application
   ├── static/           # Static assets
   ├── templates/        # HTML templates
   └── components/       # UI components

Testing Organization
~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   tests/                 # All test files (163 files organized)
   ├── api/              # API tests (16 files)
   ├── upload/           # Upload tests (10 files)
   ├── vm/               # VM tests (11 files)
   ├── scripts/          # Test runners (25+ files)
   ├── config/           # Test configuration (6 files)
   ├── fixtures/         # Test fixtures (6 files)
   ├── minio/            # MinIO tests (4 files)
   └── root-cleanup/     # Cleanup-related tests (11 files)

Documentation Structure
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   docs/                  # All documentation (72 files organized)
   ├── architecture/     # System design (4 files)
   ├── production/       # Deployment docs (5 files)
   ├── cleanup/          # Cleanup guides (3 files)
   ├── ai-analysis/      # AI analysis (4 files)
   ├── development/      # Dev guides (2 files)
   └── source/           # Sphinx documentation source

Scripts Organization
~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   scripts/               # All scripts (199 files organized)
   ├── database/         # DB scripts (5 files)
   ├── monitoring/       # Monitoring (8 files)
   ├── deployment/       # Deployment (6 files)
   ├── vagrant/          # VM management (5 files)
   ├── ui/               # UI utilities (3 files)
   ├── development/      # Dev tools (6 files)
   ├── utilities/        # Cleanup tools (3 files)
   ├── testing/          # Test scripts (8 files)
   └── ci/               # CI/CD pipeline (6 files)

Configuration Management
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   config/                # All configuration files (24 files)
   ├── docker/           # Docker configs (7 files)
   ├── node/             # Node.js configs (2 files)
   ├── python/           # Python configs (4 files)
   ├── playwright/       # Test configs (2 files)
   └── *.nix             # Development configs (6 files)

Data & Logs
~~~~~~~~~~

.. code-block:: text

   data/                  # Data files (5 files)
   └── temp/             # Temporary data

   logs/                  # Log files (7 files)
   └── cleanup/          # Cleanup logs

Infrastructure
~~~~~~~~~~~~~

.. code-block:: text

   .dockerwrapper/        # Docker wrapper (namespaced)
   ├── docker-compose.yml # Main compose file
   └── port_mappings.json # Port configuration

   migrations/            # Database migrations
   _archive_/             # Archived files

File Organization Principles
----------------------------

The reorganization follows these principles:

**1. Separation of Concerns**
   - Application code separate from configuration
   - Tests organized by functionality
   - Documentation categorized by audience

**2. Logical Grouping**
   - Related files grouped together
   - Clear directory naming conventions
   - Consistent structure across categories

**3. Accessibility**
   - Important files easy to find
   - Clear navigation paths
   - Comprehensive documentation

**4. Maintainability**
   - Scalable directory structure
   - Easy to add new components
   - Clear ownership of files

**5. Production Readiness**
   - Clean root directory
   - Proper configuration management
   - Organized deployment files

Benefits of New Structure
------------------------

**For Developers:**
- Faster file navigation
- Clear project organization
- Easier onboarding for new team members
- Reduced cognitive load

**For Operations:**
- Clear deployment structure
- Organized configuration files
- Easy monitoring and maintenance
- Proper separation of environments

**For Maintenance:**
- Easy to locate specific functionality
- Clear dependency relationships
- Simplified backup and archiving
- Better version control organization

Migration Documentation
----------------------

Complete documentation of the reorganization process is available in:

- ``ROOT_CLEANUP_SUMMARY.md`` - Complete cleanup summary
- ``docs/cleanup/`` - Detailed cleanup guides
- Git commit history with detailed change logs

Each moved file category includes documentation explaining:
- What was moved and where
- Why it was moved
- How to update references
- Next steps for integration

This structure provides a solid foundation for continued development and production deployment.
