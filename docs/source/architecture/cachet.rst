Cachet Status Dashboard
=======================

TurdParty uses <PERSON>ache<PERSON> as its service status dashboard, providing real-time monitoring and incident management capabilities for all system services.

Overview
--------

Cachet is an open-source status page system that provides:

- **Real-time Service Status**: Monitor all TurdParty services
- **Incident Management**: Create, update, and resolve incidents
- **Scheduled Maintenance**: Plan and announce maintenance windows
- **Metrics Display**: Show performance metrics and uptime
- **Subscriber Notifications**: Email alerts for status changes
- **API Integration**: Automate status updates

Architecture
------------

The Cachet implementation consists of:

.. code-block:: text

    ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
    │   Cachet    │────▶│ PostgreSQL  │     │  TurdParty  │
    │ Dashboard   │     │  (Cachet)   │     │  Services   │
    │  (:3501)    │     │             │     │             │
    └─────────────┘     └─────────────┘     └─────────────┘
          │                                       │
          │                                       │
          ▼                                       ▼
    ┌─────────────┐                         ┌─────────────┐
    │   Cachet    │                         │   Status    │
    │     API     │◀────────────────────────│  Updates    │
    │             │                         │             │
    └─────────────┘                         └─────────────┘

Components
----------

C<PERSON>t Dashboard
~~~~~~~~~~~~~~~~

- **URL**: http://localhost:3501
- **Container**: ``turdparty_cachet``
- **Status**: ✅ Healthy and configured
- **Features**: Web interface for status monitoring

Cachet Database
~~~~~~~~~~~~~~~

- **Container**: ``turdparty_postgres_cachet``
- **Database**: PostgreSQL 12
- **Status**: ✅ Healthy
- **Purpose**: Stores Cachet configuration and status data

Configuration
-------------

The Cachet dashboard is pre-configured with:

Application Settings
~~~~~~~~~~~~~~~~~~~~

- **Name**: TurdParty Status
- **Domain**: localhost:3501
- **Timezone**: UTC
- **Theme**: Default with custom branding

Service Components
~~~~~~~~~~~~~~~~~~

The following TurdParty services are monitored:

1. **TurdParty API** (http://localhost:3050)
   - Status: Operational
   - Health check: ``/api/v1/health``

2. **Frontend Application** (http://localhost:3100)
   - Status: Operational
   - Health check: HTTP response

3. **PostgreSQL Database** (localhost:3200)
   - Status: Operational
   - Health check: Connection test

4. **MinIO Storage** (http://localhost:3300)
   - Status: Operational
   - Health check: ``/minio/health/live``

5. **Redis Broker** (localhost:3400)
   - Status: Operational
   - Health check: PING command

6. **Celery Workers**
   - Status: Operational
   - Health check: Worker heartbeat

Setup and Installation
----------------------

Automatic Setup
~~~~~~~~~~~~~~~

The Cachet dashboard is automatically configured when starting services:

.. code-block:: bash

   # Start Cachet with auto-configuration
   docker compose -f .dockerwrapper/docker-compose.cachet.yml up -d
   
   # Run auto-configuration script
   ./.dockerwrapper/auto-configure-cachet.sh

Manual Setup
~~~~~~~~~~~~

If manual configuration is needed:

1. Access http://localhost:3501
2. Complete the setup wizard
3. Configure database connection
4. Set application settings
5. Add service components

API Integration
---------------

Cachet provides a REST API for automated status updates:

Authentication
~~~~~~~~~~~~~~

.. code-block:: bash

   # Set API token
   export CACHET_API_TOKEN="your-api-token"
   export CACHET_API_URL="http://localhost:3501/api/v1"

Update Component Status
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Update service status
   curl -X PUT \
     -H "Content-Type: application/json" \
     -H "X-Cachet-Token: $CACHET_API_TOKEN" \
     -d '{"status": 1}' \
     "$CACHET_API_URL/components/1"

Status Values:
- ``1``: Operational
- ``2``: Performance Issues
- ``3``: Partial Outage
- ``4``: Major Outage

Create Incident
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create new incident
   curl -X POST \
     -H "Content-Type: application/json" \
     -H "X-Cachet-Token: $CACHET_API_TOKEN" \
     -d '{
       "name": "API Maintenance",
       "message": "Scheduled maintenance in progress",
       "status": 2,
       "component_id": 1
     }' \
     "$CACHET_API_URL/incidents"

Automated Monitoring
--------------------

Status Update Script
~~~~~~~~~~~~~~~~~~~~

The system includes an automated status update script:

.. code-block:: bash

   # Run status updates
   ./.dockerwrapper/update-cachet-status.sh

This script:
- Checks health of all services
- Updates component status in Cachet
- Creates incidents for service failures
- Resolves incidents when services recover

Health Check Integration
~~~~~~~~~~~~~~~~~~~~~~~~

Services can automatically report their status:

.. code-block:: python

   import requests
   
   def update_service_status(component_id: int, status: int):
       """Update service status in Cachet."""
       headers = {
           'Content-Type': 'application/json',
           'X-Cachet-Token': os.getenv('CACHET_API_TOKEN')
       }
       data = {'status': status}
       
       response = requests.put(
           f"{CACHET_API_URL}/components/{component_id}",
           headers=headers,
           json=data
       )
       return response.json()

Customization
-------------

Theme Customization
~~~~~~~~~~~~~~~~~~~

1. Access Dashboard → Settings → Theme
2. Customize colors and branding
3. Upload custom logo
4. Add custom CSS

Email Notifications
~~~~~~~~~~~~~~~~~~~

Configure SMTP settings for email notifications:

1. Go to Dashboard → Settings → Mail
2. Configure SMTP server settings
3. Test email delivery
4. Enable subscriber notifications

Metrics and Analytics
~~~~~~~~~~~~~~~~~~~~~

Add performance metrics:

1. Create metrics in Dashboard → Metrics
2. Configure data collection
3. Display on status page
4. Set up automated reporting

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

1. **Setup page redirects**: Database may already be configured
2. **API authentication errors**: Check API token configuration
3. **Service status not updating**: Verify health check endpoints

Debugging
~~~~~~~~~

.. code-block:: bash

   # Check Cachet logs
   docker logs turdparty_cachet
   
   # Check database connectivity
   docker exec turdparty_postgres_cachet psql -U postgres -d cachet -c "SELECT 1"
   
   # Test API endpoint
   curl -H "X-Cachet-Token: $CACHET_API_TOKEN" \
        "$CACHET_API_URL/components"

For more information on Cachet configuration, see the official Cachet documentation.
