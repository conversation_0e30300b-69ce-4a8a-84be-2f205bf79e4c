# TurdParty Folder Structure Cleanup PRD

## Executive Summary

This Product Requirements Document (PRD) outlines a comprehensive plan to clean up the TurdParty project's folder structure, establish an `_archive_` directory for deprecated files, and implement pre/post-coverage testing to ensure no functionality is lost during the cleanup process.

## Objectives

1. **Organize Project Structure**: Create a clean, logical folder hierarchy that follows industry best practices
2. **Establish Archive System**: Implement a systematic approach to preserve deleted files in `_archive_` with proper documentation
3. **Maintain Test Coverage**: Ensure test coverage remains consistent before and after cleanup through comprehensive testing
4. **Improve Developer Experience**: Make the codebase more navigable and maintainable
5. **Document Changes**: Track all cleanup actions with detailed logs and checklists

## Current State Analysis

### Project Structure Issues
- **Root Directory Clutter**: 200+ files in root directory including test files, scripts, and temporary files
- **Duplicate Test Files**: Multiple test runners and configurations scattered throughout
- **Mixed Concerns**: API, UI, scripts, and documentation files intermixed
- **Inconsistent Naming**: Various naming conventions across different file types
- **Orphaned Files**: Files that may no longer be in use but haven't been removed

### Testing Infrastructure
- **Multiple Test Runners**: pytest, Playwright, Jest configurations
- **Coverage Tools**: pytest-cov, coverage.py, custom coverage scripts
- **Test Directories**: `/tests`, `/api/tests`, `/scripts/testing`, `/playwright-test`
- **Configuration Files**: Multiple pytest.ini, playwright.config.js files

## Cleanup Strategy

### Phase 1: Pre-Cleanup Assessment (Week 1)

#### 1.1 Test Coverage Baseline
- [ ] Run comprehensive test suite to establish baseline coverage
- [ ] Generate detailed coverage reports (HTML, XML, JSON)
- [ ] Document current test execution times
- [ ] Identify and catalog all test files and configurations
- [ ] Create test inventory with dependencies

#### 1.2 File Inventory and Analysis
- [ ] Catalog all files in root directory with last modified dates
- [ ] Identify duplicate files and their purposes
- [ ] Map file dependencies and relationships
- [ ] Identify orphaned or unused files
- [ ] Document current folder structure

#### 1.3 Archive System Setup
- [ ] Create `_archive_` directory structure
- [ ] Implement archive documentation template
- [ ] Create archival scripts for automated file movement
- [ ] Establish archive naming conventions

### Phase 2: Core Structure Cleanup (Week 2)

#### 2.1 Root Directory Cleanup
**Target Structure:**
```
/
├── _archive_/           # Archived files with documentation
├── api/                 # FastAPI application
├── frontend/            # React frontend application
├── ui/                  # Streamlit UI components
├── scripts/             # Utility and deployment scripts
├── tests/               # Consolidated test suite
├── docs/                # Documentation
├── .dockerwrapper/      # Docker configurations
├── migrations/          # Database migrations
├── static/              # Static assets
├── config/              # Configuration files
└── [essential root files only]
```

**Files to Archive:**
- [ ] Duplicate test files (test-*.js, test_*.py in root)
- [ ] Temporary files (*.html, *.png in root)
- [ ] Old configuration files
- [ ] Unused scripts and utilities
- [ ] Debug and diagnostic files

#### 2.2 Test Consolidation
- [ ] Merge duplicate test configurations
- [ ] Consolidate test files into `/tests` directory
- [ ] Standardize test naming conventions
- [ ] Update test discovery paths
- [ ] Migrate Playwright tests to standard structure

### Phase 3: Advanced Organization (Week 3)

#### 3.1 Directory-Specific Cleanup
- [ ] **API Directory**: Organize routes, models, services
- [ ] **Scripts Directory**: Categorize by function (testing, deployment, utilities)
- [ ] **Frontend Directory**: Clean up node_modules, build artifacts
- [ ] **Documentation**: Organize by topic and audience

#### 3.2 Configuration Standardization
- [ ] Consolidate Docker configurations
- [ ] Standardize environment files
- [ ] Merge duplicate configuration files
- [ ] Update import paths and references

### Phase 4: Validation and Documentation (Week 4)

#### 4.1 Post-Cleanup Testing
- [ ] Run complete test suite
- [ ] Generate new coverage reports
- [ ] Compare pre/post coverage metrics
- [ ] Validate all functionality works
- [ ] Performance testing

#### 4.2 Documentation Updates
- [ ] Update README files
- [ ] Create folder structure documentation
- [ ] Update development setup guides
- [ ] Document new conventions

## Archive System Specification

### Directory Structure
```
_archive_/
├── README.md                    # Archive documentation
├── cleanup-log.md              # Detailed cleanup actions
├── [original-path]/            # Maintains original structure
│   ├── file1.py               # Archived file with header
│   └── file2.js               # Archived file with header
└── metadata/
    ├── file-inventory.json     # Original file catalog
    └── dependency-map.json     # File relationship mapping
```

### Archive File Format
Each archived file will include a header comment:

```python
# ARCHIVED FILE
# Original Location: /root/old_test.py
# Archived Date: 2024-01-15
# Reason: Duplicate functionality, replaced by /tests/test_main.py
# Dependencies: None identified
# Notes: This file was a standalone test that has been integrated into the main test suite
# 
# [Original file content follows...]
```

## Testing Strategy

### Pre-Cleanup Coverage Collection
1. **Unit Tests**: Run all pytest tests with coverage
2. **Integration Tests**: Execute API and database tests
3. **E2E Tests**: Run Playwright and UI tests
4. **Performance Tests**: Baseline performance metrics

### Coverage Comparison Framework
```bash
# Pre-cleanup
./scripts/generate_baseline_coverage.sh

# Post-cleanup
./scripts/generate_post_cleanup_coverage.sh

# Comparison
./scripts/compare_coverage_reports.sh
```

### Success Criteria
- **Coverage Maintenance**: ≥95% of original coverage maintained
- **Test Execution**: All tests pass in new structure
- **Performance**: No significant performance degradation
- **Functionality**: All features work as expected

## Implementation Checklist

### Week 1: Assessment Phase
- [ ] Create baseline coverage report
- [ ] Generate file inventory
- [ ] Set up archive system
- [ ] Document current structure
- [ ] Identify cleanup targets

### Week 2: Core Cleanup
- [ ] Archive root directory clutter
- [ ] Consolidate test files
- [ ] Organize main directories
- [ ] Update configuration files
- [ ] Test intermediate state

### Week 3: Advanced Organization
- [ ] Clean subdirectories
- [ ] Standardize naming conventions
- [ ] Update documentation
- [ ] Optimize Docker configurations
- [ ] Validate integrations

### Week 4: Validation
- [ ] Run complete test suite
- [ ] Generate final coverage report
- [ ] Compare metrics
- [ ] Update documentation
- [ ] Create maintenance guidelines

## Risk Mitigation

### Backup Strategy
- [ ] Full repository backup before starting
- [ ] Incremental backups after each phase
- [ ] Archive system as safety net
- [ ] Git branch for cleanup work

### Rollback Plan
- [ ] Maintain original file locations in archive
- [ ] Document all changes in detail
- [ ] Create restoration scripts
- [ ] Test rollback procedures

## Success Metrics

### Quantitative Metrics
- **File Count Reduction**: Target 50% reduction in root directory files
- **Test Coverage**: Maintain ≥95% of baseline coverage
- **Build Time**: No increase in CI/CD pipeline time
- **Directory Depth**: Reduce average nesting levels

### Qualitative Metrics
- **Developer Experience**: Improved navigation and understanding
- **Maintainability**: Easier to locate and modify files
- **Documentation**: Clear structure documentation
- **Consistency**: Standardized naming and organization

## Timeline

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Assessment | Week 1 | Baseline coverage, file inventory, archive setup |
| Core Cleanup | Week 2 | Root directory organized, tests consolidated |
| Advanced Organization | Week 3 | All directories cleaned, configurations standardized |
| Validation | Week 4 | Coverage comparison, documentation, guidelines |

## Conclusion

This cleanup initiative will transform the TurdParty codebase into a well-organized, maintainable project structure while preserving all functionality through comprehensive testing and systematic archival of removed files.
