# Test Troubleshooting Guide

This guide will help you diagnose and fix the systemic test failures in this project.

## Common Causes of Test Failures

Based on the test output, all tests are failing with similar errors. Here are the most likely causes:

1. **Missing Python dependencies**
   - Tests are using system Python without a virtual environment
   - Required packages are not installed

2. **Missing external services**
   - Min<PERSON> (appears in many test names)
   - Vagrant/VirtualBox for VM tests
   - SSH connectivity issues

3. **Configuration issues**
   - Missing environment variables
   - Incorrect paths or permissions

4. **Mock/fixture issues**
   - Tests may rely on fixtures that aren't set up correctly

## Using the Diagnostic Tools

We've created several tools to help diagnose and fix these issues:

1. **check_test_env.py** - Check your test environment setup
2. **diagnose_test.py** - Run a specific test with verbose output
3. **run_tests.py** - Run tests with proper environment setup

### Step 1: Check Your Environment

Run the environment checker to see what might be missing:

```bash
./check_test_env.py
```

This will show:
- Python version and installed packages
- External tools availability (docker, vagrant, etc.)
- Running services on common ports
- Environment variables needed for tests
- Test directories and files

### Step 2: Diagnose a Specific Failing Test

To understand exactly why a test is failing, run:

```bash
./diagnose_test.py path/to/test_file.py
```

For example:
```bash
./diagnose_test.py ./scripts/test_minio.py
```

This will show detailed error output to help diagnose the issue.

### Step 3: Fix Common Issues

#### Missing Python Dependencies

```bash
# Create and activate a virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install test requirements
pip install -r requirements-test.txt
```

#### Missing External Services

For MinIO:
```bash
# Using Docker
docker run -d --name minio \
  -p 9000:9000 -p 9001:9001 \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  minio/minio server /data --console-address ":9001"
```

For Vagrant/VirtualBox:
```bash
# Install VirtualBox and Vagrant if missing
# Set environment variables
export VAGRANT_HOME=/path/to/vagrant/home
export VAGRANT_DEFAULT_PROVIDER=virtualbox
```

#### Setting Up Environment Variables

Create a `.env` file with necessary variables:
```
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
PYTHONPATH=/path/to/project
```

Then source it before running tests:
```bash
source .env
```

### Step 4: Run Tests With Proper Setup

Use the test runner script:

```bash
./run_tests.py
```

Or for a specific test:
```bash
./run_tests.py --test ./scripts/test_minio.py
```

## Common Test Fixes

### For MinIO Tests

1. Make sure MinIO is running on port 9000
2. Set proper environment variables:
   ```
   MINIO_ACCESS_KEY=minioadmin
   MINIO_SECRET_KEY=minioadmin
   ```
3. Check if the tests use mock MinIO or expect a real instance

### For VM/Vagrant Tests

1. Ensure Vagrant and VirtualBox are installed
2. Make sure VM templates exist in the expected location
3. Check SSH key setup for VM access

### For API Tests

1. Ensure the backend API is running on the expected port
2. Check that database connections are properly configured
3. Verify that auth bypass is enabled for tests

## Creating Mock Services

If you can't set up all the required services, consider creating mock versions:

```python
# Example of mocking MinIO in tests
@pytest.fixture
def mock_minio_client(mocker):
    mock_client = mocker.MagicMock()
    mock_client.bucket_exists.return_value = True
    return mock_client
```

## Getting Help

If you continue to have issues, try:

1. Running one test at a time with `--verbose` to see detailed output
2. Checking logs in test_logs/ directory if available
3. Running with pytest's `-xvs` flags for detailed debugging
4. Adding print statements to the test file to debug specific issues

## Additional Resources

- [pytest Documentation](https://docs.pytest.org/)
- [MinIO Python SDK](https://docs.min.io/docs/python-client-quickstart-guide.html)
- [Vagrant Documentation](https://www.vagrantup.com/docs) 