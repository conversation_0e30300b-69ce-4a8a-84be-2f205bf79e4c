# TurdParty Documentation Index

This index provides an overview of all documentation moved from the project root during cleanup.

## 📁 Documentation Structure

### 🏗️ Architecture & Development
- [`architecture/`](./architecture/) - System architecture and design documents
  - `api-frontend-alignment.md` - API and frontend integration guide
  - `model_analysis.md` - Data model analysis and design
  - `target_model_state.md` - Target state for data models
  - `health_checks_proposal.md` - Health monitoring system design

### 🧹 Cleanup & Project Structure  
- [`cleanup/`](./cleanup/) - Project cleanup and organization documentation
  - `CLEANUP_IMPLEMENTATION_GUIDE.md` - Step-by-step cleanup guide
  - `folder-structure-cleanup-prd.md` - Folder structure cleanup PRD
  - `project_structure_prd.md` - Project structure requirements

### 🚀 Production & Deployment
- [`production/`](./production/) - Production deployment and operations
  - `production_ready.md` - Complete production readiness plan
  - `prd.md` - Production requirements document
  - `cachet-prd.md` - Cachet dashboard implementation
  - `DOCKER.md` - Docker deployment guide
  - `MIGRATION.md` - Database migration procedures

### 🔬 AI Analysis & Automation
- [`ai-analysis/`](./ai-analysis/) - AI-assisted analysis and cleanup reports
  - `ai-references-cleanup-plan.md` - AI references cleanup strategy
  - `ai_references_report.md` - AI references analysis report
  - `ai_scanner_README.md` - AI scanner tool documentation
  - `AI_SCANNER_README.md` - Additional AI scanner documentation

### 🛠️ Development & Testing
- [`development/`](./development/) - Development tools and troubleshooting
  - `TEST_TROUBLESHOOTING.md` - Testing issues and solutions
  - `DASHBOARD.md` - Dashboard development guide

## 📋 Root Documentation Files

The following core documentation remains in the project root:
- `README.md` - Main project overview and setup
- `CHANGELOG.md` - Version history and changes
- `ROADMAP.md` - Project roadmap and future plans
- `SUMMARY.md` - Project summary and status

## 🔄 Cleanup Notes

These files were moved during the root folder cleanup process to improve project organization.
All documentation is still accessible and properly categorized by purpose and audience.
