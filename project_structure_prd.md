# TurdParty Project Structure PRD

## Overview

This Product Requirements Document (PRD) provides a comprehensive review of the TurdParty repository structure, defining the purpose, content, and rules for each directory. This document serves as a guide for developers to understand the organization of the codebase and maintain consistency across the project.

We will start with the following general principles:
1. Move all deleted files to .archive - add a comment at the top of the file that highlights its original folder location - and why it was moved
2. Let's start with general cruft files (test outcomes etc), unnecessary MD files
3. Let's move folder by folder - create a list in the project_structure_PRD.md and then we systematically archive
4. After every folder, we will rebuild all services, and execute the testsuite

## Project Structure Principles

1. **Separation of Concerns**: Each directory should have a clear, single responsibility
2. **Consistency**: Similar components should follow the same patterns and conventions
3. **Discoverability**: File and directory names should clearly indicate their purpose
4. **Testability**: Code should be organized to facilitate comprehensive testing
5. **Documentation**: Each major component should be well-documented

## Core Directories

### 1. `/api`

**Purpose**: Contains the FastAPI application that serves as the backend for the TurdParty system.

**Content**:
- API route definitions
- Request/response models
- Endpoint handlers
- API-specific middleware

**Rules**:
- All API endpoints should be organized by feature/domain
- Each endpoint should have corresponding test coverage
- API versioning should be explicit in the URL structure
- Authentication middleware should be consistently applied
- All endpoints should be documented with OpenAPI annotations

### 2. `/db`

**Purpose**: Contains database models, migrations, and database-related utilities.

**Content**:
- SQLAlchemy models
- Database connection utilities
- Migration scripts
- Database initialization code

**Rules**:
- All database models should be defined using SQLAlchemy ORM
- Models should include appropriate relationships and constraints
- Migrations should be managed through Alembic
- Database operations should be encapsulated in repository classes
- Direct SQL should be avoided in favor of ORM when possible

### 3. `/frontend`

**Purpose**: Contains the React-based frontend application.

**Content**:
- React components
- State management
- API client code
- UI assets and styles

**Rules**:
- Components should follow a consistent structure
- State management should use a consistent pattern
- API calls should use the service connector pattern
- Styles should use a consistent approach (CSS modules, styled-components, etc.)
- Components should be organized by feature/domain

### 4. `/tests`

**Purpose**: Contains test suites for the application.

**Content**:
- Unit tests
- Integration tests
- End-to-end tests
- Test fixtures and utilities

**Rules**:
- Tests should be organized to mirror the structure of the code they test
- Each test file should focus on a specific component or feature
- Test coverage should be maintained at a high level
- Tests should be independent and not rely on external state
- Test utilities should be shared and reused

### 5. `/utils`

**Purpose**: Contains utility functions and helpers used across the application.

**Content**:
- Shared utility functions
- Helper classes
- Common constants
- Reusable code

**Rules**:
- Utilities should be focused and have a single responsibility
- Utilities should be well-documented with clear examples
- Utilities should be thoroughly tested
- Avoid duplicating functionality available in standard libraries

### 6. `/docs`

**Purpose**: Contains project documentation.

**Content**:
- API documentation
- Architecture diagrams
- Setup and installation guides
- User guides
- Development guidelines

**Rules**:
- Documentation should be kept up-to-date with code changes
- Documentation should be written in Markdown for consistency
- API documentation should be generated from code annotations
- Diagrams should be stored in both source and rendered formats
- Documentation should be organized by topic

### 7. `/.dockerwrapper`

**Purpose**: Contains Docker configuration and scripts for containerized development and deployment.

**Content**:
- Docker Compose files
- Dockerfiles
- Container initialization scripts
- Docker-related utilities

**Rules**:
- All container names should use the 'turdparty_' prefix
- Port mappings should be centralized in a JSON configuration
- Container configurations should be environment-agnostic
- Scripts should provide consistent interfaces for common operations
- Documentation should clearly explain the Docker setup
- All services should implement health checks following the pattern at dev.to/jjoc007/mastering-docker-defining-health-checks-in-docker-compose-4l5k
- Services should depend on the health status of their dependencies using `condition: service_healthy`

### 8. `/scripts`

**Purpose**: Contains utility scripts for development, testing, and deployment.

**Content**:
- Build scripts
- Test runners
- Deployment scripts
- Development utilities

**Rules**:
- Scripts should be executable and have appropriate permissions
- Scripts should include usage documentation in comments
- Scripts should handle errors gracefully
- Scripts should be cross-platform when possible
- Scripts should use consistent naming conventions

### 9. `/tasks`

**Purpose**: Contains Celery task definitions and related code.

**Content**:
- Celery task definitions
- Task utilities
- Task scheduling configuration

**Rules**:
- Tasks should be organized by domain/feature
- Tasks should include appropriate error handling
- Long-running tasks should be designed to be resumable
- Tasks should be documented with clear input/output specifications
- Task status should be trackable

### 10. `/config`

**Purpose**: Contains configuration files and settings.

**Content**:
- Application configuration
- Environment-specific settings
- Feature flags
- Configuration schemas

**Rules**:
- Sensitive information should not be committed to the repository
- Configuration should be environment-aware
- Default configurations should be provided for development
- Configuration should be validated at startup
- Configuration should be documented

## Supporting Directories

### 1. `/static`

**Purpose**: Contains static assets served by the application.

**Content**:
- Images
- CSS files
- JavaScript files
- Fonts
- Other static resources

**Rules**:
- Assets should be organized by type
- Assets should be optimized for production
- Assets should include appropriate metadata (e.g., licensing)
- Assets should use consistent naming conventions
- Third-party assets should be clearly identified

### 2. `/migrations`

**Purpose**: Contains database migration scripts.

**Content**:
- Alembic migration scripts
- Migration utilities
- Database schema versions

**Rules**:
- Migrations should be reversible when possible
- Migrations should be tested before being applied to production
- Migrations should include descriptive comments
- Migrations should be applied in order
- Migration history should be maintained

### 3. `/ui`

**Purpose**: Contains UI components and utilities.

**Content**:
- Shared UI components
- UI utilities
- UI testing utilities

**Rules**:
- Components should follow a consistent design system
- Components should be responsive and accessible
- Components should be thoroughly tested
- Components should be documented with examples
- Components should be reusable across the application

### 4. `/.ai`

**Purpose**: Contains AI-assisted development documentation and resources.

**Content**:
- AI tool configurations
- Project context documentation
- Development workflows
- Code patterns and cheatsheets

**Rules**:
- Documentation should follow the established structure
- AI configurations should be kept up-to-date
- Documentation should provide clear context for AI assistants
- Documentation should be organized by development phase
- Documentation should be maintained alongside code changes

## Test-Related Directories

### 1. `/test-files`

**Purpose**: Contains files used for testing file upload and processing.

**Content**:
- Sample files of various types
- Test data
- File fixtures

**Rules**:
- Test files should be small in size
- Test files should cover various formats and edge cases
- Test files should include appropriate metadata
- Test files should be organized by type/purpose
- Test files should not contain sensitive information

### 2. `/test-vm`

**Purpose**: Contains VM-related test resources.

**Content**:
- VM templates for testing
- VM configuration files
- VM test scripts

**Rules**:
- VM templates should be minimal and fast to provision
- VM configurations should be consistent with production
- VM test scripts should be idempotent
- VM resources should be cleaned up after tests
- VM tests should be isolated from each other

## Development Guidelines

1. **Naming Conventions**:
   - Use descriptive, consistent naming
   - Follow language-specific conventions (PEP 8 for Python, etc.)
   - Use lowercase with underscores for files and directories
   - Use camelCase for JavaScript variables and functions
   - Use PascalCase for React components and classes

2. **Code Organization**:
   - Group related functionality together
   - Limit file size to maintain readability
   - Use appropriate design patterns
   - Maintain separation of concerns
   - Follow the principle of least surprise

3. **Documentation**:
   - Document public APIs and interfaces
   - Include examples in documentation
   - Keep documentation up-to-date with code changes
   - Use consistent documentation formats
   - Document non-obvious behavior and edge cases

4. **Testing**:
   - Write tests for all new features
   - Maintain high test coverage
   - Test edge cases and error conditions
   - Use appropriate test types (unit, integration, e2e)
   - Keep tests fast and reliable

5. **Version Control**:
   - Use descriptive commit messages
   - Keep commits focused and atomic
   - Use feature branches for development
   - Review code before merging
   - Maintain a clean commit history

## Cleanup Progress Tracking

This section tracks our progress in cleaning up the repository structure folder by folder.

| Folder | Status | Date | Notes |
|--------|--------|------|-------|
| / (root) | **Completed** | 2025-06-04 | AI references cleanup completed - archived 20 redundant markdown files, cleaned CHANGELOG.md |
| /.ai | Pending | | |
| /.dockerwrapper | Completed | 2025-05-15 | Removed unused Docker files and empty scripts, added health checks to all services |
| /api | Pending | | |
| /db | Pending | | |
| /frontend | Pending | | |
| /scripts | Pending | | |
| /static | Pending | | |
| /tasks | Pending | | |
| /tests | Pending | | |
| /utils | Pending | | |

## AI References Cleanup Plan

### 🔍 Scan Results (2025-06-04)
- **Total files scanned**: 1,847
- **Files with AI references**: 67
- **Total references found**: 1,097
- **Categories**: Cursor (794), Claude (163), LLM (140)

### 📋 Cleanup Categories

#### 1. **AI Tool Configuration Files** - ⚠️ KEEP (Functional)
- `.cursor/rules/*.mdc` - Cursor IDE configuration files
- `.focus_forge/.cursor/rules/*.mdc` - Focus Forge Cursor integration
- **Action**: Keep - these are functional development tools

#### 2. **AI Scanner Tools** - ✅ KEEP (Utility)
- `scan_ai_references.py` - AI reference scanner script
- `AI_SCANNER_README.md` / `ai_scanner_README.md` - Documentation
- `ai_references_report.md` - Generated report
- **Action**: Keep - useful for ongoing cleanup

#### 3. **Documentation with AI References** - 🧹 CLEAN
- `CHANGELOG.md` - Contains `.claude/` path references
- Various docs mentioning AI tools in examples
- **Action**: Replace specific AI tool names with generic terms

#### 4. **Output/Generated Files** - 🗑️ ARCHIVE
- `*_response.html` files in `_archive_` (already archived)
- Test output files with AI-generated content
- **Action**: Already in archive, verify cleanup

#### 5. **Residual Markdown Files** - ✅ COMPLETED
**Files successfully archived** (moved to `_archive_/markdown-cleanup-2025-06-04/`):

**Upload Documentation** (6 files archived):
- `upload-implementation-todos.md` - TODO list for upload feature
- `upload-status-diagram.md` - Status diagram for uploads
- `upload-testing-findings.md` - Testing findings and results
- `upload-test-summary.md` - Summary of upload tests
- `file-upload-e2e-status.md` - E2E testing status report
- `file-upload-testing.md` - Upload testing documentation

**Implementation Status** (6 files archived):
- `cachet-implementation-complete.md` - Cachet completion notice
- `cachet-implementation-summary.md` - Cachet implementation summary
- `INTEGRATION-TEST-SUMMARY.md` - Integration test summary
- `cleanup-phase-checklists.md` - Cleanup checklists
- `refactor_friday.md` - Friday refactoring notes
- `remediation-plan.md` - System remediation plan

**Test Logs** (4 files archived):
- `test_plan.md` - Test plan documentation
- `test_results.md` - Test results summary
- `streamlit_to_flask_conversion_20250407_115225.md` - Conversion log
- `streamlit_to_flask_conversion_20250407_115358.md` - Conversion log

**Files preserved** (valuable technical content):
- `target_model_state.md` - Technical specification for database models
- `model_analysis.md` - Comprehensive database model analysis

### 🎯 Cleanup Results Summary

**Total files archived**: 20 markdown files
**Archive location**: `_archive_/markdown-cleanup-2025-06-04/`
**AI references cleaned**: 3 references in CHANGELOG.md
**Repository improvement**: Cleaner root directory with better organization

## Conclusion

This PRD provides a comprehensive overview of the TurdParty project structure. By following these guidelines, developers can maintain a consistent, organized codebase that is easy to navigate, understand, and extend. Regular reviews and updates to this document will ensure it remains relevant as the project evolves.

## Action Log

This section logs all actions taken during the cleanup process, providing a detailed history of changes made to the repository structure.

| Date | Action | Files/Folders Affected | Description | Performed By | Tests Run | Test Results |
|------|--------|------------------------|-------------|--------------|-----------|--------------|
| 2025-05-15 | Initial PRD creation | project_structure_prd.md | Created comprehensive PRD for project structure | AI Assistant | N/A | N/A |
| 2025-05-15 | AI scanner creation | scan_ai_references.py, AI_SCANNER_README.md | Created scanner to identify AI tool references | AI Assistant | N/A | N/A |
| 2025-05-15 | Created .archive directory | .archive/ | Added directory for storing removed files | AI Assistant | N/A | N/A |
| 2025-05-15 | Cleaned up .dockerwrapper | .dockerwrapper/, .archive/.dockerwrapper/ | Archived and removed unused Docker files and empty scripts | AI Assistant | Docker rebuild | Success |
| 2025-05-15 | Added Docker health checks | .dockerwrapper/docker-compose.yml, health_checks_proposal.md | Implemented health checks for all services in Docker Compose | AI Assistant | N/A | N/A |
| 2025-05-15 | Added service dashboard | .dockerwrapper/Dockerfile.service-dashboard, .dockerwrapper/docker-compose.service-dashboard.yml | Created a simple PHP-based service dashboard with Docker integration for monitoring TurdParty services | AI Assistant | Service dashboard health check | Success |
