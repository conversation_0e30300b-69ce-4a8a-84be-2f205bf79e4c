services:
  postgres_cachet:
    image: postgres:12-alpine
    container_name: turdparty_postgres_cachet
    volumes:
      - postgres_cachet_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=cachet
    networks:
      turdparty_network:
        aliases:
          - postgres_cachet
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  cachet:
    container_name: turdparty_cachet
    image: cachethq/docker:latest
    ports:
      - "3501:8000"
    environment:
      - DB_DRIVER=pgsql
      - DB_HOST=postgres_cachet
      - DB_PORT=5432
      - DB_DATABASE=cachet
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
      - DB_PREFIX=chq_
      - APP_KEY=base64:WPrXhBQdrXPQE/YTWr+rPT9r/j8ZaSKLOh0oD0mYjTU=
      - APP_URL=http://localhost:3501
      - APP_ENV=production
      - CACHE_DRIVER=file
      - SESSION_DRIVER=file
      - QUEUE_DRIVER=sync
      - MAIL_DRIVER=log
      - MAIL_HOST=smtp.mailtrap.io
      - MAIL_PORT=2525
      - MAIL_USERNAME=null
      - MAIL_PASSWORD=null
      - MAIL_ADDRESS=null
      - MAIL_NAME=null
      - MAIL_ENCRYPTION=tls
      - CACHET_BEACON=false
      - CACHET_EMOJI=true
      - CACHET_AUTO_TWITTER=false
    volumes:
      - cachet_data:/var/www/html/storage
    networks:
      turdparty_network:
        aliases:
          - cachet
    depends_on:
      postgres_cachet:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  cachet_data:
  postgres_cachet_data:

networks:
  turdparty_network:
    external: true
    name: turdparty_turdparty_network
