#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up Cachet Dashboard...${NC}"

# Stop any existing containers
echo -e "${YELLOW}Stopping any existing Cachet containers...${NC}"
docker compose -f .dockerwrapper/docker-compose.cachet.yml down

# Start Cachet
echo -e "${YELLOW}Starting TurdParty Cachet Dashboard...${NC}"
docker compose -f .dockerwrapper/docker-compose.cachet.yml up -d

echo -e "${GREEN}Cachet Dashboard is starting at http://localhost:3501${NC}"
echo -e "${GREEN}It may take a minute for the service to be fully available.${NC}"
echo ""

# Run automatic configuration
echo -e "${YELLOW}Running automatic configuration...${NC}"
./.dockerwrapper/auto-configure-cachet.sh

# Generate a random API token for the update script
echo -e "${YELLOW}Generating API token for the update script...${NC}"
API_TOKEN=$(openssl rand -hex 32)
echo -e "${GREEN}API token generated: $API_TOKEN${NC}"

# Update the update-cachet-status.sh script with the API token
sed -i "s|CACHET_API_TOKEN=\"\"|CACHET_API_TOKEN=\"$API_TOKEN\"|g" .dockerwrapper/update-cachet-status.sh
echo -e "${GREEN}Updated update-cachet-status.sh with API token${NC}"

echo -e "${YELLOW}Note: You will need to create an API token in the Cachet dashboard with this value:${NC}"
echo -e "${GREEN}$API_TOKEN${NC}"
echo -e "${YELLOW}Go to Dashboard > Settings > API Tokens and create a new token with this value.${NC}"
