#!/bin/bash

# Script to update Cachet component statuses based on service health checks
# This script should be run periodically via cron

# Configuration
CACHET_API_URL="http://localhost:3501/api/v1"
CACHET_API_TOKEN="bdd073f7810def2fdf751702426a8edae6325ce2153338fb4d86b2c4702a0533" # Set this after creating an API token in Cachet

# Component IDs - These are set based on the order of creation in entrypoint.sh
API_COMPONENT_ID="1"
FRONTEND_COMPONENT_ID="2"
REDIS_COMPONENT_ID="3"
CELERY_DEFAULT_COMPONENT_ID="4"
CELERY_FILE_OPS_COMPONENT_ID="5"
CELERY_VM_OPS_COMPONENT_ID="6"
CELERY_FLOWER_COMPONENT_ID="7"
POSTGRES_COMPONENT_ID="8"
MINIO_COMPONENT_ID="9"

# Status codes
# 1 = Operational
# 2 = Performance Issues
# 3 = Partial Outage
# 4 = Major Outage

# Function to update component status
update_component_status() {
    local component_id=$1
    local status=$2

    if [ -z "$CACHET_API_TOKEN" ]; then
        echo "Error: CACHET_API_TOKEN not set. Please create an API token in Cachet and update this script."
        return 1
    fi

    if [ -z "$component_id" ]; then
        echo "Error: Component ID not set. Please update the component IDs in this script."
        return 1
    fi

    echo "Updating component $component_id to status $status"

    curl -X PUT \
        -H "Content-Type: application/json" \
        -H "X-Cachet-Token: $CACHET_API_TOKEN" \
        -d "{\"status\": $status}" \
        "$CACHET_API_URL/components/$component_id"

    echo ""
}

# Check API status
check_api() {
    if curl -s "http://turdparty_api:8000/api/v1/health" | grep -q "status"; then
        update_component_status "$API_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$API_COMPONENT_ID" 4 # Major Outage
    fi
}

# Check PostgreSQL status
check_postgres() {
    if docker exec turdparty_postgres pg_isready -U postgres > /dev/null 2>&1; then
        update_component_status "$POSTGRES_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$POSTGRES_COMPONENT_ID" 4 # Major Outage
    fi
}

# Check Redis status
check_redis() {
    if docker exec turdparty_redis redis-cli ping | grep -q "PONG"; then
        update_component_status "$REDIS_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$REDIS_COMPONENT_ID" 4 # Major Outage
    fi
}

# Check MinIO status
check_minio() {
    if curl -s "http://turdparty_minio:9000/minio/health/live" | grep -q "ok"; then
        update_component_status "$MINIO_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$MINIO_COMPONENT_ID" 4 # Major Outage
    fi
}

# Check Celery workers
check_celery_workers() {
    # This requires more complex logic to check Celery workers
    # For now, we'll just check if the containers are running

    if docker ps | grep -q "turdparty_celery_default"; then
        update_component_status "$CELERY_DEFAULT_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$CELERY_DEFAULT_COMPONENT_ID" 4 # Major Outage
    fi

    if docker ps | grep -q "turdparty_celery_file_ops"; then
        update_component_status "$CELERY_FILE_OPS_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$CELERY_FILE_OPS_COMPONENT_ID" 4 # Major Outage
    fi

    if docker ps | grep -q "turdparty_celery_vm_ops"; then
        update_component_status "$CELERY_VM_OPS_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$CELERY_VM_OPS_COMPONENT_ID" 4 # Major Outage
    fi

    if docker ps | grep -q "turdparty_celery_flower"; then
        update_component_status "$CELERY_FLOWER_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$CELERY_FLOWER_COMPONENT_ID" 4 # Major Outage
    fi
}

# Check Frontend status
check_frontend() {
    if curl -s "http://turdparty_frontend:80" > /dev/null 2>&1; then
        update_component_status "$FRONTEND_COMPONENT_ID" 1 # Operational
    else
        update_component_status "$FRONTEND_COMPONENT_ID" 4 # Major Outage
    fi
}

# Main function
main() {
    echo "Starting Cachet status update at $(date)"

    # Check if Cachet API token is set
    if [ -z "$CACHET_API_TOKEN" ]; then
        echo "Warning: CACHET_API_TOKEN not set. Please create an API token in Cachet and update this script."
        echo "Skipping status updates."
        exit 1
    fi

    # Run all checks
    check_api
    check_postgres
    check_redis
    check_minio
    check_celery_workers
    check_frontend

    echo "Completed Cachet status update at $(date)"
}

# Run the main function
main
