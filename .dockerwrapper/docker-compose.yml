services:
  api:
    container_name: turdparty_api
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.fixed
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-api
    ports:
      - "3050:8000"
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=************************************************************************************/app
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:turdparty_service_dashboard_secure_password_2025@postgres:5432/app
      - PORT=8000
      - TEST_MODE=true
      - PYTHONPATH=/app
      - DEBUG=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - VAGRANT_TEST_MODE=1
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ./scripts/container-init.sh:/usr/local/bin/container-init.sh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    entrypoint: ["/usr/local/bin/container-init.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    cap_add:
      - NET_ADMIN
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  react-app:
    container_name: turdparty_frontend
    image: nginx:alpine
    ports:
      - "3100:80"
    depends_on:
      api:
        condition: service_healthy
    volumes:
      - ../turdparty-app/build:/usr/share/nginx/html
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:80"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s

  dashboard:
    container_name: turdparty_dashboard
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.dashboard
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-dashboard
    ports:
      - "3150:8080"
    volumes:
      - ..:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/app
      - CONTAINER_PREFIX=turdparty
    command: ["python", "/app/dashboard.py", "start"]
    user: root
    networks:
      - turdparty_network
    restart: "no"
    healthcheck:
      test: ["CMD", "ps", "aux", "|", "grep", "[p]ython /app/dashboard.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  postgres:
    container_name: turdparty_postgres
    image: postgres:14-alpine
    ports:
      - "3200:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=turdparty_service_dashboard_secure_password_2025
      - POSTGRES_DB=app
      - POSTGRES_MULTIPLE_DATABASES=app,cachet
    volumes:
      - turdparty_postgres_data:/var/lib/postgresql/data
      - ./scripts/service-dashboard/create-multiple-postgresql-databases.sh:/docker-entrypoint-initdb.d/create-multiple-postgresql-databases.sh
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  minio:
    container_name: turdparty_minio
    image: minio/minio:latest
    ports:
      - "3300:9000"
      - "3301:9001"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - turdparty_network
    restart: unless-stopped

  minio-ssh:
    container_name: turdparty_minio_ssh
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.minio
    ports:
      - "2223:22"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    networks:
      - turdparty_network
    restart: unless-stopped
    depends_on:
      minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "pgrep", "sshd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  redis:
    container_name: turdparty_redis
    image: redis:7-alpine
    ports:
      - "3400:6379"
    volumes:
      - turdparty_redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - turdparty_network
    restart: unless-stopped

  celery-worker-default:
    container_name: turdparty_celery_default
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_healthy
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_QUEUE=default
      - CELERY_CONCURRENCY=2
      - CELERY_LOG_LEVEL=info
      - WAIT_FOR_API=false
    volumes:
      - ..:/app
      - ../logs:/app/logs
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "default", "-c", "2"]
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker-file-ops:
    container_name: turdparty_celery_file_ops
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_healthy
      minio:
        condition: service_healthy
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_QUEUE=file_ops
      - CELERY_CONCURRENCY=2
      - CELERY_LOG_LEVEL=info
      - WAIT_FOR_API=false
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "file_ops", "-c", "2"]
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker-vm-ops:
    container_name: turdparty_celery_vm_ops
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_healthy
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_QUEUE=vm_ops
      - CELERY_CONCURRENCY=2
      - CELERY_LOG_LEVEL=info
      - WAIT_FOR_API=false
      - VAGRANT_TEST_MODE=1
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "vm_ops", "-c", "2"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-flower:
    container_name: turdparty_celery_flower
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      celery-worker-default:
        condition: service_healthy
    ports:
      - "3450:5555"
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_UNAUTHENTICATED_API=true
    volumes:
      - ..:/app
    entrypoint: ["/usr/local/bin/celery-flower-entrypoint.sh"]
    command: ["celery", "-A", "api.celery_app", "flower", "--port=5555", "--address=0.0.0.0"]
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/api/workers"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  turdparty_postgres_data:
  turdparty_ssh_keys:
  turdparty_frontend_node_modules:
  turdparty_minio_data:
  turdparty_redis_data:

networks:
  turdparty_network:
    driver: bridge