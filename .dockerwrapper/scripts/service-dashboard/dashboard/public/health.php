<?php
/**
 * Health check endpoint for the TurdParty Service Dashboard
 * 
 * This script checks:
 * 1. PHP is functioning correctly
 * 2. Docker socket is accessible
 * 3. The dashboard can connect to other services
 */

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response
$response = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => [
        'php' => [
            'status' => 'pass',
            'message' => 'PHP is functioning correctly'
        ],
        'docker' => [
            'status' => 'unknown',
            'message' => 'Docker socket check not performed yet'
        ],
        'services' => [
            'status' => 'unknown',
            'message' => 'Service check not performed yet'
        ]
    ]
];

// Check Docker socket
try {
    $dockerSocket = '/var/run/docker.sock';
    if (file_exists($dockerSocket)) {
        $socket = fsockopen('unix://' . $dockerSocket, 0, $errno, $errstr, 5);
        if ($socket) {
            fclose($socket);
            $response['checks']['docker'] = [
                'status' => 'pass',
                'message' => 'Docker socket is accessible'
            ];
        } else {
            $response['checks']['docker'] = [
                'status' => 'fail',
                'message' => "Cannot connect to Docker socket: $errstr ($errno)"
            ];
            $response['status'] = 'degraded';
        }
    } else {
        $response['checks']['docker'] = [
            'status' => 'fail',
            'message' => 'Docker socket file does not exist'
        ];
        $response['status'] = 'degraded';
    }
} catch (Exception $e) {
    $response['checks']['docker'] = [
        'status' => 'fail',
        'message' => 'Docker socket check failed: ' . $e->getMessage()
    ];
    $response['status'] = 'degraded';
}

// Check if we can connect to at least one service
try {
    $serviceChecks = [];
    $services = [
        'api' => 'http://turdparty_api:8000/api/v1/health',
        'postgres' => 'turdparty_postgres:5432',
        'redis' => 'turdparty_redis:6379'
    ];
    
    $anyServiceUp = false;
    
    foreach ($services as $name => $endpoint) {
        if (strpos($endpoint, 'http') === 0) {
            // HTTP service
            $ch = curl_init($endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 2);
            curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode >= 200 && $httpCode < 400) {
                $serviceChecks[$name] = [
                    'status' => 'pass',
                    'message' => "Connected to $name service"
                ];
                $anyServiceUp = true;
            } else {
                $serviceChecks[$name] = [
                    'status' => 'fail',
                    'message' => "Failed to connect to $name service (HTTP $httpCode)"
                ];
            }
        } else {
            // TCP service
            list($host, $port) = explode(':', $endpoint);
            $socket = @fsockopen($host, $port, $errno, $errstr, 2);
            if ($socket) {
                fclose($socket);
                $serviceChecks[$name] = [
                    'status' => 'pass',
                    'message' => "Connected to $name service"
                ];
                $anyServiceUp = true;
            } else {
                $serviceChecks[$name] = [
                    'status' => 'fail',
                    'message' => "Failed to connect to $name service: $errstr ($errno)"
                ];
            }
        }
    }
    
    if ($anyServiceUp) {
        $response['checks']['services'] = [
            'status' => 'pass',
            'message' => 'Connected to at least one service',
            'details' => $serviceChecks
        ];
    } else {
        $response['checks']['services'] = [
            'status' => 'fail',
            'message' => 'Failed to connect to any service',
            'details' => $serviceChecks
        ];
        $response['status'] = 'degraded';
    }
} catch (Exception $e) {
    $response['checks']['services'] = [
        'status' => 'fail',
        'message' => 'Service check failed: ' . $e->getMessage()
    ];
    $response['status'] = 'degraded';
}

// If any check failed, set overall status to degraded
foreach ($response['checks'] as $check) {
    if ($check['status'] === 'fail') {
        $response['status'] = 'degraded';
        break;
    }
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
