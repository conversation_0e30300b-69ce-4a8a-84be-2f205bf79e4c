<?php
/**
 * TurdParty Service Dashboard
 *
 * A simple dashboard to monitor the status of TurdParty services
 */

// Configuration
$config = [
    'service_groups' => [
        'interfaces' => [
            'name' => 'Interfaces',
            'description' => 'User-facing services',
            'services' => [
                [
                    'name' => 'API',
                    'container' => 'turdparty_api',
                    'url' => 'http://turdparty_api:8000/api/v1/health',
                    'port' => 8000,
                    'description' => 'Main API service for TurdParty'
                ],
                [
                    'name' => 'Frontend',
                    'container' => 'turdparty_frontend',
                    'url' => 'http://turdparty_frontend:80',
                    'port' => 80,
                    'description' => 'Frontend web interface'
                ]
            ]
        ],
        'backend' => [
            'name' => 'Backend',
            'description' => 'Data storage services',
            'services' => [
                [
                    'name' => 'PostgreSQL',
                    'container' => 'turdparty_postgres',
                    'port' => 5432,
                    'description' => 'Database service'
                ],
                [
                    'name' => 'MinIO',
                    'container' => 'turdparty_minio',
                    'url' => 'http://turdparty_minio:9000/minio/health/live',
                    'port' => 9000,
                    'description' => 'Object storage service'
                ]
            ]
        ],
        'worker_queues' => [
            'name' => 'Worker Queues',
            'description' => 'Task processing services',
            'services' => [
                [
                    'name' => 'Redis',
                    'container' => 'turdparty_redis',
                    'port' => 6379,
                    'description' => 'Cache and message broker'
                ],
                [
                    'name' => 'Celery Default',
                    'container' => 'turdparty_celery_default',
                    'description' => 'Default task worker'
                ],
                [
                    'name' => 'Celery File Ops',
                    'container' => 'turdparty_celery_file_ops',
                    'description' => 'File operations task worker'
                ],
                [
                    'name' => 'Celery VM Ops',
                    'container' => 'turdparty_celery_vm_ops',
                    'description' => 'VM operations task worker'
                ],
                [
                    'name' => 'Celery Flower',
                    'container' => 'turdparty_celery_flower',
                    'url' => 'http://turdparty_celery_flower:5555',
                    'port' => 5555,
                    'description' => 'Celery monitoring tool'
                ]
            ]
        ]
    ]
];

/**
 * Check if a container is running
 *
 * @param string $containerName The name of the container
 * @return bool True if the container is running, false otherwise
 */
function isContainerRunning($containerName) {
    $command = "docker inspect -f '{{.State.Running}}' $containerName 2>/dev/null";
    $output = shell_exec($command);
    return trim($output) === 'true';
}

/**
 * Check if a service is responding
 *
 * @param array $service The service configuration
 * @return bool True if the service is responding, false otherwise
 */
function isServiceResponding($service) {
    if (!isset($service['url'])) {
        return isContainerRunning($service['container']);
    }

    $ch = curl_init($service['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $httpCode >= 200 && $httpCode < 400;
}

/**
 * Get the status of all services
 *
 * @param array $services The services configuration
 * @return array The status of all services
 */
function getServicesStatus($services) {
    $result = [];

    foreach ($services as $service) {
        $isRunning = isContainerRunning($service['container']);
        $isResponding = $isRunning ? isServiceResponding($service) : false;

        $result[] = [
            'name' => $service['name'],
            'container' => $service['container'],
            'description' => $service['description'],
            'isRunning' => $isRunning,
            'isResponding' => $isResponding,
            'status' => $isResponding ? 'Operational' : ($isRunning ? 'Degraded' : 'Down')
        ];
    }

    return $result;
}

// Get the status of all services by group
$serviceGroups = [];
$allServices = [];

foreach ($config['service_groups'] as $groupId => $group) {
    $groupServices = getServicesStatus($group['services']);
    $serviceGroups[$groupId] = [
        'name' => $group['name'],
        'description' => $group['description'],
        'services' => $groupServices
    ];

    // Add all services to a flat array for overall status calculation
    $allServices = array_merge($allServices, $groupServices);
}

// Calculate overall system status
$operationalCount = 0;
foreach ($allServices as $service) {
    if ($service['status'] === 'Operational') {
        $operationalCount++;
    }
}
$overallStatus = $operationalCount === count($allServices) ? 'Operational' : 'Degraded';
if ($operationalCount === 0) {
    $overallStatus = 'Major Outage';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty Service Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .status-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 18px;
        }
        .status-operational {
            background-color: #d4edda;
            color: #155724;
        }
        .status-degraded {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-outage {
            background-color: #f8d7da;
            color: #721c24;
        }
        .service-group {
            margin-bottom: 40px;
        }
        .group-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .group-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .group-description {
            color: #6c757d;
            font-size: 16px;
        }
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .service-name {
            font-weight: bold;
            font-size: 18px;
        }
        .service-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .status-operational {
            background-color: #d4edda;
            color: #155724;
        }
        .status-degraded {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-down {
            background-color: #f8d7da;
            color: #721c24;
        }
        .service-description {
            color: #6c757d;
            margin-bottom: 10px;
        }
        .service-details {
            font-size: 14px;
            color: #495057;
        }
        .refresh-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-button:hover {
            background-color: #0069d9;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 14px;
        }
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #121212;
                color: #e0e0e0;
            }
            .container {
                background-color: #1e1e1e;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            }
            h1 {
                color: #e0e0e0;
            }
            .group-title {
                color: #e0e0e0;
            }
            .group-header {
                border-bottom-color: #444;
            }
            .service-card {
                background-color: #2d2d2d;
                border-color: #444;
            }
            .service-description, .group-description {
                color: #adb5bd;
            }
            .service-details {
                color: #ced4da;
            }
            .refresh-button {
                background-color: #0d6efd;
            }
            .refresh-button:hover {
                background-color: #0b5ed7;
            }
            .footer {
                color: #adb5bd;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💩 TurdParty Service Dashboard 🎉</h1>

        <div class="status-indicator <?php echo strtolower('status-' . str_replace(' ', '-', $overallStatus)); ?>">
            System Status: <?php echo $overallStatus; ?>
        </div>

        <?php foreach ($serviceGroups as $groupId => $group): ?>
            <div class="service-group">
                <div class="group-header">
                    <div class="group-title">
                        <?php
                        // Add appropriate emoji for each group
                        $emoji = '';
                        switch ($groupId) {
                            case 'interfaces':
                                $emoji = '🖥️';
                                break;
                            case 'backend':
                                $emoji = '💾';
                                break;
                            case 'worker_queues':
                                $emoji = '⚙️';
                                break;
                        }
                        echo $emoji . ' ' . htmlspecialchars($group['name']);
                        ?>
                    </div>
                    <div class="group-description"><?php echo htmlspecialchars($group['description']); ?></div>
                </div>

                <div class="services-grid">
                    <?php foreach ($group['services'] as $service): ?>
                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-name"><?php echo htmlspecialchars($service['name']); ?></div>
                                <div class="service-status status-<?php echo strtolower($service['status']); ?>">
                                    <?php echo $service['status']; ?>
                                </div>
                            </div>
                            <div class="service-description"><?php echo htmlspecialchars($service['description']); ?></div>
                            <div class="service-details">
                                Container: <?php echo htmlspecialchars($service['container']); ?><br>
                                Running: <?php echo $service['isRunning'] ? 'Yes' : 'No'; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <button class="refresh-button" onclick="window.location.reload()">Refresh Status</button>

        <div class="footer">
            <p>Last updated: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>TurdParty Service Dashboard</p>
        </div>
    </div>
</body>
</html>
