#!/bin/bash

# This script automatically configures Cachet without manual intervention

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting automated Cachet configuration...${NC}"

# Wait for <PERSON><PERSON><PERSON> to be ready
echo -e "${YELLOW}Waiting for <PERSON>ache<PERSON> to be ready...${NC}"
ATTEMPTS=0
MAX_ATTEMPTS=30
until curl -s -o /dev/null -w "%{http_code}" http://localhost:3501/setup | grep -q "200" || [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; do
    ATTEMPTS=$((ATTEMPTS + 1))
    echo -e "${YELLOW}Waiting for Cachet setup page... attempt $ATTEMPTS of $MAX_ATTEMPTS${NC}"
    sleep 5
done

if [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; then
    echo -e "${RED}Cachet setup page not available after $MAX_ATTEMPTS attempts. Exiting.${NC}"
    exit 1
fi

echo -e "${GREEN}Cachet setup page is ready!${NC}"

# Step 1: Configure database settings
echo -e "${YELLOW}Configuring database settings...${NC}"
docker exec -i turdparty_cachet php artisan config:clear
docker exec -i turdparty_cachet php artisan cache:clear

# Step 2: Run migrations
echo -e "${YELLOW}Running database migrations...${NC}"
docker exec -i turdparty_cachet php artisan migrate --force

# Step 3: Create .env file with proper settings
echo -e "${YELLOW}Creating environment configuration...${NC}"
docker exec -i turdparty_cachet bash -c 'cat > /var/www/html/.env << EOF
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:3501
APP_KEY=base64:WPrXhBQdrXPQE/YTWr+rPT9r/j8ZaSKLOh0oD0mYjTU=

DB_DRIVER=pgsql
DB_HOST=postgres_cachet
DB_DATABASE=cachet
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_PORT=5432
DB_PREFIX=chq_

CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync

MAIL_DRIVER=log
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ADDRESS=null
MAIL_NAME=null
MAIL_ENCRYPTION=tls

CACHET_BEACON=false
CACHET_EMOJI=true
CACHET_AUTO_TWITTER=false
EOF'

# Step 4: Clear and cache configuration
echo -e "${YELLOW}Caching configuration...${NC}"
docker exec -i turdparty_cachet php artisan config:cache
docker exec -i turdparty_cachet php artisan route:cache

# Step 5: Create admin user directly in database
echo -e "${YELLOW}Creating admin user...${NC}"
docker exec -i turdparty_postgres_cachet psql -U postgres -d cachet << 'EOF'
INSERT INTO chq_users (username, email, password, api_key, active, level, created_at, updated_at)
VALUES (
    'admin',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'bdd073f7810def2fdf751702426a8edae6325ce2153338fb4d86b2c4702a0533',
    true,
    1,
    NOW(),
    NOW()
);
EOF

# Step 6: Create component groups
echo -e "${YELLOW}Creating component groups...${NC}"
docker exec -i turdparty_postgres_cachet psql -U postgres -d cachet << 'EOF'
INSERT INTO chq_component_groups (name, "order", visible, collapsed, created_at, updated_at)
VALUES
    ('🖥️ Interfaces', 1, 1, 0, NOW(), NOW()),
    ('⚙️ Worker Queues', 2, 1, 0, NOW(), NOW()),
    ('💾 Backend', 3, 1, 0, NOW(), NOW());
EOF

# Step 7: Create components
echo -e "${YELLOW}Creating components...${NC}"
docker exec -i turdparty_postgres_cachet psql -U postgres -d cachet << 'EOF'
INSERT INTO chq_components (name, description, link, status, "order", group_id, enabled, created_at, updated_at)
VALUES
    ('API', 'Main API service for TurdParty', 'http://turdparty_api:8000/api/v1/health', 1, 1, 1, true, NOW(), NOW()),
    ('Frontend', 'Frontend web interface', 'http://turdparty_frontend:80', 1, 2, 1, true, NOW(), NOW()),
    ('Redis', 'Cache and message broker', '', 1, 1, 2, true, NOW(), NOW()),
    ('Celery Default', 'Default task worker', '', 1, 2, 2, true, NOW(), NOW()),
    ('Celery File Ops', 'File operations task worker', '', 1, 3, 2, true, NOW(), NOW()),
    ('Celery VM Ops', 'VM operations task worker', '', 1, 4, 2, true, NOW(), NOW()),
    ('Celery Flower', 'Celery monitoring tool', '', 1, 5, 2, true, NOW(), NOW()),
    ('PostgreSQL', 'Database', '', 1, 1, 3, true, NOW(), NOW()),
    ('MinIO', 'Object storage', 'http://turdparty_minio:9000/minio/health/live', 1, 2, 3, true, NOW(), NOW());
EOF

# Step 8: Set application settings
echo -e "${YELLOW}Setting application settings...${NC}"
docker exec -i turdparty_postgres_cachet psql -U postgres -d cachet << 'EOF'
INSERT INTO chq_settings (name, value, created_at, updated_at)
VALUES
    ('app_name', 'TurdParty Status', NOW(), NOW()),
    ('app_domain', 'localhost:3501', NOW(), NOW()),
    ('app_locale', 'en', NOW(), NOW()),
    ('app_timezone', 'UTC', NOW(), NOW()),
    ('show_support', '0', NOW(), NOW()),
    ('allow_tracking', '0', NOW(), NOW()),
    ('app_banner', '', NOW(), NOW()),
    ('app_analytics', '', NOW(), NOW()),
    ('app_stylesheet', '', NOW(), NOW()),
    ('display_graphs', '1', NOW(), NOW()),
    ('only_disrupted_days', '0', NOW(), NOW()),
    ('app_banner_style', 'info', NOW(), NOW()),
    ('app_banner_visible', '0', NOW(), NOW()),
    ('dashboard_login_icon', '1', NOW(), NOW()),
    ('subscribers', '1', NOW(), NOW()),
    ('skip_subscriber_verification', '0', NOW(), NOW()),
    ('automatic_localization', '0', NOW(), NOW()),
    ('enable_external_dependencies', '1', NOW(), NOW()),
    ('sort_incidents_by', 'created_at', NOW(), NOW()),
    ('app_incident_days', '7', NOW(), NOW()),
    ('major_outage_rate', '50', NOW(), NOW());
EOF

# Step 9: Clear cache and restart services
echo -e "${YELLOW}Clearing cache and restarting services...${NC}"
docker exec -i turdparty_cachet php artisan cache:clear
docker exec -i turdparty_cachet php artisan config:clear
docker exec -i turdparty_cachet php artisan route:clear
docker exec -i turdparty_cachet php artisan view:clear

# Step 10: Verify setup
echo -e "${YELLOW}Verifying setup...${NC}"
sleep 5

# Check if we can access the dashboard
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3501 | grep -q "200"; then
    echo -e "${GREEN}✓ Cachet dashboard is accessible${NC}"
else
    echo -e "${RED}✗ Cachet dashboard is not accessible${NC}"
fi

# Check if API is working
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3501/api/v1/ping | grep -q "200"; then
    echo -e "${GREEN}✓ Cachet API is working${NC}"
else
    echo -e "${RED}✗ Cachet API is not working${NC}"
fi

echo -e "${GREEN}Cachet configuration completed!${NC}"
echo -e "${GREEN}Dashboard URL: http://localhost:3501${NC}"
echo -e "${GREEN}Login credentials:${NC}"
echo -e "${GREEN}  Email: <EMAIL>${NC}"
echo -e "${GREEN}  Password: password${NC}"
echo -e "${GREEN}API Token: bdd073f7810def2fdf751702426a8edae6325ce2153338fb4d86b2c4702a0533${NC}"
