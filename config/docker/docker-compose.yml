version: '3'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    environment:
      - DATABASE_URL=********************************************/app
      - PYTHONPATH=/app
      - MINIO_DIRECT=true
      - MINIO_HOST=minio
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./api:/app/api
      - ./scripts:/app/scripts
    ports:
      - "3055:8000"
    depends_on:
      - postgres
      - minio
    networks:
      default:
        aliases:
          - turdparty-api-1

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5435:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      default:
        aliases:
          - turdparty-postgres-1

  minio:
    image: minio/minio:latest
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9500:9000"
      - "9501:9001"
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      default:
        aliases:
          - turdparty-minio-1

  minio-ssh:
    build:
      context: .
      dockerfile: .dockerwrapper/Dockerfile.minio
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "2225:22"
    depends_on:
      - minio
    networks:
      default:
        aliases:
          - turdparty-minio-ssh-1

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      default:
        aliases:
          - turdparty-redis-1

  celery-worker:
    build:
      context: .
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      - redis
      - api
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./:/app
      - ./logs:/app/logs
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info"]
    networks:
      default:
        aliases:
          - turdparty-celery-worker-1

  celery-flower:
    build:
      context: .
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      - redis
      - celery-worker
    ports:
      - "5555:5555"
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./:/app
    command: ["celery", "-A", "api.celery_app", "flower", "--port=5555", "--address=0.0.0.0"]
    networks:
      default:
        aliases:
          - turdparty-celery-flower-1

volumes:
  postgres_data:
  minio_data:
  redis_data:

networks:
  default:
    name: turdparty_default