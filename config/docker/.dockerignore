# Version control
.git
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.venv/
venv/
ENV/

# Docker
.dockerwrapper/*
!.dockerwrapper/Dockerfile
!.dockerwrapper/Dockerfile.playwright
!.dockerwrapper/docker-compose.production.yml
!.dockerwrapper/scripts/

# Development environment
.idea/
.vscode/
*.swp
*.swo

# Testing
.coverage
htmlcov/
.tox/

# OS specific
.DS_Store
Thumbs.db 