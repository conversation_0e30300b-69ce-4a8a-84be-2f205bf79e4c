FROM python:3.10-slim-buster

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libmagic1 \
    openssh-client \
    libyaml-dev \
    python3-yaml \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install Python dependencies (without requirements file for simplicity)
RUN pip install --upgrade pip && \
    pip install --no-cache-dir \
    fastapi==0.95.0 \
    uvicorn==0.21.1 \
    sqlalchemy==2.0.7 \
    psycopg2-binary==2.9.5 \
    python-multipart==0.0.6 \
    python-jose==3.3.0 \
    passlib==1.7.4 \
    bcrypt==4.0.1 \
    pydantic==1.10.7 \
    python-dotenv==1.0.0 \
    requests==2.31.0 \
    starlette==0.26.1 \
    asyncpg==0.27.0 \
    minio==7.1.14 \
    werkzeug==2.0.3 \
    flask==2.0.1 \
    pyjwt==2.4.0 \
    email-validator==2.0.0 \
    boto3==1.28.0 \
    paramiko==3.2.0 \
    pyotp==2.8.0 \
    qrcode==7.4.2 \
    python-magic==0.4.27

# Copy application code (we'll mount volumes in docker-compose)
COPY ./api /app/api
COPY ./main.py /app/main.py

# Create required directories
RUN mkdir -p /app/uploads /app/logs

# Expose port
EXPOSE 8000

# Run the application directly with Python
CMD ["python", "main.py"]