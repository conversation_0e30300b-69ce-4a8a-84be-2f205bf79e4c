{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    python310
    python310Packages.rich
    python310Packages.textual
    python310Packages.docker
    python310Packages.requests
    python310Packages.typer
    python310Packages.psutil
  ];

  shellHook = ''
    echo ""
    echo "TurdParty Service Monitor Environment"
    echo "====================================="
    echo "Available commands:"
    echo "  python service_monitor.py       - Run the service monitor with Rich UI"
    echo "  python service_monitor.py --tui - Run the service monitor with Textual TUI"
    echo ""
  '';
}
