# Root Cleanup - Configuration Files

This document tracks configuration files moved from the project root during cleanup.

## 📁 Configuration Organization

### 🐳 Docker Configuration (`docker/`)
- `docker-compose.yml` - Main Docker Compose configuration
- `docker-lock.json` - Docker dependency lock file

### 📦 Node.js Configuration (`node/`)
- `package.json` - Node.js project configuration
- `package-lock.json` - Node.js dependency lock file

### 🐍 Python Configuration (`python/`)
- `pyproject.toml` - Python project configuration (Poetry)
- `poetry.lock` - Poetry dependency lock file
- `requirements.txt` - Python dependencies
- `requirements-ssh.txt` - SSH-specific Python dependencies

### 🎭 Playwright Configuration (`playwright/`)
- `playwright.config.js` - Playwright test configuration
- `playwright-auth.json` - Playwright authentication data

## 📊 Logs Moved (`../logs/cleanup/`)
- `cleanup_master_*.log` - Master cleanup script logs
- `endpoint_monitoring.log` - Endpoint monitoring logs

## 🧪 Testing Scripts Moved (`../scripts/testing/`)
- `check_basic_openapi.js` - Basic OpenAPI validation
- `check_docs_content.js` - Documentation content checker
- `check_docs_endpoint.js` - Documentation endpoint checker
- `check_openapi_and_ui.js` - OpenAPI and UI validation
- `check_swagger_ui.js` - Swagger UI checker
- `minimal-config.js` - Minimal configuration for testing
- `docker-dashboard.js` - Docker dashboard script
- `server.js` - Test server implementation

## 📄 Data Files Moved (`../data/temp/`)
- `tasks.json` - Task configuration data
- `timer_data.json` - Timer data
- `status_chart.txt` - Status chart data
- `implementation_vs_pending.txt` - Implementation status
- `textual_ui.css` - Textual UI styles

## 🔄 Usage Notes

All configuration files maintain their original functionality but are now organized by technology/purpose.

### Important: Update References

After this cleanup, update any scripts or documentation that reference these files:
- Docker Compose commands should use `-f config/docker/docker-compose.yml`
- Package managers should reference the new config locations
- CI/CD pipelines may need path updates

### Symlinks (if needed)

If any tools require these files in the root, consider creating symlinks:
```bash
ln -s config/docker/docker-compose.yml docker-compose.yml
ln -s config/node/package.json package.json
ln -s config/python/pyproject.toml pyproject.toml
```
