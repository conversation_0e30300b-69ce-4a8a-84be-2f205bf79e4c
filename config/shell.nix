{ pkgs ? import <nixpkgs> {} }:

with pkgs;

mkShell {
  buildInputs = [
    vagrant
    qemu_kvm
    libvirt
    python3
    python3Packages.pytest
    python3Packages.fastapi
    python3Packages.sqlalchemy
    python3Packages.requests
    python3Packages.paramiko
    python3Packages.httpx
    python3Packages.pyjwt
    python3Packages.cryptography
    python3Packages.pydantic-settings
    python3Packages.flask
    python3Packages.psycopg2
    python3Packages.asyncpg
    python3Packages.passlib
    python3Packages.email-validator
    python3Packages.boto3
    python3Packages.pyotp
    python3Packages.qrcode
    python3Packages.python-multipart
    python3Packages.minio
    python3Packages.pip
    python3Packages.pytest-asyncio
    python3Packages.aiosqlite
    python3Packages.coverage
    python3Packages.streamlit
    python3Packages.grpcio
    python3Packages.psutil
    python3Packages.rich
    python3Packages.textual
    python3Packages.typer
    python3Packages.docker
    python3Packages.pyyaml
    python3Packages.python-magic
    docker-compose
    gnumake
    nodejs_20
    nodePackages.npm
    findutils
    # Celery dependencies
    python3Packages.celery
    python3Packages.redis
    python3Packages.flower
    python3Packages.kombu
    redis
  ];

  shellHook = ''
    export NIXPKGS_ALLOW_UNFREE=1
    export VAGRANT_DEFAULT_PROVIDER="libvirt"
    export SSL_CERT_FILE="${cacert}/etc/ssl/certs/ca-bundle.crt"

    echo ""
    echo "TurdParty Development Environment"
    echo "=================================="
    echo "Available commands:"
    echo "  python service_monitor.py       - Run the service monitor with Rich UI"
    echo "  python service_monitor.py --tui - Run the service monitor with Textual TUI"
    echo ""
  '';
}