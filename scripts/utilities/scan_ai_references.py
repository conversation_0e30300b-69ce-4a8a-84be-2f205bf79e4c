#!/usr/bin/env python3
"""
AI Reference Scanner

This script scans the codebase for references to AI tools like <PERSON> or Cursor
that should be reviewed and potentially removed before production deployment.

Usage:
    python scan_ai_references.py [--path PATH] [--output OUTPUT] [--verbose]

Options:
    --path PATH       Path to scan (default: current directory)
    --output OUTPUT   Output file for results (default: ai_references_report.md)
    --verbose         Enable verbose output
    --fix             Attempt to automatically comment out AI references
"""

import os
import re
import argparse
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Tuple

# AI tools and related terms to scan for
AI_TERMS = {
    "claude": [
        r"claude",
        r"anthropic",
        r"\.claude",
        r"claude\.",
        r"claude\s+prompt",
        r"prompt\s+claude",
    ],
    "cursor": [
        r"cursor",
        r"cursor\.sh",
        r"cursor\s+ai",
        r"\.cursor",
        r"cursor\.",
        r"cursorrules",
    ],
    "ai_assistant": [
        r"ai\s+assistant",
        r"assistant\s+ai",
        r"ai\s+helper",
        r"ai\s+generated",
        r"generated\s+by\s+ai",
    ],
    "llm": [
        r"llm",
        r"large\s+language\s+model",
        r"language\s+model",
        r"gpt",
        r"chatgpt",
    ],
}

# File extensions to scan
FILE_EXTENSIONS = [
    ".py", ".js", ".jsx", ".ts", ".tsx", ".md", ".txt", ".html",
    ".css", ".scss", ".json", ".yaml", ".yml", ".sh", ".bash",
    ".sql", ".graphql", ".toml", ".ini", ".cfg", ".conf"
]

# Directories to exclude
EXCLUDE_DIRS = [
    "node_modules", "venv", ".venv", "env", ".env", ".git",
    "__pycache__", ".pytest_cache", "dist", "build", ".idea",
    ".vscode", ".coverage", "htmlcov", ".mypy_cache", ".tox",
    ".eggs", "egg-info", "egg", ".dockerwrapper", ".focus_forge",
    "focus_forge"
]

# Files to exclude
EXCLUDE_FILES = [
    "package-lock.json", "yarn.lock", "poetry.lock", "Pipfile.lock",
    "requirements-lock.txt", "ai_references_report.md"
]

# Allowed references (e.g., in documentation about the project structure)
ALLOWED_REFERENCES = [
    r"\.ai/docs/0-ai-config/cursor/",
    r"\.ai/docs/0-ai-config/claude/",
    r"\.ai/README\.md",
    r"\.ai/STRUCTURE\.md",
    r"project_structure_prd\.md",
]

# Directories to completely exclude from the report (even if they contain matches)
EXCLUDE_FROM_REPORT = [
    r"^/home/<USER>/dev/10Baht/turdparty/\.ai/",
]


class AIReferenceScanner:
    """Scanner for AI tool references in the codebase."""

    def __init__(self, base_path: str = ".", verbose: bool = False):
        """Initialize the scanner.

        Args:
            base_path: The base path to scan
            verbose: Whether to enable verbose output
        """
        self.base_path = Path(base_path).resolve()
        self.verbose = verbose
        self.references: Dict[str, List[Tuple[str, int, str, str]]] = {}
        self.stats = {
            "files_scanned": 0,
            "files_with_references": 0,
            "total_references": 0,
            "references_by_category": {},
            "references_by_extension": {},
        }

    def is_excluded_path(self, path: Path) -> bool:
        """Check if a path should be excluded from scanning.

        Args:
            path: The path to check

        Returns:
            True if the path should be excluded, False otherwise
        """
        # Check if the path is in an excluded directory
        for part in path.parts:
            if part in EXCLUDE_DIRS:
                return True

        # Check if the file is excluded
        if path.name in EXCLUDE_FILES:
            return True

        return False

    def is_allowed_reference(self, file_path: str, line: str) -> bool:
        """Check if a reference is allowed (e.g., in documentation).

        Args:
            file_path: The path to the file containing the reference
            line: The line containing the reference

        Returns:
            True if the reference is allowed, False otherwise
        """
        for allowed in ALLOWED_REFERENCES:
            if re.search(allowed, file_path) or re.search(allowed, line):
                return True
        return False

    def scan_file(self, file_path: Path) -> None:
        """Scan a single file for AI references.

        Args:
            file_path: The path to the file to scan
        """
        if self.verbose:
            print(f"Scanning {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                lines = f.readlines()

            self.stats["files_scanned"] += 1
            file_has_references = False

            for line_num, line in enumerate(lines, 1):
                for category, patterns in AI_TERMS.items():
                    for pattern in patterns:
                        matches = re.finditer(pattern, line, re.IGNORECASE)
                        for match in matches:
                            # Skip allowed references
                            if self.is_allowed_reference(str(file_path), line):
                                continue

                            # Record the reference
                            if str(file_path) not in self.references:
                                self.references[str(file_path)] = []

                            self.references[str(file_path)].append((
                                category,
                                line_num,
                                line.strip(),
                                match.group(0)
                            ))

                            # Update statistics
                            self.stats["total_references"] += 1
                            self.stats["references_by_category"][category] = \
                                self.stats["references_by_category"].get(category, 0) + 1

                            ext = file_path.suffix
                            self.stats["references_by_extension"][ext] = \
                                self.stats["references_by_extension"].get(ext, 0) + 1

                            file_has_references = True

            if file_has_references:
                self.stats["files_with_references"] += 1

        except Exception as e:
            print(f"Error scanning {file_path}: {e}", file=sys.stderr)

    def scan_directory(self) -> None:
        """Scan the base directory recursively for AI references."""
        for root, dirs, files in os.walk(self.base_path):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]

            for file in files:
                file_path = Path(root) / file

                # Skip excluded files and check extension
                if (self.is_excluded_path(file_path) or
                    file_path.suffix not in FILE_EXTENSIONS):
                    continue

                self.scan_file(file_path)

    def generate_report(self, output_file: str = "ai_references_report.md") -> None:
        """Generate a Markdown report of the scan results.

        Args:
            output_file: The file to write the report to
        """
        # Filter out references from excluded directories
        filtered_references = {}
        filtered_total = 0
        filtered_files_count = 0
        filtered_by_category = {category: 0 for category in self.stats["references_by_category"]}
        filtered_by_extension = {ext: 0 for ext in self.stats["references_by_extension"]}

        for file_path, references in self.references.items():
            # Skip files in excluded directories
            if any(re.match(pattern, file_path) for pattern in EXCLUDE_FROM_REPORT):
                continue

            filtered_references[file_path] = references
            filtered_files_count += 1
            filtered_total += len(references)

            # Update category and extension counts
            for category, _, _, _ in references:
                filtered_by_category[category] = filtered_by_category.get(category, 0) + 1

            ext = Path(file_path).suffix
            filtered_by_extension[ext] = filtered_by_extension.get(ext, 0) + 1

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# AI References Scan Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## Summary\n\n")
            f.write(f"- Files scanned: {self.stats['files_scanned']}\n")
            f.write(f"- Files with AI references (excluding .ai/ directory): {filtered_files_count}\n")
            f.write(f"- Total references found (excluding .ai/ directory): {filtered_total}\n\n")

            f.write("## References by Category\n\n")
            for category, count in filtered_by_category.items():
                if count > 0:
                    f.write(f"- {category}: {count}\n")
            f.write("\n")

            f.write("## References by File Extension\n\n")
            for ext, count in filtered_by_extension.items():
                if count > 0:
                    f.write(f"- {ext}: {count}\n")
            f.write("\n")

            f.write("## Detailed Findings\n\n")
            for file_path, references in sorted(filtered_references.items()):
                f.write(f"### {file_path}\n\n")

                for category, line_num, line, match in references:
                    f.write(f"- Line {line_num} ({category}): `{line}`\n")
                    f.write(f"  - Match: `{match}`\n")

                f.write("\n")

            f.write("## Recommendations\n\n")
            f.write("1. Review each reference and determine if it should be removed or modified\n")
            f.write("2. For documentation files, consider replacing specific AI tool mentions with generic terms\n")
            f.write("3. For code files, remove any AI-specific comments or instructions\n")
            f.write("4. Update the `.ai` directory structure to use more generic naming if needed\n")
            f.write("5. Run this scan regularly before releases to ensure no AI references remain\n")

    def attempt_fixes(self) -> Dict[str, List[Tuple[int, str, str]]]:
        """Attempt to automatically fix AI references by commenting them out.

        Returns:
            A dictionary of files and the changes made
        """
        fixes = {}

        for file_path, references in self.references.items():
            path = Path(file_path)

            # Skip binary files and non-text files
            if path.suffix not in FILE_EXTENSIONS:
                continue

            try:
                with open(path, 'r', encoding='utf-8', errors='replace') as f:
                    lines = f.readlines()

                file_fixes = []
                for category, line_num, line, match in references:
                    # Skip if already commented
                    if line.strip().startswith(('#', '//', '/*', '*', '<!--')):
                        continue

                    # Determine comment style based on file extension
                    comment_prefix = "# "  # Default for Python, Shell, etc.
                    if path.suffix in ['.js', '.jsx', '.ts', '.tsx', '.css', '.scss']:
                        comment_prefix = "// "
                    elif path.suffix in ['.html', '.md', '.xml']:
                        comment_prefix = "<!-- "
                        comment_suffix = " -->"

                    # Create commented version
                    commented_line = comment_prefix + line
                    if path.suffix in ['.html', '.md', '.xml']:
                        commented_line = comment_prefix + line + comment_suffix

                    # Record the fix
                    file_fixes.append((line_num, line, commented_line))

                    # Apply the fix
                    lines[line_num - 1] = commented_line

                if file_fixes:
                    # Write the changes back to the file
                    with open(path, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    fixes[file_path] = file_fixes

            except Exception as e:
                print(f"Error fixing {file_path}: {e}", file=sys.stderr)

        return fixes


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Scan codebase for AI tool references")
    parser.add_argument("--path", default=".", help="Path to scan (default: current directory)")
    parser.add_argument("--output", default="ai_references_report.md", help="Output file for results")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--fix", action="store_true", help="Attempt to automatically comment out AI references")

    args = parser.parse_args()

    scanner = AIReferenceScanner(args.path, args.verbose)

    print(f"Scanning {args.path} for AI references...")
    scanner.scan_directory()

    print(f"Found {scanner.stats['total_references']} references in {scanner.stats['files_with_references']} files")

    if args.fix:
        print("Attempting to fix references...")
        fixes = scanner.attempt_fixes()
        print(f"Applied fixes to {len(fixes)} files")

    scanner.generate_report(args.output)
    print(f"Report generated: {args.output}")


if __name__ == "__main__":
    main()
