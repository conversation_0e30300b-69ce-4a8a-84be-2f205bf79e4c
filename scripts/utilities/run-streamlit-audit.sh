#!/bin/bash

# Simple script to audit all Streamlit usage in the codebase
# This will help plan the migration from Streamlit to Flask

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set up log file
LOG_DIR="test_logs"
mkdir -p $LOG_DIR
STREAMLIT_LOG="$LOG_DIR/streamlit_to_flask_conversion_$(date +%Y%m%d_%H%M%S).md"

echo "# Streamlit to Flask Conversion Log" > $STREAMLIT_LOG
echo "Generated on $(date)" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG

log() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

section() {
    echo -e "\n${YELLOW}======================= $* =======================${NC}"
}

section "STREAMLIT TO FLASK CONVERSION AUDIT"
log "Starting Streamlit audit at $(date)"
log "Log file: $STREAMLIT_LOG"

# Find all Python files with streamlit imports
STREAMLIT_FILES=($(grep -r "import streamlit" --include="*.py" . | cut -d: -f1 | sort | uniq))

log "Found ${#STREAMLIT_FILES[@]} files with Streamlit imports"
echo "## Files requiring conversion from Streamlit to Flask" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG
echo "Total files to convert: ${#STREAMLIT_FILES[@]}" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG

# Group files by directory for better organization
echo "### By directory" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG

DIRECTORIES=$(for file in "${STREAMLIT_FILES[@]}"; do dirname "$file"; done | sort | uniq)

for dir in $DIRECTORIES; do
    echo "#### $dir/" >> $STREAMLIT_LOG
    echo "" >> $STREAMLIT_LOG
    
    DIR_FILES=($(grep -r "import streamlit" --include="*.py" "$dir" | cut -d: -f1 | sort | uniq))
    for file in "${DIR_FILES[@]}"; do
        echo "- **$file**" >> $STREAMLIT_LOG
        
        # Count streamlit usage instances
        ST_COUNT=$(grep -c "st\." "$file")
        echo "  - Streamlit API calls: ~$ST_COUNT" >> $STREAMLIT_LOG
        
        # Look for specific Streamlit components that need conversion
        echo "  - Components used:" >> $STREAMLIT_LOG
        
        # Common Streamlit UI components
        if grep -q "st\.button" "$file"; then
            echo "    - Buttons" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.form" "$file"; then
            echo "    - Forms" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.text_input\|st\.number_input\|st\.date_input" "$file"; then
            echo "    - Input fields" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.selectbox\|st\.multiselect" "$file"; then
            echo "    - Selection widgets" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.sidebar" "$file"; then
            echo "    - Sidebar" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.write\|st\.markdown\|st\.text" "$file"; then
            echo "    - Text display" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.dataframe\|st\.table" "$file"; then
            echo "    - Data tables" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.plotly\|st\.pyplot\|st\.altair" "$file"; then
            echo "    - Charts/visualizations" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.file_uploader" "$file"; then
            echo "    - File uploaders" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.session_state" "$file"; then
            echo "    - Session state (needs Flask sessions)" >> $STREAMLIT_LOG
        fi
        if grep -q "st\.cache" "$file"; then
            echo "    - Caching mechanism" >> $STREAMLIT_LOG
        fi
        
        # Add code samples
        echo "  - Sample usage:" >> $STREAMLIT_LOG
        echo '```python' >> $STREAMLIT_LOG
        grep -n "st\." "$file" | head -5 | sed 's/^/    /' >> $STREAMLIT_LOG
        if [ "$(grep -n "st\." "$file" | wc -l)" -gt 5 ]; then
            echo "    # ... and $(( $(grep -n "st\." "$file" | wc -l) - 5 )) more instances" >> $STREAMLIT_LOG
        fi
        echo '```' >> $STREAMLIT_LOG
        echo "" >> $STREAMLIT_LOG
    done
    
    echo "" >> $STREAMLIT_LOG
done

# Provide a conversion guide
echo "## Conversion Guide" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG
echo "### Common Streamlit to Flask Mappings" >> $STREAMLIT_LOG
echo "" >> $STREAMLIT_LOG

# Write the table directly without using command substitution
cat << 'EOF' >> $STREAMLIT_LOG
| Streamlit Component | Flask/HTML Equivalent |
|-------------------|------------------------|
| st.button | `<button>` or `<input type="submit">` |
| st.text_input | `<input type="text">` |
| st.number_input | `<input type="number">` |
| st.selectbox | `<select>` dropdown |
| st.multiselect | `<select multiple>` |
| st.checkbox | `<input type="checkbox">` |
| st.radio | `<input type="radio">` |
| st.form | `<form>` element |
| st.sidebar | Left navigation or sidebar with CSS |
| st.write/markdown | Rendered with Jinja2 templates |
| st.dataframe/table | HTML tables or DataTables.js |
| st.file_uploader | `<input type="file">` |
| st.session_state | Flask session |
| st.cache | Flask-Caching extension |
EOF

echo "" >> $STREAMLIT_LOG

section "STREAMLIT AUDIT COMPLETE"
log "Audit completed at $(date)"
log "Found ${#STREAMLIT_FILES[@]} files requiring conversion from Streamlit to Flask"
log "Detailed report written to: $STREAMLIT_LOG"

# Print top directories by number of files
section "FILES BY DIRECTORY"
for dir in $DIRECTORIES; do
    count=$(grep -r "import streamlit" --include="*.py" "$dir" | cut -d: -f1 | sort | uniq | wc -l)
    echo -e "${YELLOW}$dir/${NC}: ${count} files"
done 