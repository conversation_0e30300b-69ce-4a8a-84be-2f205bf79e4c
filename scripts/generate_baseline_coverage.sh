#!/bin/bash

# TurdParty Baseline Coverage Generation Script
# This script generates comprehensive baseline coverage reports before cleanup

set -e

# Make script executable
chmod +x "$0"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASELINE_DIR="test-reports/baseline"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$BASELINE_DIR/baseline_generation_$TIMESTAMP.log"

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}   TurdParty Baseline Coverage Report   ${NC}"
echo -e "${BLUE}=======================================${NC}"

# Create baseline directory structure
echo -e "${YELLOW}Setting up baseline directory structure...${NC}"
mkdir -p "$BASELINE_DIR"/{html,xml,json,logs,inventory,performance}

# Start logging
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "Baseline coverage generation started at: $(date)"
echo "Baseline directory: $BASELINE_DIR"
echo "Log file: $LOG_FILE"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run command with error handling
run_with_error_handling() {
    local cmd="$1"
    local description="$2"
    
    echo -e "\n${YELLOW}$description...${NC}"
    echo "Executing: $cmd"
    
    if eval "$cmd"; then
        echo -e "${GREEN}✓ $description completed successfully${NC}"
        return 0
    else
        echo -e "${RED}✗ $description failed${NC}"
        return 1
    fi
}

# Function to measure execution time
measure_time() {
    local start_time=$(date +%s.%N)
    "$@"
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    echo "Execution time: ${duration}s"
    echo "$1,$duration" >> "$BASELINE_DIR/performance/execution_times.csv"
}

# Initialize performance tracking
echo "Test Type,Execution Time (seconds)" > "$BASELINE_DIR/performance/execution_times.csv"

# 1. Generate file inventory
echo -e "\n${BLUE}=== GENERATING FILE INVENTORY ===${NC}"

run_with_error_handling \
    "find . -type f -name '*.py' -o -name '*.js' -o -name '*.ts' | grep -E '(test_|_test\.|\.test\.|\.spec\.)' > $BASELINE_DIR/inventory/test_files.txt" \
    "Cataloging test files"

run_with_error_handling \
    "find . -maxdepth 1 -type f -exec ls -lh {} \; > $BASELINE_DIR/inventory/root_files.txt" \
    "Cataloging root directory files"

run_with_error_handling \
    "find . -name '*.py' -exec grep -l '^import\|^from' {} \; | head -100 > $BASELINE_DIR/inventory/python_files_sample.txt" \
    "Sampling Python files for dependency analysis"

# 2. Python/pytest coverage
echo -e "\n${BLUE}=== GENERATING PYTHON COVERAGE ===${NC}"

if command_exists python && command_exists pytest; then
    # Set Python path
    export PYTHONPATH=.
    
    # Run pytest with comprehensive coverage
    measure_time run_with_error_handling \
        "python -m pytest \
            --cov=api \
            --cov=db \
            --cov=ui \
            --cov=scripts \
            --cov-report=html:$BASELINE_DIR/html/python \
            --cov-report=xml:$BASELINE_DIR/xml/python_coverage.xml \
            --cov-report=json:$BASELINE_DIR/json/python_coverage.json \
            --cov-report=term \
            -v \
            --tb=short \
            --maxfail=10 \
            > $BASELINE_DIR/logs/pytest_output.txt 2>&1" \
        "Running pytest with coverage"
    
    # Generate coverage summary
    if [ -f "$BASELINE_DIR/json/python_coverage.json" ]; then
        python -c "
import json
with open('$BASELINE_DIR/json/python_coverage.json', 'r') as f:
    data = json.load(f)
    summary = data.get('totals', {})
    print(f'Python Coverage Summary:')
    print(f'  Lines: {summary.get(\"percent_covered\", 0):.2f}%')
    print(f'  Branches: {summary.get(\"percent_covered_display\", \"N/A\")}')
    print(f'  Files covered: {len(data.get(\"files\", {}))}')
" > "$BASELINE_DIR/logs/python_coverage_summary.txt"
    fi
else
    echo -e "${RED}Python or pytest not available, skipping Python coverage${NC}"
fi

# 3. JavaScript/Node.js coverage (if applicable)
echo -e "\n${BLUE}=== GENERATING JAVASCRIPT COVERAGE ===${NC}"

if command_exists npm && [ -f "package.json" ]; then
    # Check if jest is available
    if npm list jest >/dev/null 2>&1; then
        measure_time run_with_error_handling \
            "npm test -- --coverage --coverageDirectory=$BASELINE_DIR/html/javascript --coverageReporters=html,json,text > $BASELINE_DIR/logs/jest_output.txt 2>&1" \
            "Running Jest with coverage"
    else
        echo -e "${YELLOW}Jest not configured, skipping JavaScript unit test coverage${NC}"
    fi
else
    echo -e "${YELLOW}Node.js/npm not available, skipping JavaScript coverage${NC}"
fi

# 4. Playwright E2E coverage
echo -e "\n${BLUE}=== GENERATING PLAYWRIGHT COVERAGE ===${NC}"

if command_exists npx && [ -f "playwright.config.js" ]; then
    # Ensure test results directory exists
    mkdir -p "$BASELINE_DIR/html/playwright"
    
    measure_time run_with_error_handling \
        "npx playwright test --reporter=html --output-dir=$BASELINE_DIR/html/playwright > $BASELINE_DIR/logs/playwright_output.txt 2>&1" \
        "Running Playwright tests"
    
    # Copy playwright report if generated
    if [ -d "playwright-report" ]; then
        cp -r playwright-report/* "$BASELINE_DIR/html/playwright/" 2>/dev/null || true
    fi
else
    echo -e "${YELLOW}Playwright not available, skipping E2E coverage${NC}"
fi

# 5. Integration test coverage
echo -e "\n${BLUE}=== RUNNING INTEGRATION TESTS ===${NC}"

# Check for integration test scripts
if [ -f "run-all-tests.sh" ]; then
    measure_time run_with_error_handling \
        "timeout 600 ./run-all-tests.sh > $BASELINE_DIR/logs/integration_tests.txt 2>&1" \
        "Running integration tests (10 min timeout)"
elif [ -f "run_integration_tests.sh" ]; then
    measure_time run_with_error_handling \
        "timeout 600 ./run_integration_tests.sh > $BASELINE_DIR/logs/integration_tests.txt 2>&1" \
        "Running integration tests (10 min timeout)"
else
    echo -e "${YELLOW}No integration test script found, skipping${NC}"
fi

# 6. Generate system information
echo -e "\n${BLUE}=== COLLECTING SYSTEM INFORMATION ===${NC}"

cat > "$BASELINE_DIR/logs/system_info.txt" << EOF
System Information - Baseline Coverage Generation
Generated: $(date)
Hostname: $(hostname)
OS: $(uname -a)
Python Version: $(python --version 2>&1 || echo "Not available")
Node Version: $(node --version 2>&1 || echo "Not available")
Git Commit: $(git rev-parse HEAD 2>&1 || echo "Not available")
Git Branch: $(git branch --show-current 2>&1 || echo "Not available")
Working Directory: $(pwd)
EOF

# 7. Generate dependency information
echo -e "\n${BLUE}=== COLLECTING DEPENDENCY INFORMATION ===${NC}"

if [ -f "requirements.txt" ]; then
    cp requirements.txt "$BASELINE_DIR/inventory/"
fi

if [ -f "pyproject.toml" ]; then
    cp pyproject.toml "$BASELINE_DIR/inventory/"
fi

if [ -f "package.json" ]; then
    cp package.json "$BASELINE_DIR/inventory/"
fi

if [ -f "package-lock.json" ]; then
    cp package-lock.json "$BASELINE_DIR/inventory/"
fi

# 8. Generate baseline summary report
echo -e "\n${BLUE}=== GENERATING BASELINE SUMMARY ===${NC}"

cat > "$BASELINE_DIR/baseline_summary.md" << EOF
# TurdParty Baseline Coverage Report

Generated: $(date)
Baseline Directory: $BASELINE_DIR

## Test Execution Summary

### Python Tests
- Coverage Report: [HTML]($BASELINE_DIR/html/python/index.html)
- Coverage Data: [JSON]($BASELINE_DIR/json/python_coverage.json)
- Test Output: [Log]($BASELINE_DIR/logs/pytest_output.txt)

### JavaScript Tests
- Coverage Report: [HTML]($BASELINE_DIR/html/javascript/index.html)
- Test Output: [Log]($BASELINE_DIR/logs/jest_output.txt)

### Playwright E2E Tests
- Test Report: [HTML]($BASELINE_DIR/html/playwright/index.html)
- Test Output: [Log]($BASELINE_DIR/logs/playwright_output.txt)

### Integration Tests
- Test Output: [Log]($BASELINE_DIR/logs/integration_tests.txt)

## Performance Metrics
- Execution Times: [CSV]($BASELINE_DIR/performance/execution_times.csv)

## File Inventory
- Test Files: [List]($BASELINE_DIR/inventory/test_files.txt)
- Root Files: [List]($BASELINE_DIR/inventory/root_files.txt)
- Dependencies: [Files]($BASELINE_DIR/inventory/)

## System Information
- System Info: [Details]($BASELINE_DIR/logs/system_info.txt)

## Next Steps
1. Review coverage reports for completeness
2. Proceed with cleanup phases
3. Generate post-cleanup coverage for comparison
EOF

# 9. Create coverage comparison preparation
echo -e "\n${BLUE}=== PREPARING FOR COMPARISON ===${NC}"

# Create a metadata file for comparison script
cat > "$BASELINE_DIR/metadata.json" << EOF
{
    "generation_date": "$(date -Iseconds)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
    "baseline_dir": "$BASELINE_DIR",
    "reports": {
        "python_coverage_json": "$BASELINE_DIR/json/python_coverage.json",
        "python_coverage_xml": "$BASELINE_DIR/xml/python_coverage.xml",
        "python_coverage_html": "$BASELINE_DIR/html/python/index.html",
        "playwright_html": "$BASELINE_DIR/html/playwright/index.html",
        "execution_times": "$BASELINE_DIR/performance/execution_times.csv"
    }
}
EOF

echo -e "\n${GREEN}=======================================${NC}"
echo -e "${GREEN}   Baseline Coverage Generation Complete   ${NC}"
echo -e "${GREEN}=======================================${NC}"

echo -e "\nBaseline reports generated in: ${BLUE}$BASELINE_DIR${NC}"
echo -e "Summary report: ${BLUE}$BASELINE_DIR/baseline_summary.md${NC}"
echo -e "Log file: ${BLUE}$LOG_FILE${NC}"

echo -e "\n${YELLOW}Next steps:${NC}"
echo -e "1. Review the generated reports"
echo -e "2. Proceed with cleanup phases"
echo -e "3. Run generate_post_cleanup_coverage.sh after cleanup"
echo -e "4. Use compare_coverage_reports.sh to validate changes"

exit 0
