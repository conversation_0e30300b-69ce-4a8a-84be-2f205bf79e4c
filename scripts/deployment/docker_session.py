"""
This file is deprecated. Use api.db.dependencies instead.
This is kept for backward compatibility only.
"""
import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from typing import Generator, Optional
from contextlib import contextmanager
import os

from api.core.config import settings
from api.db.base import Base

logger = logging.getLogger(__name__)

# Get database URL from environment or use default
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost/turdparty")

# Create synchronous engine with connection pooling
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    pool_pre_ping=True,
    pool_size=settings.SQLALCHEMY_POOL_SIZE,
    max_overflow=settings.SQLALCHEMY_MAX_OVERFLOW,
    pool_timeout=settings.SQLALCHEMY_POOL_TIMEOUT,
    echo=settings.DEBUG
)

# Create synchronous session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# Create asynchronous engine
async_engine = create_async_engine(
    settings.DATABASE_URL_ASYNC,
    pool_pre_ping=True,
    pool_size=settings.SQLALCHEMY_POOL_SIZE,
    max_overflow=settings.SQLALCHEMY_MAX_OVERFLOW,
    pool_timeout=settings.SQLALCHEMY_POOL_TIMEOUT,
    echo=settings.DEBUG
)

# Create asynchronous session factory
AsyncSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    expire_on_commit=False
)

# For backwards compatibility
SessionLocal = SessionLocal

Base = declarative_base()

@contextmanager
def get_db() -> Generator[Session, None, None]:
    """Get database session with automatic commit/rollback."""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()

def get_db_session() -> Session:
    """Get a new database session."""
    return SessionLocal()

def set_session_user(session: Session, user_id: str) -> None:
    """Set the current user ID in the session info."""
    if not hasattr(session, 'info'):
        session.info = {}
    session.info['user_id'] = user_id

def get_sync_session() -> Session:
    """Get a synchronous database session."""
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()

async def get_async_session() -> AsyncSession:
    """Get an asynchronous database session."""
    async with AsyncSessionLocal() as session:
        return session

def get_db():
    db = SessionLocal()
    try:
        logger.debug("Database session started")
        yield db
    except Exception as e:
        logger.error(f"Error during database session: {str(e)}")
        db.rollback()
        raise
    finally:
        logger.debug("Database session closed")
        db.close()

async def get_db_session():
    """
    Get an async database session.
    
    Yields:
        AsyncSession: SQLAlchemy async session
    """
    async_session = AsyncSessionLocal()
    try:
        logger.debug("Async database session started")
        yield async_session
        await async_session.commit()
    except Exception as e:
        logger.error(f"Error during async database session: {str(e)}")
        await async_session.rollback()
        raise
    finally:
        logger.debug("Async database session closed")
        await async_session.close()

def init_db(*, clean: bool = False) -> None:
    """Initialize database."""
    try:
        logger.info("Initializing database...")
        
        if clean:
            logger.info("Dropping all tables...")
            Base.metadata.drop_all(bind=engine)
        
        logger.info("Creating tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise