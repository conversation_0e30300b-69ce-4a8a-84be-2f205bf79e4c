#!/bin/bash

# Script to install the required Node.js packages for the Docker dashboard

# Change to the script directory
cd "$(dirname "$0")"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Installing Node.js..."
    
    # Install Node.js using apt
    sudo apt-get update
    sudo apt-get install -y nodejs npm
    
    # Check if installation was successful
    if ! command -v node &> /dev/null; then
        echo "Failed to install Node.js. Please install it manually."
        exit 1
    fi
    
    echo "Node.js installed successfully."
fi

# Install required packages
echo "Installing required Node.js packages..."
npm install

echo "Installation complete. You can now run the dashboard using ./turdparty-dashboard.sh" 