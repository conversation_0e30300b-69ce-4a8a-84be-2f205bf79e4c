#!/bin/bash
# Script to set up MinIO environment variables in the Docker container

# Check if the container is running
if ! docker ps | grep -q TurdParty-container-test; then
  echo "Error: TurdParty-container-test container is not running"
  exit 1
fi

echo "Setting up MinIO environment variables in the container..."

# Restart the API service in the container with environment variables
echo "Restarting the API service in the container..."
docker exec -d TurdParty-container-test bash -c 'pkill -f "python -m uvicorn main:app" || true'
docker exec -d -e MINIO_ACCESS_KEY=minioadmin \
                -e MINIO_SECRET_KEY=minioadmin \
                -e SSH_KEY_PATH=/home/<USER>/.ssh/id_rsa \
                -e VAGRANT_SSH_USER=vagrant \
                -e MINIO_HOST=localhost \
                TurdParty-container-test bash -c 'cd /app && python -m uvicorn main:app --host 0.0.0.0 --port 8080'

echo "Environment setup complete." 