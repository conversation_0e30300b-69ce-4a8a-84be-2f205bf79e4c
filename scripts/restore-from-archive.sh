#!/bin/bash

# TurdParty File Restoration Script
# Restores a file from the archive back to its original location

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ARCHIVE_ROOT="_archive_"
CLEANUP_LOG="$ARCHIVE_ROOT/cleanup-log.md"

show_usage() {
    echo "Usage: $0 <archived_file_path>"
    echo ""
    echo "Arguments:"
    echo "  archived_file_path  Path to the archived file (e.g., _archive_/test-upload.js)"
    echo ""
    echo "Examples:"
    echo "  $0 _archive_/test-upload.js"
    echo "  $0 _archive_/scripts/debug_script.py"
    exit 1
}

if [ $# -ne 1 ]; then
    echo -e "${RED}Error: Incorrect number of arguments${NC}"
    show_usage
fi

ARCHIVED_FILE="$1"

if [ ! -f "$ARCHIVED_FILE" ]; then
    echo -e "${RED}Error: Archived file '$ARCHIVED_FILE' does not exist${NC}"
    exit 1
fi

# Extract original location from file header
ORIGINAL_LOCATION=$(head -10 "$ARCHIVED_FILE" | grep "Original Location:" | sed 's/.*Original Location: //' | sed 's/[[:space:]]*$//' | tr -d '\r')

if [ -z "$ORIGINAL_LOCATION" ]; then
    echo -e "${RED}Error: Could not determine original location from archived file${NC}"
    exit 1
fi

# Remove leading slash if present
ORIGINAL_LOCATION=$(echo "$ORIGINAL_LOCATION" | sed 's/^\/*//')

echo -e "${BLUE}Restoring file from archive${NC}"
echo -e "  Archived file: $ARCHIVED_FILE"
echo -e "  Original location: $ORIGINAL_LOCATION"

# Check if original location already exists
if [ -f "$ORIGINAL_LOCATION" ]; then
    echo -e "${YELLOW}Warning: File already exists at original location${NC}"
    read -p "Overwrite existing file? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Restoration cancelled${NC}"
        exit 0
    fi
fi

# Create directory if needed
mkdir -p "$(dirname "$ORIGINAL_LOCATION")"

# Extract original content (skip archive header)
awk '/\[Original file content follows\.\.\.\]/{flag=1; next} flag' "$ARCHIVED_FILE" > "$ORIGINAL_LOCATION"

# Verify restoration
if [ ! -f "$ORIGINAL_LOCATION" ]; then
    echo -e "${RED}Error: Failed to restore file${NC}"
    exit 1
fi

# Log restoration
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "| $(date '+%Y-%m-%d') | $(basename "$ORIGINAL_LOCATION") | /$ORIGINAL_LOCATION | Manual restoration | Restored from archive |" >> "$CLEANUP_LOG"

echo -e "${GREEN}✓ File successfully restored${NC}"
echo -e "  Restored to: ${BLUE}$ORIGINAL_LOCATION${NC}"
