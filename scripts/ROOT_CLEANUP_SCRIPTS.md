# Root Cleanup - Script Files

This document tracks script files moved from the project root during cleanup.

## 📁 Script Organization

### 🗄️ Database Scripts (`database/`)
- `database_init_alembic.py` - Initialize Alembic migrations
- `init_db.py` - Database initialization
- `run_migrations.py` - Run database migrations
- `check_table.py` - Database table verification
- `db_tasks.py` - Database task utilities

### 📊 Monitoring Scripts (`monitoring/`)
- `service_monitor.py` - Service monitoring utility
- `run-service-monitor.sh` - Service monitor runner
- `dashboard.py` - Dashboard application
- `turdparty-dashboard.sh` - Dashboard startup script
- `verify_consolidated_endpoints.py` - Endpoint verification
- `run_api_coverage.py` - API coverage analysis
- `analyze_coverage.py` - Coverage analysis utility

### 🚀 Deployment Scripts (`deployment/`)
- `build-script.sh` - Build automation script
- `push-docker-images.sh` - Docker image deployment
- `rebuild_containers.sh` - Container rebuild utility
- `docker_session.py` - Docker session management
- `setup_minio_env.sh` - MinIO environment setup
- `install-dashboard-deps.sh` - Dashboard dependencies installer

### 🖥️ Vagrant & VM Scripts (`vagrant/`)
- `vagrant_exec.sh` - Vagrant command execution
- `start-vagrant-proxy.sh` - Vagrant proxy startup
- `inject_file_scp.sh` - File injection via SCP
- `register_windows_template.py` - Windows template registration
- `update_templates.py` - Template update utility

### 🎨 UI Scripts (`ui/`)
- `textual_ui.py` - Textual UI implementation
- `selenium_capture_screenshots.py` - Screenshot automation
- `create_ui_links.sh` - UI link generation

### 🛠️ Development Scripts (`development/`)
- `check_connection.py` - Connection testing
- `create_user.py` - User creation utility
- `modify_auth_middleware.py` - Authentication middleware
- `openapi_proxy.py` - OpenAPI proxy
- `override_dependencies.py` - Dependency overrides
- `minio_ssh_wrapper_fixed.py` - MinIO SSH wrapper

### 🧹 Utility Scripts (`utilities/`)
- `cleanup-master-script.sh` - Master cleanup script
- `scan_ai_references.py` - AI reference scanner
- `run-streamlit-audit.sh` - Streamlit audit runner

## 📋 Files Remaining in Root

- `main.py` - Main application entry point (stays in root)

## 🔄 Usage Notes

All scripts maintain their original functionality but are now organized by purpose.
Update any references to these scripts in documentation or other files.
