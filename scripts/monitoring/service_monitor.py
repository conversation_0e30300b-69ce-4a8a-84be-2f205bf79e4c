#!/usr/bin/env python3
"""
TurdParty Service Monitor

A Rich-based dashboard for monitoring TurdParty services and their health status.
"""

import os
import sys
import time
import json
import subprocess
import threading
import datetime
import signal
import argparse
import socket
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union

# Rich imports
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from rich import box

# Docker imports
try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False

# Configuration
CONFIG_FILE = os.path.expanduser("~/.turdparty-service-monitor.json")
DEFAULT_CONFIG = {
    "refresh_interval": 3,
    "container_prefix": "turdparty",
    "services": [
        {
            "name": "API",
            "container": "turdparty_api_1",
            "url": "http://localhost:3050/api/v1/health",
            "port": 3050,
            "description": "FastAPI backend service"
        },
        {
            "name": "Frontend",
            "container": "turdparty_frontend_1",
            "url": "http://localhost:3100",
            "port": 3100,
            "description": "React frontend UI"
        },
        {
            "name": "Dashboard",
            "container": "turdparty_dashboard",
            "url": "http://localhost:3150",
            "port": 3150,
            "description": "Monitoring dashboard"
        },
        {
            "name": "PostgreSQL",
            "container": "turdparty_postgres_1",
            "port": 3200,
            "description": "PostgreSQL database"
        },
        {
            "name": "MinIO",
            "container": "turdparty_minio_1",
            "url": "http://localhost:3300/minio/health/live",
            "port": 3300,
            "description": "Object storage service"
        },
        {
            "name": "MinIO Console",
            "container": "turdparty_minio_1",
            "url": "http://localhost:3301",
            "port": 3301,
            "description": "MinIO web console"
        }
    ]
}

# Global variables
config = {}
stop_threads = False
console = Console()

def load_config() -> Dict:
    """Load configuration from file or use defaults."""
    global config
    config = DEFAULT_CONFIG.copy()
    
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            console.print(f"[bold red]Error loading config:[/] {e}")
    
    # Save config back (to create it if it doesn't exist)
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        console.print(f"[bold red]Error saving config:[/] {e}")
    
    return config

def check_port(host: str, port: int, timeout: float = 1.0) -> bool:
    """Check if a port is open on a host."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def check_url(url: str, timeout: float = 3.0) -> Tuple[bool, Optional[Dict]]:
    """Check if a URL is accessible and return response data if available."""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            try:
                return True, response.json()
            except ValueError:
                return True, {"content": "Non-JSON response"}
        return False, {"status_code": response.status_code}
    except requests.RequestException:
        return False, None

def get_container_status(container_name: str) -> Dict:
    """Get status of a Docker container."""
    if not DOCKER_AVAILABLE:
        return {"status": "unknown", "error": "Docker Python package not available"}
    
    try:
        client = docker.from_env()
        containers = client.containers.list(all=True, filters={"name": container_name})
        
        if not containers:
            return {"status": "not_found", "error": f"Container {container_name} not found"}
        
        container = containers[0]
        
        # Get basic info
        info = {
            "id": container.id[:12],
            "name": container.name,
            "status": container.status,
            "image": container.image.tags[0] if container.image.tags else container.image.id[:12],
        }
        
        # Get stats if container is running
        if container.status == "running":
            try:
                stats = container.stats(stream=False)
                
                # Calculate CPU usage
                cpu_delta = stats["cpu_stats"]["cpu_usage"]["total_usage"] - stats["precpu_stats"]["cpu_usage"]["total_usage"]
                system_delta = stats["cpu_stats"]["system_cpu_usage"] - stats["precpu_stats"]["system_cpu_usage"]
                cpu_percent = (cpu_delta / system_delta) * 100.0 * stats["cpu_stats"]["online_cpus"]
                
                # Calculate memory usage
                mem_usage = stats["memory_stats"]["usage"]
                mem_limit = stats["memory_stats"]["limit"]
                mem_percent = (mem_usage / mem_limit) * 100.0
                
                info["cpu_percent"] = cpu_percent
                info["mem_usage"] = mem_usage
                info["mem_limit"] = mem_limit
                info["mem_percent"] = mem_percent
            except Exception as e:
                info["stats_error"] = str(e)
        
        return info
    except Exception as e:
        return {"status": "error", "error": str(e)}

def check_service_health(service: Dict) -> Dict:
    """Check health of a service."""
    result = {
        "name": service["name"],
        "description": service.get("description", ""),
        "container": service.get("container", ""),
        "port": service.get("port"),
        "url": service.get("url"),
        "status": "unknown",
        "details": {},
        "last_checked": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Check container status if container name is provided
    if service.get("container"):
        container_status = get_container_status(service["container"])
        result["container_status"] = container_status
        
        if container_status.get("status") == "running":
            result["status"] = "running"
        elif container_status.get("status") == "not_found":
            result["status"] = "not_found"
        else:
            result["status"] = container_status.get("status", "unknown")
    
    # Check port if provided
    if service.get("port"):
        port_open = check_port("localhost", service["port"])
        result["port_open"] = port_open
        
        if not port_open and result["status"] == "running":
            result["status"] = "port_closed"
    
    # Check URL if provided
    if service.get("url"):
        url_accessible, response_data = check_url(service["url"])
        result["url_accessible"] = url_accessible
        result["response_data"] = response_data
        
        if url_accessible:
            result["status"] = "healthy"
        elif result["status"] == "running":
            result["status"] = "unhealthy"
    
    return result

def get_service_status_emoji(status: str) -> str:
    """Get emoji for service status."""
    status_emojis = {
        "healthy": "✅",
        "running": "🟡",
        "unhealthy": "🔴",
        "port_closed": "🔸",
        "not_found": "❓",
        "error": "⚠️",
        "unknown": "❔"
    }
    return status_emojis.get(status, "❔")

def get_service_status_color(status: str) -> str:
    """Get color for service status."""
    status_colors = {
        "healthy": "green",
        "running": "yellow",
        "unhealthy": "red",
        "port_closed": "orange1",
        "not_found": "grey50",
        "error": "red",
        "unknown": "grey70"
    }
    return status_colors.get(status, "grey70")

def create_services_table() -> Table:
    """Create a table showing service status."""
    table = Table(title="TurdParty Services", box=box.ROUNDED)
    
    table.add_column("Service", style="cyan", no_wrap=True)
    table.add_column("Status", style="white")
    table.add_column("Container", style="magenta")
    table.add_column("Port", justify="right", style="green")
    table.add_column("URL", style="blue")
    table.add_column("Description", style="yellow")
    
    for service in config["services"]:
        health = check_service_health(service)
        
        status_emoji = get_service_status_emoji(health["status"])
        status_color = get_service_status_color(health["status"])
        
        container_status = ""
        if "container_status" in health:
            if health["container_status"].get("status") == "running":
                container_status = f"[green]{health['container_status'].get('status')}[/]"
            else:
                container_status = f"[red]{health['container_status'].get('status')}[/]"
        
        port_status = ""
        if "port_open" in health:
            if health["port_open"]:
                port_status = f"[green]{health['port']}[/]"
            else:
                port_status = f"[red]{health['port']}[/]"
        
        url_status = ""
        if "url_accessible" in health:
            if health["url_accessible"]:
                url_status = f"[green]{health['url']}[/]"
            else:
                url_status = f"[red]{health['url']}[/]"
        
        table.add_row(
            health["name"],
            f"[{status_color}]{status_emoji} {health['status'].upper()}[/]",
            container_status or "-",
            port_status or str(health.get("port", "-")),
            url_status or health.get("url", "-"),
            health["description"]
        )
    
    return table

def main_rich_ui():
    """Main function for Rich UI."""
    load_config()
    
    console.print(Panel.fit(
        "[bold blue]TurdParty Service Monitor[/]\n"
        "[yellow]A Rich-based dashboard for monitoring TurdParty services[/]",
        border_style="green"
    ))
    
    try:
        with Live(create_services_table(), refresh_per_second=1/config["refresh_interval"]) as live:
            while True:
                live.update(create_services_table())
                time.sleep(config["refresh_interval"])
    except KeyboardInterrupt:
        console.print("\n[bold green]Exiting service monitor...[/]")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="TurdParty Service Monitor")
    parser.add_argument("--tui", action="store_true", help="Use Textual TUI instead of Rich UI")
    args = parser.parse_args()
    
    if args.tui:
        try:
            from textual_ui import TextualApp
            TextualApp().run()
        except ImportError:
            console.print("[bold red]Error:[/] Textual UI requires the textual package. Please install it with 'pip install textual'.")
            sys.exit(1)
    else:
        main_rich_ui()

if __name__ == "__main__":
    main()
