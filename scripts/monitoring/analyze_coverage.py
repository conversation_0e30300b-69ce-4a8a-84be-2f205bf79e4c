#!/usr/bin/env python3
"""
Test coverage analysis script for TurdParty.

This script analyzes coverage data and generates a report showing:
1. Overall coverage statistics
2. Coverage by module
3. Modules with low coverage
4. Uncovered lines in critical modules
"""

import os
import sys
import json
import argparse
from collections import defaultdict
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default paths
DEFAULT_COVERAGE_JSON = "test-reports/coverage/coverage.json"
DEFAULT_OUTPUT_DIR = "test-reports/analysis"

# Coverage thresholds
CRITICAL_THRESHOLD = 60  # Below this is critical
WARNING_THRESHOLD = 80   # Below this is warning
GOOD_THRESHOLD = 90      # Above this is good

# Critical modules that should have high coverage
CRITICAL_MODULES = [
    "api.routes.file_upload",
    "api.services.file_upload_service",
    "api.routes.minio_ssh_wrapper",
    "api.schemas.file_upload",
    "api.routes.vagrant_vm",
    "api.services.vagrant_vm_service"
]

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Analyze test coverage data")
    parser.add_argument(
        "--coverage-file",
        default=DEFAULT_COVERAGE_JSON,
        help=f"Path to coverage JSON file (default: {DEFAULT_COVERAGE_JSON})"
    )
    parser.add_argument(
        "--output-dir",
        default=DEFAULT_OUTPUT_DIR,
        help=f"Directory for output reports (default: {DEFAULT_OUTPUT_DIR})"
    )
    parser.add_argument(
        "--critical-threshold",
        type=float,
        default=CRITICAL_THRESHOLD,
        help=f"Critical coverage threshold percentage (default: {CRITICAL_THRESHOLD})"
    )
    parser.add_argument(
        "--warning-threshold",
        type=float,
        default=WARNING_THRESHOLD,
        help=f"Warning coverage threshold percentage (default: {WARNING_THRESHOLD})"
    )
    return parser.parse_args()

def load_coverage_data(coverage_file):
    """Load coverage data from JSON file."""
    try:
        with open(coverage_file, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"Coverage file not found: {coverage_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in coverage file: {coverage_file}")
        sys.exit(1)

def get_module_name(file_path):
    """Convert file path to module name."""
    # Remove .py extension
    if file_path.endswith('.py'):
        file_path = file_path[:-3]
    
    # Convert path separators to dots
    module_name = file_path.replace('/', '.')
    
    return module_name

def analyze_coverage(coverage_data):
    """Analyze coverage data and return statistics."""
    results = {
        'overall': coverage_data.get('totals', {}),
        'modules': {},
        'low_coverage': [],
        'uncovered_critical': []
    }
    
    # Process each file
    for file_path, file_data in coverage_data.get('files', {}).items():
        module_name = get_module_name(file_path)
        summary = file_data.get('summary', {})
        
        # Store module coverage
        results['modules'][module_name] = {
            'percent_covered': summary.get('percent_covered', 0),
            'covered_lines': summary.get('covered_lines', 0),
            'num_statements': summary.get('num_statements', 0),
            'missing_lines': summary.get('missing_lines', 0)
        }
        
        # Check if this is a low coverage module
        if summary.get('percent_covered', 0) < CRITICAL_THRESHOLD:
            results['low_coverage'].append({
                'module': module_name,
                'percent_covered': summary.get('percent_covered', 0),
                'missing_lines': summary.get('missing_lines', 0),
                'num_statements': summary.get('num_statements', 0)
            })
        
        # Check if this is a critical module with uncovered lines
        if module_name in CRITICAL_MODULES and summary.get('missing_lines', 0) > 0:
            results['uncovered_critical'].append({
                'module': module_name,
                'percent_covered': summary.get('percent_covered', 0),
                'missing_lines': file_data.get('missing_lines', []),
                'num_statements': summary.get('num_statements', 0)
            })
    
    return results

def generate_report(analysis, output_dir, args):
    """Generate a coverage analysis report."""
    os.makedirs(output_dir, exist_ok=True)
    report_file = os.path.join(output_dir, 'coverage_analysis.txt')
    
    with open(report_file, 'w') as f:
        # Report header
        f.write("=" * 80 + "\n")
        f.write(f"TurdParty Test Coverage Analysis\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        
        # Overall statistics
        overall = analysis['overall']
        f.write("OVERALL COVERAGE\n")
        f.write("-" * 80 + "\n")
        f.write(f"Total Lines: {overall.get('num_statements', 0)}\n")
        f.write(f"Covered Lines: {overall.get('covered_lines', 0)}\n")
        f.write(f"Missing Lines: {overall.get('missing_lines', 0)}\n")
        f.write(f"Coverage: {overall.get('percent_covered', 0):.1f}%\n\n")
        
        # Coverage by module (sorted by coverage percentage)
        f.write("COVERAGE BY MODULE\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'Module':<50} {'Coverage':<10} {'Lines':<10} {'Missing':<10}\n")
        
        sorted_modules = sorted(
            analysis['modules'].items(),
            key=lambda x: x[1]['percent_covered'],
            reverse=True
        )
        
        for module, data in sorted_modules:
            f.write(f"{module:<50} {data['percent_covered']:<10.1f} {data['num_statements']:<10} {data['missing_lines']:<10}\n")
        
        f.write("\n")
        
        # Low coverage modules
        if analysis['low_coverage']:
            f.write("LOW COVERAGE MODULES (Below Critical Threshold)\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'Module':<50} {'Coverage':<10} {'Lines':<10} {'Missing':<10}\n")
            
            for module in sorted(analysis['low_coverage'], key=lambda x: x['percent_covered']):
                f.write(f"{module['module']:<50} {module['percent_covered']:<10.1f} {module['num_statements']:<10} {module['missing_lines']:<10}\n")
            
            f.write("\n")
        
        # Uncovered lines in critical modules
        if analysis['uncovered_critical']:
            f.write("UNCOVERED LINES IN CRITICAL MODULES\n")
            f.write("-" * 80 + "\n")
            
            for module in analysis['uncovered_critical']:
                f.write(f"Module: {module['module']} ({module['percent_covered']:.1f}% covered)\n")
                f.write(f"Missing lines: {', '.join(map(str, module['missing_lines']))}\n\n")
    
    logger.info(f"Coverage analysis report generated: {report_file}")
    return report_file

def main():
    """Main entry point."""
    args = parse_args()
    
    # Load coverage data
    logger.info(f"Loading coverage data from {args.coverage_file}")
    coverage_data = load_coverage_data(args.coverage_file)
    
    # Analyze coverage
    logger.info("Analyzing coverage data")
    analysis = analyze_coverage(coverage_data)
    
    # Generate report
    logger.info(f"Generating coverage analysis report in {args.output_dir}")
    report_file = generate_report(analysis, args.output_dir, args)
    
    # Print summary to console
    overall = analysis['overall']
    print("\nCOVERAGE SUMMARY:")
    print(f"Total Lines: {overall.get('num_statements', 0)}")
    print(f"Covered Lines: {overall.get('covered_lines', 0)}")
    print(f"Coverage: {overall.get('percent_covered', 0):.1f}%")
    
    if analysis['low_coverage']:
        print(f"\nLow coverage modules: {len(analysis['low_coverage'])}")
    
    if analysis['uncovered_critical']:
        print(f"\nCritical modules with uncovered lines: {len(analysis['uncovered_critical'])}")
    
    print(f"\nDetailed report: {report_file}")

if __name__ == "__main__":
    main()
