#!/bin/bash

# Run the TurdParty Service Monitor using the existing shell.nix

# Check if nix-shell is available
if ! command -v nix-shell &> /dev/null; then
    echo "Error: nix-shell is not installed or not in your PATH."
    echo "Please install Nix package manager: https://nixos.org/download.html"
    exit 1
fi

# Default to Rich UI
UI_MODE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --tui)
            UI_MODE="--tui"
            shift
            ;;
        --help)
            echo "Usage: $0 [--tui] [--help]"
            echo ""
            echo "Options:"
            echo "  --tui   Use the Textual TUI interface instead of Rich UI"
            echo "  --help  Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information."
            exit 1
            ;;
    esac
done

# Run the service monitor in a nix-shell with required packages
nix-shell -p python310 python310Packages.rich python310Packages.textual python310Packages.docker python310Packages.requests python310Packages.psutil --run "python service_monitor.py $UI_MODE"
