#!/usr/bin/env python3
"""
TurdParty Docker Dashboard

A CLI dashboard for monitoring and managing Docker containers for the TurdParty application.
"""

print("Dashboard script is running...")

import os
import sys
import time
import json
import subprocess
import threading
import datetime
import signal
import click
import yaml
import urwid
import re
from pathlib import Path

print("Imports completed successfully")

# Configuration
CONFIG_FILE = os.path.expanduser("~/.turdparty-dashboard.json")
DEFAULT_CONFIG = {
    "refresh_interval": 3,
    "log_lines": 50,
    "docker_compose_file": os.path.join(os.path.dirname(os.path.abspath(__file__)), ".dockerwrapper/docker-compose.yml"),
    "project_name": "turdparty",
    "container_prefix": os.environ.get("CONTAINER_PREFIX", "turdparty"),
    "theme": {
        "header_bg": "dark blue",
        "header_fg": "white",
        "footer_bg": "dark blue",
        "footer_fg": "white",
        "table_header_bg": "dark blue",
        "table_header_fg": "white",
        "selected_bg": "dark blue",
        "selected_fg": "white",
        "running_fg": "dark green",
        "stopped_fg": "dark red",
        "created_fg": "yellow"
    }
}

print("Configuration loaded")

# Global variables
config = {}
containers_list = []
selected_container = None
logs_thread = None
stats_thread = None
stop_threads = False

# Docker command wrapper
def run_docker_command(command, capture_output=True):
    """Run a Docker command using subprocess."""
    try:
        # Check if we're running in a Docker container
        in_docker = os.path.exists('/.dockerenv')
        print(f"Running in Docker container: {in_docker}")
        
        # Prepare the command
        cmd = ["docker"]
        if isinstance(command, str):
            cmd.append(command)
        else:
            cmd.extend(command)
        
        print(f"Running command: {' '.join(cmd)}")
        
        # Run the command
        result = subprocess.run(
            cmd,
            capture_output=capture_output,
            text=True,
            check=False
        )
        
        print(f"Command return code: {result.returncode}")
        if result.stdout:
            print(f"Command stdout length: {len(result.stdout)} characters")
        
        if result.returncode != 0 and result.stderr:
            print(f"Error running Docker command: {result.stderr}")
            return None
        
        return result.stdout if capture_output else True
    except Exception as e:
        print(f"Error running Docker command: {e}")
        import traceback
        traceback.print_exc()
        return None

# Get container list
def get_containers():
    """Get a list of all containers."""
    print("Getting container list...")
    # Use a more detailed format to get restart count and status with uptime
    output = run_docker_command([
        "ps", "-a", 
        "--format", "{{.ID}}|{{.Names}}|{{.Status}}|{{.Image}}|{{.Ports}}|{{.RunningFor}}"
    ])
    if not output:
        print("No output from docker ps command")
        return []
    
    containers = []
    prefix = config.get("container_prefix", "turdparty")
    print(f"Using container prefix: {prefix}")
    
    for line in output.strip().split("\n"):
        if not line:
            continue
        
        parts = line.split("|")
        if len(parts) < 6:
            continue
        
        container_id, name, status, image, ports, running_for = parts
        
        # Only process containers with our prefix
        if not name.startswith(prefix):
            continue
            
        # Parse status and extract restart count
        restart_count = 0
        runtime = ""
        status_text = "created"
        
        if status.startswith("Up"):
            status_text = "running"
            runtime = running_for.strip()
            
            # Extract restart count if available
            restart_match = re.search(r'Restarting \((\d+)', status)
            if restart_match:
                restart_count = int(restart_match.group(1))
            else:
                restart_match = re.search(r'\((\d+)\)', status)
                if restart_match:
                    restart_count = int(restart_match.group(1))
        elif status.startswith("Exited"):
            status_text = "exited"
            # Extract exit code
            exit_match = re.search(r'Exited \((\d+)\)', status)
            if exit_match:
                exit_code = exit_match.group(1)
                status_text = f"exited ({exit_code})"
        
        # Parse ports
        ports_list = []
        for port in ports.split(","):
            if "->" in port:
                host, container = port.split("->")
                ports_list.append(f"{host.strip()}->{container.strip()}")
            elif port.strip():
                ports_list.append(port.strip())
        
        containers.append({
            "id": container_id,
            "name": name,
            "status": status_text,
            "image": image.replace("dockerwrapper-", f"{prefix}-"),
            "ports": ports_list,
            "restart_count": restart_count,
            "runtime": runtime
        })
    
    print(f"Found {len(containers)} containers")
    return containers

# Find container by name or partial name
def find_container(name):
    """Find a container by name or partial name."""
    containers = get_containers()
    
    # First try exact match
    for container in containers:
        if container["name"] == name:
            return container
    
    # Then try partial match (container name contains the search term)
    for container in containers:
        if name in container["name"]:
            return container
    
    # Then try partial match (search term contains the container name)
    for container in containers:
        if container["name"] in name:
            return container
    
    return None

# Get container logs
def get_container_logs(container_name, lines=50):
    """Get logs for a container."""
    container = find_container(container_name)
    if not container:
        print(f"Container {container_name} not found")
        return None
    
    print(f"Getting logs for container {container['name']}")
    
    # Use bash -c to capture stderr as well
    cmd = ["bash", "-c", f"docker logs {container['name']} --tail {lines} 2>&1"]
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=False
        )
        
        print(f"Command return code: {result.returncode}")
        if result.stdout:
            print(f"Command stdout length: {len(result.stdout)} characters")
        
        if result.returncode != 0 and result.stderr:
            print(f"Error running Docker command: {result.stderr}")
            return None
        
        return result.stdout
    except Exception as e:
        print(f"Error running Docker command: {e}")
        import traceback
        traceback.print_exc()
        return None

# Get container stats
def get_container_stats(container_name):
    """Get stats for a container."""
    container = find_container(container_name)
    if not container:
        print(f"Container {container_name} not found")
        return "No stats available"
    
    output = run_docker_command(["stats", "--no-stream", "--format", "{{.CPUPerc}}|{{.MemUsage}}|{{.NetIO}}|{{.BlockIO}}", container["name"]])
    if not output:
        return "No stats available"
    
    parts = output.strip().split("|")
    if len(parts) < 4:
        return "Invalid stats format"
    
    cpu, mem, net, block = parts
    
    return f"CPU: {cpu}\nMemory: {mem}\nNetwork I/O: {net}\nBlock I/O: {block}"

# Start a container
def start_container(container_name):
    """Start a container."""
    container = find_container(container_name)
    if not container:
        print(f"Container {container_name} not found")
        return False
    
    return run_docker_command(["start", container["name"]], capture_output=False)

# Stop a container
def stop_container(container_name):
    """Stop a container."""
    container = find_container(container_name)
    if not container:
        print(f"Container {container_name} not found")
        return False
    
    return run_docker_command(["stop", container["name"]], capture_output=False)

# Restart a container
def restart_container(container_name):
    """Restart a container."""
    container = find_container(container_name)
    if not container:
        print(f"Container {container_name} not found")
        return False
    
    return run_docker_command(["restart", container["name"]], capture_output=False)

# Start all containers
def start_all_containers():
    """Start all containers."""
    containers = get_containers()
    for container in containers:
        if container["status"] != "running":
            start_container(container["name"])
    return True

# Stop all containers
def stop_all_containers():
    """Stop all containers."""
    containers = get_containers()
    for container in containers:
        if container["status"] == "running":
            stop_container(container["name"])
    return True

# Load configuration
def load_config():
    global config
    config = DEFAULT_CONFIG.copy()
    
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            print(f"Error loading config: {e}")
    
    # Save config back (to create it if it doesn't exist)
    save_config()
    
    return config

# Save configuration
def save_config():
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        print(f"Error saving config: {e}")

# CLI Commands
@click.group()
def cli():
    """TurdParty Docker Dashboard CLI."""
    # Load configuration
    load_config()

@cli.command()
def start():
    """Start the interactive dashboard UI."""
    click.echo("Starting interactive dashboard UI...")
    # This would normally start the interactive UI
    # For now, just list containers
    containers = get_containers()
    if not containers:
        click.echo("No containers found.")
        return
    
    click.echo("Containers:")
    for container in containers:
        status_color = "green" if container["status"] == "running" else "red"
        click.echo(f"{container['name']} - {click.style(container['status'], fg=status_color)} - {container['image']}")

@cli.command()
def list():
    """List all containers."""
    containers = get_containers()
    if not containers:
        click.echo("No containers found.")
        return
    
    # Print header
    click.echo(f"{'NAME'.ljust(30)} {'STATUS'.ljust(10)} {'RESTARTS'.ljust(10)} {'RUNTIME'.ljust(15)} {'IMAGE'.ljust(30)} {'PORTS'}")
    click.echo("-" * 120)
    
    # Print containers
    for container in containers:
        status_color = "green" if "running" in container["status"] else "red"
        ports = ", ".join(container["ports"]) if container["ports"] else "none"
        restarts = str(container["restart_count"]) if "restart_count" in container else "0"
        runtime = container["runtime"] if "runtime" in container and container["runtime"] else "-"
        
        click.echo(
            f"{container['name'].ljust(30)} "
            f"{click.style(container['status'].ljust(10), fg=status_color)} "
            f"{restarts.ljust(10)} "
            f"{runtime.ljust(15)} "
            f"{container['image'].ljust(30)} "
            f"{ports}"
        )

@cli.command()
def up():
    """Start all containers."""
    click.echo("Starting all containers...")
    if start_all_containers():
        click.echo("All containers started successfully.")
    else:
        click.echo("Failed to start all containers.")

@cli.command()
def down():
    """Stop all containers."""
    click.echo("Stopping all containers...")
    if stop_all_containers():
        click.echo("All containers stopped successfully.")
    else:
        click.echo("Failed to stop all containers.")

@cli.command()
@click.argument("container")
def restart(container):
    """Restart a specific container."""
    click.echo(f"Restarting container {container}...")
    if restart_container(container):
        click.echo(f"Container {container} restarted successfully.")
    else:
        click.echo(f"Failed to restart container {container}.")

@cli.command()
@click.argument("container")
@click.option("--lines", "-n", default=50, help="Number of log lines to show.")
def logs(container, lines):
    """View logs for a specific container."""
    container_obj = find_container(container)
    if not container_obj:
        click.echo(f"Container {container} not found.")
        return
    
    click.echo(f"Logs for container {container_obj['name']}:")
    logs = get_container_logs(container_obj['name'], lines)
    if logs:
        click.echo(logs)
    else:
        click.echo(f"No logs found for container {container_obj['name']}.")

@cli.command()
@click.argument("container")
def stats(container):
    """View stats for a specific container."""
    container_obj = find_container(container)
    if not container_obj:
        click.echo(f"Container {container} not found.")
        return
    
    click.echo(f"Stats for container {container_obj['name']}:")
    stats = get_container_stats(container_obj['name'])
    click.echo(stats)

@cli.command()
def config():
    """View or edit configuration."""
    click.echo("Current configuration:")
    click.echo(json.dumps(config, indent=2))
    click.echo("\nTo edit the configuration, edit the file:")
    click.echo(CONFIG_FILE)

if __name__ == "__main__":
    cli() 