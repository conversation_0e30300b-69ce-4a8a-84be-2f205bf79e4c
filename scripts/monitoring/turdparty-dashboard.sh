#!/bin/bash

# TurdParty Docker Dashboard Launcher
# This script installs dependencies if needed and launches the dashboard

# Change to the script directory
cd "$(dirname "$0")"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js to use this dashboard."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "npm is not installed. Please install npm to use this dashboard."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo "Failed to install dependencies. Please check your npm configuration."
        exit 1
    fi
fi

# Make the dashboard script executable
chmod +x docker-dashboard.js

# Run the dashboard
node docker-dashboard.js 