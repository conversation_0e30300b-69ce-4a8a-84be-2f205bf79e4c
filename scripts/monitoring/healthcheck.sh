#!/bin/bash

# TurdParty Health Check Script
# Comprehensive health verification after root folder cleanup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to print status
print_status() {
    local status=$1
    local message=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $message"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC}: $message"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  WARN${NC}: $message"
    else
        echo -e "${BLUE}ℹ️  INFO${NC}: $message"
    fi
}

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local name=$2
    local expected_status=${3:-200}
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        print_status "PASS" "$name endpoint responding"
        return 0
    else
        print_status "FAIL" "$name endpoint not responding"
        return 1
    fi
}

# Function to check container health
check_container() {
    local container_name=$1
    local service_name=$2
    
    if docker ps --filter "name=$container_name" --filter "status=running" | grep -q "$container_name"; then
        print_status "PASS" "$service_name container running"
        
        # Check health status if available
        health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "no-health-check")
        if [ "$health_status" = "healthy" ]; then
            print_status "PASS" "$service_name container healthy"
        elif [ "$health_status" = "no-health-check" ]; then
            print_status "INFO" "$service_name container (no health check configured)"
        else
            print_status "WARN" "$service_name container health: $health_status"
        fi
        return 0
    else
        print_status "FAIL" "$service_name container not running"
        return 1
    fi
}

echo -e "${BLUE}🔍 TurdParty Health Check - Post Cleanup Verification${NC}"
echo "=================================================================="
echo

# Phase 1: Container Status
echo -e "${BLUE}📦 Phase 1: Container Status${NC}"
echo "--------------------------------"

check_container "turdparty_api" "API"
check_container "turdparty_postgres" "PostgreSQL"
check_container "turdparty_redis" "Redis"
check_container "turdparty_minio" "MinIO"
check_container "turdparty_minio_ssh" "MinIO SSH"
check_container "turdparty_celery_default" "Celery Default Worker"
check_container "turdparty_celery_file_ops" "Celery File Ops Worker"
check_container "turdparty_celery_vm_ops" "Celery VM Ops Worker"
check_container "turdparty_celery_flower" "Celery Flower"

echo

# Phase 2: Service Endpoints
echo -e "${BLUE}🌐 Phase 2: Service Endpoints${NC}"
echo "--------------------------------"

check_http "http://localhost:3050/api/v1/health" "API Health"
check_http "http://localhost:3300/minio/health/live" "MinIO Health"
check_http "http://localhost:3450/api/workers" "Celery Flower API"

echo

# Phase 3: Database Connectivity
echo -e "${BLUE}🗄️  Phase 3: Database Connectivity${NC}"
echo "--------------------------------"

if docker exec turdparty_postgres pg_isready -U postgres >/dev/null 2>&1; then
    print_status "PASS" "PostgreSQL accepting connections"
else
    print_status "FAIL" "PostgreSQL not accepting connections"
fi

if docker exec turdparty_redis redis-cli ping >/dev/null 2>&1; then
    print_status "PASS" "Redis responding to ping"
else
    print_status "FAIL" "Redis not responding to ping"
fi

echo

# Phase 4: File Structure Verification
echo -e "${BLUE}📁 Phase 4: File Structure Verification${NC}"
echo "--------------------------------"

# Check essential root files
essential_files=("main.py" "README.md" "CHANGELOG.md" "ROADMAP.md" "SUMMARY.md" "Vagrantfile" ".gitignore")
for file in "${essential_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "PASS" "Essential file exists: $file"
    else
        print_status "FAIL" "Missing essential file: $file"
    fi
done

# Check organized directories
organized_dirs=("tests" "docs" "scripts" "config" "api" "logs")
for dir in "${organized_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status "PASS" "Organized directory exists: $dir"
    else
        print_status "FAIL" "Missing organized directory: $dir"
    fi
done

echo

# Phase 5: Configuration Files
echo -e "${BLUE}⚙️  Phase 5: Configuration Files${NC}"
echo "--------------------------------"

config_files=(
    "config/docker/docker-compose.yml"
    "config/python/pyproject.toml"
    "config/node/package.json"
    "config/shell.nix"
)

for config in "${config_files[@]}"; do
    if [ -f "$config" ]; then
        print_status "PASS" "Config file exists: $config"
    else
        print_status "FAIL" "Missing config file: $config"
    fi
done

echo

# Summary
echo "=================================================================="
echo -e "${BLUE}📊 HEALTH CHECK SUMMARY${NC}"
echo "=================================================================="
echo -e "Total Checks: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL CHECKS PASSED! TurdParty is healthy and ready for production!${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  $FAILED_CHECKS checks failed. Please review the issues above.${NC}"
    exit 1
fi
