#!/usr/bin/env python3
"""Run API tests with coverage."""

import os
import sys
import subprocess
import argparse
from datetime import datetime

def log(message):
    """Print message with timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_tests_with_coverage():
    """Run tests with coverage."""
    log("Running API tests with coverage...")
    
    # Create coverage directory
    os.makedirs("coverage_reports", exist_ok=True)
    
    # Run unittest directly with coverage
    cmd = [
        "coverage", "run", "-m", "unittest",
        "test_api_coverage.py"
    ]
    
    try:
        log(f"Running command: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
        
        # Generate coverage reports
        log("Generating coverage reports...")
        subprocess.run(["coverage", "report"], check=False)
        subprocess.run(["coverage", "html", "-d", "coverage_reports/html"], check=False)
        subprocess.run(["coverage", "xml", "-o", "coverage_reports/coverage.xml"], check=False)
        
        log("Tests completed successfully. Coverage reports available in 'coverage_reports' directory.")
    except subprocess.CalledProcessError as e:
        log(f"Error running tests: {e}")
        sys.exit(1)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run API tests with coverage")
    parser.add_argument("--html-only", action="store_true", help="Generate only HTML report")
    args = parser.parse_args()
    
    if args.html_only:
        log("Generating HTML coverage report only...")
        cmd = ["coverage", "html", "-d", "coverage_reports/html"]
        subprocess.run(cmd, check=True)
    else:
        run_tests_with_coverage()

if __name__ == "__main__":
    main() 