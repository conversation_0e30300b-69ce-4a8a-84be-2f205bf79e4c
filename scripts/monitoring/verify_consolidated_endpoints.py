#!/usr/bin/env python3
"""
Script to verify that the consolidated VM endpoints are properly defined.
"""
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.getcwd())

try:
    # Import the router from the consolidated_vagrant module
    from api.routes.consolidated_vagrant import router

    # Print the router prefix
    print(f"Router prefix: {router.prefix}")

    # Get all the routes
    routes = [route.path for route in router.routes]

    # Print all routes
    print("\nRoutes defined in the router:")
    for route in router.routes:
        print(f"  {route.path}")

    # Check that the expected endpoints are defined
    # Note: The router has a prefix, so we need to check for the endpoints without the prefix
    expected_endpoints = [
        "/",
        "/{vm_id}",
        "/{vm_id}/status",
        "/{vm_id}/info",
        "/{vm_id}/start",
        "/{vm_id}/stop",
        "/{vm_id}/destroy",
        "/{vm_id}/execute",
        "/boxes",
        "/templates",
        "/connection",
        "/injections",
        "/injections/{injection_id}",
        "/injections/{injection_id}/status"
    ]

    # Check if all expected endpoints are defined
    # We need to account for the router prefix
    missing_endpoints = []
    for endpoint in expected_endpoints:
        prefixed_endpoint = router.prefix + endpoint
        if prefixed_endpoint not in routes:
            missing_endpoints.append(endpoint)

    if missing_endpoints:
        print(f"ERROR: The following endpoints are missing: {missing_endpoints}")
        sys.exit(1)
    else:
        print("SUCCESS: All expected endpoints are defined in the consolidated_vagrant router.")

    # Try to import the application to check the redirect routes
    try:
        from api.application import app

        # Get all the routes
        app_routes = [route.path for route in app.routes]

        # Check that the expected redirect routes are defined
        expected_redirect_routes = [
            "/api/v1/vagrant/{path:path}",
            "/api/v1/vagrant_vm/{path:path}",
            "/api/v1/vagrant-vm/{path:path}",
            "/api/v1/vm_injection/{path:path}",
            "/api/v1/vm-injection/{path:path}",
            "/api/v1/vagrant",
            "/api/v1/vagrant_vm",
            "/api/v1/vagrant-vm",
            "/api/v1/vm_injection",
            "/api/v1/vm-injection"
        ]

        # Check if all expected redirect routes are defined
        missing_redirect_routes = [route for route in expected_redirect_routes if route not in app_routes]

        if missing_redirect_routes:
            print(f"WARNING: The following redirect routes are missing: {missing_redirect_routes}")
            print("This may be because the application is not fully initialized.")
        else:
            print("SUCCESS: All expected redirect routes are defined in the application.")

    except ImportError as e:
        print(f"WARNING: Could not import the application to check redirect routes: {e}")

    print("\nVerification completed successfully.")
    sys.exit(0)

except ImportError as e:
    print(f"ERROR: Could not import the consolidated_vagrant router: {e}")
    sys.exit(1)
except Exception as e:
    print(f"ERROR: An unexpected error occurred: {e}")
    sys.exit(1)
