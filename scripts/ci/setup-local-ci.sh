#!/bin/bash

# Setup Local CI/CD Environment for TurdParty
# Installs pre-commit hooks and local development tools

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Setting up Local CI/CD Environment for TurdParty${NC}"
echo "=================================================================="

# Function to print status
print_status() {
    local status=$1
    local message=$2
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅${NC} $message"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️${NC} $message"
    else
        echo -e "${BLUE}ℹ️${NC} $message"
    fi
}

# Create CI directory structure
echo "Creating CI directory structure..."
mkdir -p scripts/ci/hooks
mkdir -p scripts/ci/templates
mkdir -p logs/ci

print_status "PASS" "CI directories created"

# Create pre-commit hook
echo "Setting up pre-commit hook..."
cat > scripts/ci/hooks/pre-commit << 'EOF'
#!/bin/bash

# TurdParty Pre-commit Hook
# Runs quick checks before allowing commits

set -e

echo "🔍 Running pre-commit checks..."

# Quick syntax check
echo "Checking Python syntax..."
if find api/ -name "*.py" -exec python3 -m py_compile {} \; 2>/dev/null; then
    echo "✅ Python syntax OK"
else
    echo "❌ Python syntax errors found"
    exit 1
fi

# Check for debug statements
echo "Checking for debug statements..."
if find api/ -name "*.py" -exec grep -l "print(" {} \; | head -1 >/dev/null; then
    echo "⚠️  Warning: Debug print statements found"
    echo "Continue anyway? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check for sensitive data
echo "Checking for sensitive data..."
if grep -r -i "password.*=" api/ --include="*.py" | grep -v "# " | head -1 >/dev/null; then
    echo "⚠️  Warning: Potential sensitive data found"
    echo "Continue anyway? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "✅ Pre-commit checks passed"
EOF

chmod +x scripts/ci/hooks/pre-commit

# Install pre-commit hook
if [ -d ".git" ]; then
    cp scripts/ci/hooks/pre-commit .git/hooks/pre-commit
    print_status "PASS" "Pre-commit hook installed"
else
    print_status "WARN" "Not a git repository - pre-commit hook not installed"
fi

# Create commit message template
echo "Creating commit message template..."
cat > scripts/ci/templates/commit-template.txt << 'EOF'
# <type>: <description>
#
# <body>
#
# <footer>

# Type can be:
# feat: A new feature
# fix: A bug fix
# docs: Documentation only changes
# style: Changes that do not affect the meaning of the code
# refactor: A code change that neither fixes a bug nor adds a feature
# perf: A code change that improves performance
# test: Adding missing tests or correcting existing tests
# chore: Changes to the build process or auxiliary tools

# Examples:
# feat: add user authentication system
# fix: resolve database connection timeout
# docs: update API documentation
# refactor: optimize file upload performance
EOF

# Create local development script
echo "Creating local development helper..."
cat > scripts/ci/dev-workflow.sh << 'EOF'
#!/bin/bash

# Local Development Workflow Helper
# Common development tasks for TurdParty

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

case "$1" in
    "start")
        echo -e "${BLUE}🚀 Starting TurdParty development environment...${NC}"
        cd .dockerwrapper && docker compose up -d
        echo -e "${GREEN}✅ Services started${NC}"
        echo "API: http://localhost:3050"
        echo "MinIO: http://localhost:3301"
        echo "Flower: http://localhost:3450"
        ;;
    "stop")
        echo -e "${BLUE}🛑 Stopping TurdParty services...${NC}"
        cd .dockerwrapper && docker compose down
        echo -e "${GREEN}✅ Services stopped${NC}"
        ;;
    "restart")
        echo -e "${BLUE}🔄 Restarting TurdParty services...${NC}"
        cd .dockerwrapper && docker compose restart
        echo -e "${GREEN}✅ Services restarted${NC}"
        ;;
    "logs")
        echo -e "${BLUE}📋 Showing service logs...${NC}"
        cd .dockerwrapper && docker compose logs -f
        ;;
    "health")
        echo -e "${BLUE}🏥 Running health check...${NC}"
        ./scripts/monitoring/healthcheck.sh
        ;;
    "ci")
        echo -e "${BLUE}🔍 Running local CI pipeline...${NC}"
        ./scripts/ci/local-ci.sh
        ;;
    "test")
        echo -e "${BLUE}🧪 Running tests...${NC}"
        ./scripts/testing/post-cleanup-test.sh
        ;;
    "clean")
        echo -e "${BLUE}🧹 Cleaning up containers and volumes...${NC}"
        cd .dockerwrapper && docker compose down -v
        docker system prune -f
        echo -e "${GREEN}✅ Cleanup complete${NC}"
        ;;
    *)
        echo "TurdParty Development Workflow Helper"
        echo "Usage: $0 {start|stop|restart|logs|health|ci|test|clean}"
        echo ""
        echo "Commands:"
        echo "  start   - Start all services"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show service logs"
        echo "  health  - Run health check"
        echo "  ci      - Run local CI pipeline"
        echo "  test    - Run test suite"
        echo "  clean   - Clean up containers and volumes"
        ;;
esac
EOF

chmod +x scripts/ci/dev-workflow.sh

# Create local CI configuration
echo "Creating local CI configuration..."
cat > scripts/ci/local-ci.conf << 'EOF'
# Local CI/CD Configuration for TurdParty

# Service endpoints
API_URL=http://localhost:3050
MINIO_URL=http://localhost:3300
FLOWER_URL=http://localhost:3450

# Docker configuration
DOCKER_PREFIX=turdparty
COMPOSE_FILE=.dockerwrapper/docker-compose.yml

# Test configuration
COVERAGE_THRESHOLD=80
TIMEOUT=300

# Quality gates
MAX_RESPONSE_TIME=2.0
MIN_DOC_FILES=10

# Notification settings (optional)
SLACK_WEBHOOK_URL=""
EMAIL_NOTIFICATIONS=""
EOF

# Create Makefile for common tasks
echo "Creating Makefile..."
cat > Makefile << 'EOF'
# TurdParty Local Development Makefile

.PHONY: help start stop restart logs health ci test clean setup

help: ## Show this help message
	@echo "TurdParty Development Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'

setup: ## Setup local CI/CD environment
	@./scripts/ci/setup-local-ci.sh

start: ## Start all services
	@./scripts/ci/dev-workflow.sh start

stop: ## Stop all services
	@./scripts/ci/dev-workflow.sh stop

restart: ## Restart all services
	@./scripts/ci/dev-workflow.sh restart

logs: ## Show service logs
	@./scripts/ci/dev-workflow.sh logs

health: ## Run health check
	@./scripts/ci/dev-workflow.sh health

ci: ## Run local CI pipeline
	@./scripts/ci/dev-workflow.sh ci

test: ## Run test suite
	@./scripts/ci/dev-workflow.sh test

clean: ## Clean up containers and volumes
	@./scripts/ci/dev-workflow.sh clean

# Development shortcuts
dev-start: start health ## Start services and run health check
dev-test: ci test ## Run CI pipeline and tests
dev-deploy: ci ## Run full CI pipeline before deployment
EOF

print_status "PASS" "Makefile created"

# Set up git configuration (if in git repo)
if [ -d ".git" ]; then
    echo "Configuring git settings..."
    git config commit.template scripts/ci/templates/commit-template.txt
    print_status "PASS" "Git commit template configured"
fi

echo
echo "=================================================================="
echo -e "${GREEN}🎉 Local CI/CD Setup Complete!${NC}"
echo "=================================================================="
echo
echo "Available commands:"
echo -e "${BLUE}make help${NC}     - Show all available commands"
echo -e "${BLUE}make start${NC}    - Start development environment"
echo -e "${BLUE}make ci${NC}       - Run local CI pipeline"
echo -e "${BLUE}make health${NC}   - Run health checks"
echo -e "${BLUE}make test${NC}     - Run test suite"
echo
echo "Or use the workflow script directly:"
echo -e "${BLUE}./scripts/ci/dev-workflow.sh start${NC}"
echo
echo -e "${GREEN}✅ Ready for local development with CI/CD!${NC}"
