#!/bin/bash

# TurdParty Pre-commit Hook
# Runs quick checks before allowing commits

set -e

echo "🔍 Running pre-commit checks..."

# Quick syntax check
echo "Checking Python syntax..."
if find api/ -name "*.py" -exec python3 -m py_compile {} \; 2>/dev/null; then
    echo "✅ Python syntax OK"
else
    echo "❌ Python syntax errors found"
    exit 1
fi

# Check for debug statements
echo "Checking for debug statements..."
if find api/ -name "*.py" -exec grep -l "print(" {} \; | head -1 >/dev/null; then
    echo "⚠️  Warning: Debug print statements found"
    echo "Continue anyway? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check for sensitive data
echo "Checking for sensitive data..."
if grep -r -i "password.*=" api/ --include="*.py" | grep -v "# " | head -1 >/dev/null; then
    echo "⚠️  Warning: Potential sensitive data found"
    echo "Continue anyway? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "✅ Pre-commit checks passed"
