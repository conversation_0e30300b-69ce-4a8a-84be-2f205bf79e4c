#!/bin/bash

# Local CI/CD Pipeline for TurdParty
# Runs comprehensive checks locally before commits/deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="TurdParty"
DOCKER_PREFIX="turdparty"
COVERAGE_THRESHOLD=80
TIMEOUT=300

# Counters
TOTAL_STAGES=0
PASSED_STAGES=0
FAILED_STAGES=0

# Function to print stage header
print_stage() {
    local stage_name=$1
    TOTAL_STAGES=$((TOTAL_STAGES + 1))
    echo
    echo "=================================================================="
    echo -e "${BLUE}🔄 Stage $TOTAL_STAGES: $stage_name${NC}"
    echo "=================================================================="
}

# Function to print status
print_status() {
    local status=$1
    local message=$2
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $message"
        PASSED_STAGES=$((PASSED_STAGES + 1))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC}: $message"
        FAILED_STAGES=$((FAILED_STAGES + 1))
        return 1
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  WARN${NC}: $message"
    else
        echo -e "${PURPLE}ℹ️  INFO${NC}: $message"
    fi
}

# Function to run command with timeout
run_with_timeout() {
    local cmd="$1"
    local desc="$2"
    local timeout_duration=${3:-$TIMEOUT}
    
    echo -e "${BLUE}Running:${NC} $desc"
    if timeout $timeout_duration bash -c "$cmd"; then
        print_status "PASS" "$desc"
        return 0
    else
        print_status "FAIL" "$desc (timeout: ${timeout_duration}s)"
        return 1
    fi
}

# Stage 1: Environment Setup
print_stage "Environment Setup & Validation"

# Check required tools
echo "Checking required tools..."
for tool in docker curl; do
    if command -v $tool >/dev/null 2>&1; then
        print_status "PASS" "$tool is available"
    else
        print_status "FAIL" "$tool is not installed"
    fi
done

# Check containerized tools
echo "Checking containerized tools..."
if docker exec ${DOCKER_PREFIX}_api python3 --version >/dev/null 2>&1; then
    print_status "PASS" "python3 available in container"
else
    print_status "WARN" "python3 not available in container"
fi

if docker exec ${DOCKER_PREFIX}_api which node >/dev/null 2>&1; then
    print_status "PASS" "node available in container"
else
    print_status "WARN" "node not available in container (optional)"
fi

# Check project structure
echo "Validating project structure..."
required_dirs=("api" "tests" "docs" "scripts" "config")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status "PASS" "Directory exists: $dir"
    else
        print_status "FAIL" "Missing directory: $dir"
    fi
done

# Stage 2: Code Quality Checks
print_stage "Code Quality & Linting"

# Python code quality (using container)
echo "Running Python code quality checks..."

# Check Python syntax in container
if docker exec ${DOCKER_PREFIX}_api find /app/api -name "*.py" -exec python3 -m py_compile {} \; 2>/dev/null; then
    print_status "PASS" "Python syntax check"
else
    print_status "WARN" "Python syntax issues found"
fi

# Check for common issues
if find api/ -name "*.py" -exec grep -l "print(" {} \; | head -1 >/dev/null; then
    print_status "WARN" "Debug print statements found in code"
else
    print_status "PASS" "No debug print statements"
fi

# JavaScript/Node.js quality (if available)
if command -v node >/dev/null 2>&1 && [ -f "config/node/package.json" ]; then
    echo "Running JavaScript code quality checks..."
    
    # Check JSON syntax
    if node -e "require('./config/node/package.json')" 2>/dev/null; then
        print_status "PASS" "package.json syntax"
    else
        print_status "FAIL" "package.json syntax error"
    fi
fi

# Stage 3: Security Checks
print_stage "Security Scanning"

echo "Running security checks..."

# Check for sensitive data in code
if grep -r -i "password\|secret\|key" api/ --include="*.py" | grep -v "# " | head -1 >/dev/null; then
    print_status "WARN" "Potential sensitive data in code (review manually)"
else
    print_status "PASS" "No obvious sensitive data in code"
fi

# Check Docker security
if [ -f "config/docker/Dockerfile" ]; then
    if grep -q "USER" config/docker/Dockerfile; then
        print_status "PASS" "Dockerfile uses non-root user"
    else
        print_status "WARN" "Dockerfile may run as root"
    fi
fi

# Stage 4: Service Health Checks
print_stage "Service Health Verification"

echo "Running comprehensive health checks..."
if ./scripts/monitoring/healthcheck.sh >/dev/null 2>&1; then
    print_status "PASS" "All services healthy"
else
    print_status "FAIL" "Service health check failed"
fi

# Stage 5: API Testing
print_stage "API Integration Testing"

echo "Testing API endpoints..."

# Test API health
if curl -s -f http://localhost:3050/api/v1/health >/dev/null; then
    print_status "PASS" "API health endpoint"
else
    print_status "FAIL" "API health endpoint not responding"
fi

# Test API documentation
if curl -s -f http://localhost:3050/docs >/dev/null 2>&1; then
    print_status "PASS" "API documentation accessible"
else
    print_status "WARN" "API documentation not accessible"
fi

# Stage 6: Database Tests
print_stage "Database Integration Testing"

echo "Testing database connectivity..."

# Test PostgreSQL
if docker exec ${DOCKER_PREFIX}_postgres pg_isready -U postgres >/dev/null 2>&1; then
    print_status "PASS" "PostgreSQL connectivity"
else
    print_status "FAIL" "PostgreSQL connectivity"
fi

# Test Redis
if docker exec ${DOCKER_PREFIX}_redis redis-cli ping >/dev/null 2>&1; then
    print_status "PASS" "Redis connectivity"
else
    print_status "FAIL" "Redis connectivity"
fi

# Stage 7: File Upload & Storage Tests
print_stage "File Upload & Storage Testing"

echo "Testing MinIO and file operations..."

# Test MinIO health
if curl -s -f http://localhost:3300/minio/health/live >/dev/null; then
    print_status "PASS" "MinIO health check"
else
    print_status "FAIL" "MinIO health check"
fi

# Stage 8: Performance Checks
print_stage "Performance Validation"

echo "Running performance checks..."

# Check API response time
api_response_time=$(curl -s -w "%{time_total}" -o /dev/null http://localhost:3050/api/v1/health)
if (( $(echo "$api_response_time < 2.0" | bc -l) )); then
    print_status "PASS" "API response time: ${api_response_time}s"
else
    print_status "WARN" "API response time slow: ${api_response_time}s"
fi

# Check container resource usage
echo "Checking container resource usage..."
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep ${DOCKER_PREFIX} | while read line; do
    print_status "INFO" "Resource usage: $line"
done

# Stage 9: Documentation Validation
print_stage "Documentation Validation"

echo "Validating documentation..."

# Check README exists and has content
if [ -f "README.md" ] && [ -s "README.md" ]; then
    print_status "PASS" "README.md exists and has content"
else
    print_status "FAIL" "README.md missing or empty"
fi

# Check documentation structure
doc_count=$(find docs/ -name "*.md" | wc -l)
if [ "$doc_count" -gt 10 ]; then
    print_status "PASS" "Documentation comprehensive ($doc_count files)"
else
    print_status "WARN" "Limited documentation ($doc_count files)"
fi

# Final Summary
echo
echo "=================================================================="
echo -e "${BLUE}📊 LOCAL CI/CD PIPELINE SUMMARY${NC}"
echo "=================================================================="
echo -e "Total Stages: ${BLUE}$TOTAL_STAGES${NC}"
echo -e "Passed: ${GREEN}$PASSED_STAGES${NC}"
echo -e "Failed: ${RED}$FAILED_STAGES${NC}"

if [ $FAILED_STAGES -eq 0 ]; then
    echo
    echo -e "${GREEN}🎉 ALL STAGES PASSED! Ready for deployment!${NC}"
    echo -e "${GREEN}✅ $PROJECT_NAME is production-ready!${NC}"
    exit 0
else
    echo
    echo -e "${RED}⚠️  $FAILED_STAGES stages failed. Please fix issues before deployment.${NC}"
    exit 1
fi
