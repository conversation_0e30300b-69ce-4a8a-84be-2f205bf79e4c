# Local CI/CD Configuration for TurdParty

# Service endpoints
API_URL=http://localhost:3050
MINIO_URL=http://localhost:3300
FLOWER_URL=http://localhost:3450

# Docker configuration
DOCKER_PREFIX=turdparty
COMPOSE_FILE=.dockerwrapper/docker-compose.yml

# Test configuration
COVERAGE_THRESHOLD=80
TIMEOUT=300

# Quality gates
MAX_RESPONSE_TIME=2.0
MIN_DOC_FILES=10

# Notification settings (optional)
SLACK_WEBHOOK_URL=""
EMAIL_NOTIFICATIONS=""
