#!/bin/bash

# Local Development Workflow Helper
# Common development tasks for TurdParty

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

case "$1" in
    "start")
        echo -e "${BLUE}🚀 Starting TurdParty development environment...${NC}"
        cd .dockerwrapper && docker compose up -d
        echo -e "${GREEN}✅ Services started${NC}"
        echo "API: http://localhost:3050"
        echo "MinIO: http://localhost:3301"
        echo "Flower: http://localhost:3450"
        ;;
    "stop")
        echo -e "${BLUE}🛑 Stopping TurdParty services...${NC}"
        cd .dockerwrapper && docker compose down
        echo -e "${GREEN}✅ Services stopped${NC}"
        ;;
    "restart")
        echo -e "${BLUE}🔄 Restarting TurdParty services...${NC}"
        cd .dockerwrapper && docker compose restart
        echo -e "${GREEN}✅ Services restarted${NC}"
        ;;
    "logs")
        echo -e "${BLUE}📋 Showing service logs...${NC}"
        cd .dockerwrapper && docker compose logs -f
        ;;
    "health")
        echo -e "${BLUE}🏥 Running health check...${NC}"
        ./scripts/monitoring/healthcheck.sh
        ;;
    "ci")
        echo -e "${BLUE}🔍 Running local CI pipeline...${NC}"
        ./scripts/ci/local-ci.sh
        ;;
    "test")
        echo -e "${BLUE}🧪 Running tests...${NC}"
        ./scripts/testing/post-cleanup-test.sh
        ;;
    "clean")
        echo -e "${BLUE}🧹 Cleaning up containers and volumes...${NC}"
        cd .dockerwrapper && docker compose down -v
        docker system prune -f
        echo -e "${GREEN}✅ Cleanup complete${NC}"
        ;;
    *)
        echo "TurdParty Development Workflow Helper"
        echo "Usage: $0 {start|stop|restart|logs|health|ci|test|clean}"
        echo ""
        echo "Commands:"
        echo "  start   - Start all services"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show service logs"
        echo "  health  - Run health check"
        echo "  ci      - Run local CI pipeline"
        echo "  test    - Run test suite"
        echo "  clean   - Clean up containers and volumes"
        ;;
esac
