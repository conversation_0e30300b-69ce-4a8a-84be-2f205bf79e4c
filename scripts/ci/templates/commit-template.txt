# <type>: <description>
#
# <body>
#
# <footer>

# Type can be:
# feat: A new feature
# fix: A bug fix
# docs: Documentation only changes
# style: Changes that do not affect the meaning of the code
# refactor: A code change that neither fixes a bug nor adds a feature
# perf: A code change that improves performance
# test: Adding missing tests or correcting existing tests
# chore: Changes to the build process or auxiliary tools

# Examples:
# feat: add user authentication system
# fix: resolve database connection timeout
# docs: update API documentation
# refactor: optimize file upload performance
