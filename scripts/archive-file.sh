#!/bin/bash

# TurdParty File Archival Script
# Archives a file to the _archive_ directory with proper documentation

set -e

# Make script executable
chmod +x "$0"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ARCHIVE_ROOT="_archive_"
CLEANUP_LOG="$ARCHIVE_ROOT/cleanup-log.md"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
DATE_ONLY=$(date '+%Y-%m-%d')

# Function to show usage
show_usage() {
    echo "Usage: $0 <file_path> <reason> [additional_notes]"
    echo ""
    echo "Arguments:"
    echo "  file_path       Path to the file to archive (relative to project root)"
    echo "  reason          Reason for archiving the file"
    echo "  additional_notes Optional additional notes about the archival"
    echo ""
    echo "Examples:"
    echo "  $0 test-upload.js 'Duplicate functionality, replaced by /tests/upload.test.js'"
    echo "  $0 debug_script.py 'Temporary debug script no longer needed' 'Used during development phase'"
    exit 1
}

# Check arguments
if [ $# -lt 2 ]; then
    echo -e "${RED}Error: Insufficient arguments${NC}"
    show_usage
fi

FILE_PATH="$1"
REASON="$2"
ADDITIONAL_NOTES="${3:-}"

# Validate file exists
if [ ! -f "$FILE_PATH" ]; then
    echo -e "${RED}Error: File '$FILE_PATH' does not exist${NC}"
    exit 1
fi

# Get file information
FILE_SIZE=$(stat -f%z "$FILE_PATH" 2>/dev/null || stat -c%s "$FILE_PATH" 2>/dev/null || echo "unknown")
FILE_MODIFIED=$(stat -f%Sm "$FILE_PATH" 2>/dev/null || stat -c%y "$FILE_PATH" 2>/dev/null || echo "unknown")
FILE_TYPE=$(file -b "$FILE_PATH" 2>/dev/null || echo "unknown")

# Determine file extension for comment style
FILE_EXT="${FILE_PATH##*.}"
case "$FILE_EXT" in
    py)
        COMMENT_START="# "
        COMMENT_END=""
        ;;
    js|ts|jsx|tsx)
        COMMENT_START="// "
        COMMENT_END=""
        ;;
    html|xml)
        COMMENT_START="<!-- "
        COMMENT_END=" -->"
        ;;
    css)
        COMMENT_START="/* "
        COMMENT_END=" */"
        ;;
    sh|bash)
        COMMENT_START="# "
        COMMENT_END=""
        ;;
    md)
        COMMENT_START="<!-- "
        COMMENT_END=" -->"
        ;;
    *)
        COMMENT_START="# "
        COMMENT_END=""
        ;;
esac

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}      TurdParty File Archival Tool      ${NC}"
echo -e "${BLUE}=======================================${NC}"

echo -e "\n${YELLOW}Archiving file: $FILE_PATH${NC}"
echo -e "Reason: $REASON"
if [ -n "$ADDITIONAL_NOTES" ]; then
    echo -e "Notes: $ADDITIONAL_NOTES"
fi

# Create archive directory structure
ARCHIVE_DIR="$ARCHIVE_ROOT/$(dirname "$FILE_PATH")"
mkdir -p "$ARCHIVE_DIR"

# Create archive root structure if it doesn't exist
mkdir -p "$ARCHIVE_ROOT"/{metadata,logs}

# Initialize cleanup log if it doesn't exist
if [ ! -f "$CLEANUP_LOG" ]; then
    cat > "$CLEANUP_LOG" << EOF
# TurdParty Cleanup Log

This file tracks all cleanup actions performed during the folder structure reorganization.

## Archive Actions

| Date | File | Original Location | Reason | Notes |
|------|------|-------------------|--------|-------|
EOF
fi

# Determine archive file path
ARCHIVE_FILE="$ARCHIVE_ROOT/$FILE_PATH"

# Create archive header
ARCHIVE_HEADER="${COMMENT_START}ARCHIVED FILE${COMMENT_END}
${COMMENT_START}Original Location: /$FILE_PATH${COMMENT_END}
${COMMENT_START}Archived Date: $TIMESTAMP${COMMENT_END}
${COMMENT_START}Reason: $REASON${COMMENT_END}
${COMMENT_START}File Size: $FILE_SIZE bytes${COMMENT_END}
${COMMENT_START}Last Modified: $FILE_MODIFIED${COMMENT_END}
${COMMENT_START}File Type: $FILE_TYPE${COMMENT_END}"

if [ -n "$ADDITIONAL_NOTES" ]; then
    ARCHIVE_HEADER="$ARCHIVE_HEADER
${COMMENT_START}Notes: $ADDITIONAL_NOTES${COMMENT_END}"
fi

# Check for dependencies (basic analysis)
echo -e "\n${YELLOW}Analyzing dependencies...${NC}"
DEPENDENCIES=""

case "$FILE_EXT" in
    py)
        # Look for Python imports
        IMPORTS=$(grep -n "^import\|^from" "$FILE_PATH" 2>/dev/null | head -5 || echo "")
        if [ -n "$IMPORTS" ]; then
            DEPENDENCIES="Python imports found: $(echo "$IMPORTS" | wc -l) import statements"
        fi
        ;;
    js|ts|jsx|tsx)
        # Look for JavaScript imports/requires
        IMPORTS=$(grep -n "^import\|require(" "$FILE_PATH" 2>/dev/null | head -5 || echo "")
        if [ -n "$IMPORTS" ]; then
            DEPENDENCIES="JavaScript imports found: $(echo "$IMPORTS" | wc -l) import statements"
        fi
        ;;
esac

if [ -n "$DEPENDENCIES" ]; then
    ARCHIVE_HEADER="$ARCHIVE_HEADER
${COMMENT_START}Dependencies: $DEPENDENCIES${COMMENT_END}"
    echo -e "  Dependencies detected: $DEPENDENCIES"
else
    ARCHIVE_HEADER="$ARCHIVE_HEADER
${COMMENT_START}Dependencies: None identified${COMMENT_END}"
    echo -e "  No obvious dependencies detected"
fi

# Look for references to this file in other files
echo -e "\n${YELLOW}Checking for references...${NC}"
REFERENCES=$(grep -r "$(basename "$FILE_PATH")" . --exclude-dir=_archive_ --exclude-dir=.git --exclude-dir=node_modules --exclude-dir=__pycache__ 2>/dev/null | grep -v "^$FILE_PATH:" | wc -l || echo "0")

if [ "$REFERENCES" -gt 0 ]; then
    ARCHIVE_HEADER="$ARCHIVE_HEADER
${COMMENT_START}References: $REFERENCES potential references found in codebase${COMMENT_END}"
    echo -e "  ${YELLOW}Warning: $REFERENCES potential references found${NC}"
    echo -e "  ${YELLOW}Please verify these references are updated or no longer needed${NC}"
else
    ARCHIVE_HEADER="$ARCHIVE_HEADER
${COMMENT_START}References: No references found in codebase${COMMENT_END}"
    echo -e "  No references found in codebase"
fi

ARCHIVE_HEADER="$ARCHIVE_HEADER
${COMMENT_START}${COMMENT_END}
${COMMENT_START}[Original file content follows...]${COMMENT_END}
${COMMENT_START}${COMMENT_END}

"

# Create the archived file
echo -e "\n${YELLOW}Creating archived file...${NC}"
{
    echo -e "$ARCHIVE_HEADER"
    cat "$FILE_PATH"
} > "$ARCHIVE_FILE"

# Verify archive was created successfully
if [ ! -f "$ARCHIVE_FILE" ]; then
    echo -e "${RED}Error: Failed to create archive file${NC}"
    exit 1
fi

# Update cleanup log
echo "| $DATE_ONLY | $(basename "$FILE_PATH") | /$FILE_PATH | $REASON | $ADDITIONAL_NOTES |" >> "$CLEANUP_LOG"

# Create metadata entry
METADATA_FILE="$ARCHIVE_ROOT/metadata/$(basename "$FILE_PATH" | sed 's/\./_/g')_metadata.json"
cat > "$METADATA_FILE" << EOF
{
    "original_path": "/$FILE_PATH",
    "archive_path": "/$ARCHIVE_FILE",
    "archived_date": "$TIMESTAMP",
    "reason": "$REASON",
    "additional_notes": "$ADDITIONAL_NOTES",
    "file_info": {
        "size_bytes": "$FILE_SIZE",
        "last_modified": "$FILE_MODIFIED",
        "file_type": "$FILE_TYPE",
        "extension": "$FILE_EXT"
    },
    "analysis": {
        "dependencies": "$DEPENDENCIES",
        "references_found": $REFERENCES
    },
    "archived_by": "$(whoami)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')"
}
EOF

# Remove original file
echo -e "\n${YELLOW}Removing original file...${NC}"
rm "$FILE_PATH"

# Verify original file was removed
if [ -f "$FILE_PATH" ]; then
    echo -e "${RED}Error: Failed to remove original file${NC}"
    exit 1
fi

echo -e "\n${GREEN}✓ File successfully archived${NC}"
echo -e "  Original: ${BLUE}$FILE_PATH${NC}"
echo -e "  Archive: ${BLUE}$ARCHIVE_FILE${NC}"
echo -e "  Metadata: ${BLUE}$METADATA_FILE${NC}"
echo -e "  Log entry: ${BLUE}$CLEANUP_LOG${NC}"

if [ "$REFERENCES" -gt 0 ]; then
    echo -e "\n${YELLOW}⚠️  Warning: $REFERENCES potential references detected${NC}"
    echo -e "   Please verify these are updated or no longer needed:"
    grep -r "$(basename "$FILE_PATH")" . --exclude-dir=_archive_ --exclude-dir=.git --exclude-dir=node_modules --exclude-dir=__pycache__ 2>/dev/null | grep -v "^$FILE_PATH:" | head -5 || true
fi

echo -e "\n${BLUE}Archive operation completed successfully${NC}"
exit 0
