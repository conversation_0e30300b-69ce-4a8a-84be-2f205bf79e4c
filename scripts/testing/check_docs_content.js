// @ts-check
const { chromium } = require('playwright');

async function checkDocsContent() {
  console.log('Starting browser...');
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('Navigating to API docs page...');
    await page.goto('http://api:8000/api/v1/docs', { timeout: 30000 });
    
    console.log('Page loaded, taking screenshot...');
    await page.screenshot({ path: '/app/docs_page.png', fullPage: true });
    
    // Get the full HTML content of the page
    const htmlContent = await page.content();
    console.log('\nPage HTML Content (first 1000 chars):');
    console.log(htmlContent.substring(0, 1000));
    
    // Check if specific expected elements are in the HTML
    const hasSwaggerUIDiv = htmlContent.includes('<div id="swagger-ui">');
    const hasSwaggerUIScript = htmlContent.includes('SwaggerUIBundle');
    const hasOpenApiUrl = htmlContent.includes('/api/v1/openapi.json');
    
    console.log('\nPage Analysis:');
    console.log(`Has #swagger-ui div: ${hasSwaggerUIDiv}`);
    console.log(`Has SwaggerUIBundle script: ${hasSwaggerUIScript}`);
    console.log(`Has OpenAPI URL reference: ${hasOpenApiUrl}`);
    
    // Try to detect issues
    if (!hasSwaggerUIDiv) {
      console.log('⚠️ The page is missing the main Swagger UI container div');
    }
    
    if (!hasSwaggerUIScript) {
      console.log('⚠️ The page is missing the SwaggerUIBundle JavaScript');
    }
    
    if (!hasOpenApiUrl) {
      console.log('⚠️ No reference to OpenAPI JSON URL found');
    }
    
    // Try to fetch the OpenAPI JSON directly
    console.log('\nAttempting to fetch OpenAPI JSON...');
    const apiPage = await context.newPage();
    const apiResponse = await apiPage.goto('http://api:8000/api/v1/openapi.json', { timeout: 10000 });
    
    if (apiResponse) {
      console.log(`OpenAPI JSON status: ${apiResponse.status()}`);
      if (apiResponse.status() === 200) {
        const jsonContent = await apiResponse.text();
        console.log(`OpenAPI JSON size: ${jsonContent.length} characters`);
        console.log(`First 200 chars: ${jsonContent.substring(0, 200)}...`);
      } else {
        console.log('⚠️ Failed to fetch OpenAPI JSON');
      }
    } else {
      console.log('⚠️ No response when fetching OpenAPI JSON');
    }
    
  } catch (error) {
    console.error('Error checking docs content:', error);
  } finally {
    await browser.close();
    console.log('\nCheck complete');
  }
}

checkDocsContent(); 