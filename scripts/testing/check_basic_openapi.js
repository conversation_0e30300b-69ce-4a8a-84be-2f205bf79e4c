const fs = require('fs');
const http = require('http');

async function fetchOpenAPISpec() {
  console.log('Fetching OpenAPI spec from API...');
  
  const options = {
    hostname: 'localhost',
    port: 3050,
    path: '/api/v1/openapi.json',
    method: 'GET',
    headers: {
      'Accept': 'application/json'
    }
  };
  
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      console.log(`Status: ${res.statusCode}`);
      console.log(`Headers: ${JSON.stringify(res.headers)}`);
      
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode !== 200) {
          console.error(`Received non-200 status: ${res.statusCode}`);
          console.error(`Response body: ${data.substring(0, 500)}...`);
          reject(new Error(`HTTP Error: ${res.statusCode}`));
          return;
        }
        
        try {
          const jsonData = JSON.parse(data);
          console.log('Successfully parsed OpenAPI JSON');
          console.log(`JSON contains ${Object.keys(jsonData).length} top-level keys`);
          
          if (jsonData.paths) {
            console.log(`API has ${Object.keys(jsonData.paths).length} paths defined`);
            console.log('Paths include:');
            Object.keys(jsonData.paths).slice(0, 10).forEach(path => {
              console.log(` - ${path}`);
            });
            
            // Save to file for inspection
            fs.writeFileSync('openapi-spec.json', data);
            console.log('Saved OpenAPI spec to openapi-spec.json');
          } else {
            console.error('No "paths" field found in the OpenAPI spec');
          }
          
          resolve(jsonData);
        } catch (err) {
          console.error('Error parsing JSON:', err);
          console.error(`Raw data (first 500 chars): ${data.substring(0, 500)}...`);
          reject(err);
        }
      });
    });
    
    req.on('error', (err) => {
      console.error('Error making request:', err);
      reject(err);
    });
    
    req.end();
  });
}

// Run the function
fetchOpenAPISpec()
  .then(() => console.log('Done!'))
  .catch(err => console.error('Failed:', err)); 