// This script will run in a browser environment like Chrome

// First, check if we can access the OpenAPI JSON file
console.log('Checking OpenAPI JSON accessibility...');
fetch('/static/openapi.json')
  .then(response => {
    if (response.ok) {
      return response.json();
    }
    throw new Error('Failed to load OpenAPI JSON');
  })
  .then(data => {
    console.log('✅ OpenAPI JSON loaded successfully:', data);
    document.body.innerHTML += '<p style="color:green;">✅ OpenAPI JSON loaded successfully</p>';
  })
  .catch(error => {
    console.error('❌ Error loading OpenAPI JSON:', error);
    document.body.innerHTML += `<p style="color:red;">❌ Error loading OpenAPI JSON: ${error.message}</p>`;
  });

// Check if Swagger UI is loaded
setTimeout(() => {
  console.log('Checking Swagger UI elements...');
  const swaggerUI = document.getElementById('swagger-ui');
  
  if (swaggerUI) {
    document.body.innerHTML += '<p style="color:green;">✅ Swagger UI container found</p>';
    
    // Check if Swagger UI content is loaded
    const apiInfo = document.querySelector('.information-container');
    if (apiInfo) {
      document.body.innerHTML += '<p style="color:green;">✅ Swagger UI content loaded</p>';
    } else {
      document.body.innerHTML += '<p style="color:red;">❌ Swagger UI content not loaded</p>';
    }
    
    // Check for common CSP issues
    document.body.innerHTML += '<h3>Checking for CSP issues...</h3>';
    
    // Check console for CSP errors
    const cspErrors = [];
    const originalConsoleError = console.error;
    console.error = function(msg) {
      if (typeof msg === 'string' && msg.includes('Content Security Policy')) {
        cspErrors.push(msg);
        document.body.innerHTML += `<p style="color:orange;">⚠️ CSP Error: ${msg}</p>`;
      }
      originalConsoleError.apply(console, arguments);
    };
    
    // Suggest fix
    document.body.innerHTML += `
      <h3>Suggested CSP Fix:</h3>
      <pre>
default-src 'self'; 
script-src 'self' https://cdn.jsdelivr.net 'unsafe-inline' 'unsafe-eval'; 
style-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; 
img-src 'self' data:; 
connect-src 'self' localhost:*;
      </pre>
    `;
  } else {
    document.body.innerHTML += '<p style="color:red;">❌ Swagger UI container not found</p>';
  }
}, 3000); 