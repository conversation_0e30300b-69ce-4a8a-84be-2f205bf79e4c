const { spawn } = require('child_process');
const path = require('path');

// Start the Python API server using uvicorn
const pythonProcess = spawn('uvicorn', ['main:app', '--host', '0.0.0.0', '--port', '8000']);

pythonProcess.stdout.on('data', (data) => {
  console.log(`Server output: ${data}`);
});

pythonProcess.stderr.on('data', (data) => {
  console.error(`Server error: ${data}`);
});

pythonProcess.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
});

// Keep the Node.js process running
process.on('SIGINT', () => {
  pythonProcess.kill();
  process.exit();
});