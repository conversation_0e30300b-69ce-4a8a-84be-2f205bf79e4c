// @ts-check
const { chromium } = require('playwright');

async function checkDocsEndpoint() {
  console.log('Starting browser to check /api/v1/docs endpoint...');
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('Navigating to http://localhost:3050/api/v1/docs');
    await page.goto('http://localhost:3050/api/v1/docs', { timeout: 30000 });
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot
    console.log('Taking screenshot...');
    await page.screenshot({ path: 'docs_endpoint.png', fullPage: true });
    
    // Check page content
    const pageContent = await page.content();
    console.log('Page title:', await page.title());
    
    // Check if any elements are visible on the page
    const bodyContent = await page.evaluate(() => document.body.innerText);
    console.log('Body text (truncated):', bodyContent.substring(0, 200) + '...');
    
    // Check for specific Swagger UI elements
    const hasSwaggerElement = await page.locator('#swagger-ui').count() > 0;
    console.log('Has #swagger-ui element:', hasSwaggerElement);
    
    // Look for the OpenAPI URL in the source
    const pageSource = await page.content();
    const openapiUrlMatch = /url:\s*['"]([^'"]+)['"]/i.exec(pageSource);
    if (openapiUrlMatch) {
      console.log('Found OpenAPI URL in page source:', openapiUrlMatch[1]);
      
      // Try to fetch this URL directly
      const openapiUrl = openapiUrlMatch[1];
      const apiPage = await context.newPage();
      console.log(`Trying to fetch OpenAPI JSON from: ${openapiUrl}`);
      try {
        const response = await apiPage.goto(openapiUrl, { timeout: 10000 });
        if (response) {
          console.log('OpenAPI JSON response status:', response.status());
          console.log('OpenAPI JSON content type:', response.headers()['content-type']);
        } else {
          console.log('No response received from OpenAPI URL');
        }
      } catch (error) {
        console.error('Error fetching OpenAPI JSON:', error.message);
      }
      await apiPage.close();
    } else {
      console.log('No OpenAPI URL found in page source');
    }
    
    // Check for the raw script element with the URL
    const scriptTags = await page.locator('script').evaluateAll(scripts => 
      scripts.map(script => script.textContent)
    );
    console.log('Script tags found:', scriptTags.length);
    
    console.log('Inspection complete');
  } catch (error) {
    console.error('Error checking docs endpoint:', error);
  } finally {
    // Leave the browser open for manual inspection
    // await browser.close();
    console.log('Browser left open for manual inspection. Close when done.');
  }
}

checkDocsEndpoint(); 