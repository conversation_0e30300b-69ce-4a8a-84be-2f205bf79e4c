// @ts-check
const { chromium } = require('playwright');

async function checkSwaggerUI() {
  console.log('Starting browser...');
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('Navigating to Swagger UI...');
    await page.goto('http://api:8000/api/v1/docs', { timeout: 30000 });
    
    // Wait for the Swagger UI to load
    console.log('Waiting for Swagger UI to load...');
    await page.waitForSelector('#swagger-ui', { timeout: 10000 });
    await page.waitForSelector('.information-container', { timeout: 10000 });
    
    // Check if the API title is displayed
    const apiTitle = await page.textContent('.title');
    console.log('API Title found:', apiTitle);
    
    // Check if endpoints are displayed
    const operationCount = await page.locator('.opblock').count();
    console.log(`Found ${operationCount} API operations`);
    
    // Take a screenshot
    console.log('Taking screenshot...');
    await page.screenshot({ path: 'swagger-ui-check.png', fullPage: true });
    
    // Check for specific endpoints we expect to see
    const healthEndpoint = await page.getByText('/api/v1/health').isVisible();
    const vmsEndpoint = await page.getByText('/api/v1/virtual-machines').isVisible();
    
    console.log('Health endpoint visible:', healthEndpoint);
    console.log('Virtual machines endpoint visible:', vmsEndpoint);
    
    if (apiTitle && operationCount > 0 && healthEndpoint && vmsEndpoint) {
      console.log('✅ Swagger UI is rendering correctly!');
    } else {
      console.log('❌ Swagger UI is not rendering as expected');
    }
  } catch (error) {
    console.error('Error checking Swagger UI:', error);
  } finally {
    // Close the browser
    await browser.close();
  }
}

checkSwaggerUI(); 