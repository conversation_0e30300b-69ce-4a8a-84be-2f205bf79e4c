import socket
import sys

# Try different host addresses and ports
configs = [
    {"host": "host.docker.internal", "port": 40000},
    {"host": "**********", "port": 40000},
    {"host": "127.0.0.1", "port": 40000},
    {"host": "localhost", "port": 40000},
    {"host": "host.docker.internal", "port": 40001},
    {"host": "**********", "port": 40001},
    {"host": "127.0.0.1", "port": 40001},
    {"host": "localhost", "port": 40001}
]

for config in configs:
    host = config["host"]
    port = config["port"]
    try:
        print(f"Trying to connect to {host}:{port}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        if result == 0:
            print(f"Connection to {host}:{port} succeeded")
        else:
            print(f"Connection to {host}:{port} failed with error code {result}")
        sock.close()
    except Exception as e:
        print(f"Error connecting to {host}:{port}: {e}")
