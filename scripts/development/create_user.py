from api.models.user import User
from api.db.session import get_db
from sqlalchemy.orm import Session

def create_user():
    try:
        with get_db() as db:
            user = User(
                email='<EMAIL>',
                hashed_password='$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW',
                is_active=True,
                is_superuser=True
            )
            db.add(user)
            db.commit()
            print('User created successfully')
    except Exception as e:
        print(f"Error creating user: {e}")

if __name__ == "__main__":
    create_user()
