from flask import Flask, jsonify
import requests
import json

app = Flask(__name__)

@app.route('/openapi.json')
def openapi_json():
    try:
        # Try to fetch the OpenAPI JSON from the API
        response = requests.get('http://localhost:3050/api/v1/openapi.json', timeout=5)
        
        # If successful, return it with CORS headers
        if response.status_code == 200:
            json_data = response.json()
        else:
            # If unsuccessful, use a minimal OpenAPI spec
            json_data = {
                "openapi": "3.0.3",
                "info": {
                    "title": "TurdParty API",
                    "version": "1.0.0",
                    "description": "API for TurdParty application"
                },
                "paths": {
                    "/api/v1/health": {
                        "get": {
                            "summary": "Health check",
                            "responses": {
                                "200": {
                                    "description": "Successful response",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "status": {
                                                        "type": "string"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
        response = jsonify(json_data)
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Methods', 'GET, OPTIONS')
        response.headers.add('Access-Control-Allow-Headers', '*')
        return response
    except Exception as e:
        # Return a fallback OpenAPI spec if there's an error
        return jsonify({
            "openapi": "3.0.3",
            "info": {
                "title": "TurdParty API (Fallback)",
                "version": "1.0.0",
                "description": f"Error fetching API spec: {str(e)}"
            },
            "paths": {}
        })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3051, debug=True) 