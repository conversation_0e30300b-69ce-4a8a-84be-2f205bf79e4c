#!/bin/bash

# Serve TurdParty Documentation Locally
# Builds and serves Sphinx documentation for local viewing

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}📚 TurdParty Documentation Server${NC}"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "docs/source/conf.py" ]; then
    echo -e "${YELLOW}⚠️  Please run this script from the project root directory${NC}"
    exit 1
fi

# Build documentation
echo -e "${BLUE}🔨 Building documentation...${NC}"
if docker exec turdparty_api sphinx-build -b html /app/docs/source /app/docs/build/html; then
    echo -e "${GREEN}✅ Documentation built successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Building documentation in container failed, trying local build...${NC}"
    
    # Try local build if container fails
    if command -v sphinx-build >/dev/null 2>&1; then
        cd docs && sphinx-build -b html source build/html && cd ..
        echo -e "${GREEN}✅ Documentation built locally${NC}"
    else
        echo -e "${YELLOW}⚠️  Sphinx not available locally. Installing in container...${NC}"
        docker exec turdparty_api pip install sphinx sphinx_rtd_theme myst-parser
        docker exec turdparty_api sphinx-build -b html /app/docs/source /app/docs/build/html
        echo -e "${GREEN}✅ Documentation built in container${NC}"
    fi
fi

# Check if documentation was built
if [ ! -f "docs/build/html/index.html" ]; then
    echo -e "${YELLOW}❌ Documentation build failed${NC}"
    exit 1
fi

# Start local server
echo
echo -e "${BLUE}🌐 Starting documentation server...${NC}"
echo -e "${GREEN}📖 Documentation available at: http://localhost:8080${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop the server${NC}"
echo

# Try different Python versions for serving
if command -v python3 >/dev/null 2>&1; then
    cd docs/build/html && python3 -m http.server 8080
elif command -v python >/dev/null 2>&1; then
    cd docs/build/html && python -m http.server 8080
else
    echo -e "${YELLOW}⚠️  Python not available locally. Using container to serve...${NC}"
    docker exec -d turdparty_api bash -c "cd /app/docs/build/html && python3 -m http.server 8080"
    echo -e "${GREEN}📖 Documentation served from container at: http://localhost:8080${NC}"
    echo -e "${YELLOW}Note: You may need to expose port 8080 in the container configuration${NC}"
fi
