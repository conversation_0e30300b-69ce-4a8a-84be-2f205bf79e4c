#!/bin/bash

# TurdParty Archive System Setup Script
# Initializes the _archive_ directory structure and documentation

set -e

# Make script executable
chmod +x "$0"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ARCHIVE_ROOT="_archive_"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}   TurdParty Archive System Setup       ${NC}"
echo -e "${BLUE}=======================================${NC}"

echo -e "\n${YELLOW}Setting up archive directory structure...${NC}"

# Create main archive directory structure
mkdir -p "$ARCHIVE_ROOT"/{api,frontend,ui,scripts,tests,docs,config,root,metadata,logs}

echo -e "${GREEN}✓ Created archive directory structure${NC}"

# Create main archive README
cat > "$ARCHIVE_ROOT/README.md" << 'EOF'
# TurdParty Archive Directory

This directory contains files that have been removed from the main codebase during the cleanup process. Each file is preserved with metadata indicating its original location and the reason for archiving.

## Purpose

The archive system serves several purposes:
- **Preservation**: Keeps deleted files for potential future reference
- **Documentation**: Records why files were removed and when
- **Safety**: Provides a rollback mechanism if needed
- **Audit Trail**: Maintains a complete history of cleanup actions

## Structure

The archive maintains the same directory structure as the original codebase:

```
_archive_/
├── README.md                    # This file
├── cleanup-log.md              # Detailed log of all cleanup actions
├── api/                        # Archived API files
├── frontend/                   # Archived frontend files
├── ui/                         # Archived UI files
├── scripts/                    # Archived scripts
├── tests/                      # Archived test files
├── docs/                       # Archived documentation
├── config/                     # Archived configuration files
├── root/                       # Archived root directory files
├── metadata/                   # JSON metadata for each archived file
└── logs/                       # Archive operation logs
```

## File Format

Each archived file includes a header comment with:
- Original location
- Date of archiving
- Reason for archiving
- File metadata (size, type, last modified)
- Dependency analysis
- Reference count in codebase

### Example Header (Python file):
```python
# ARCHIVED FILE
# Original Location: /test_old.py
# Archived Date: 2024-01-15 14:30:00
# Reason: Duplicate functionality, replaced by /tests/test_main.py
# File Size: 1024 bytes
# Last Modified: 2024-01-10 10:15:00
# File Type: Python script
# Dependencies: None identified
# References: No references found in codebase
#
# [Original file content follows...]
```

## Usage

### Archiving Files
Use the archive script to move files to the archive:
```bash
./scripts/archive-file.sh path/to/file.py "Reason for archiving" "Optional notes"
```

### Restoring Files
Use the restore script to bring files back:
```bash
./scripts/restore-from-archive.sh _archive_/path/to/file.py
```

### Searching Archives
Find archived files by name or content:
```bash
# Find by filename
find _archive_ -name "*.py" -type f

# Search content in archived files
grep -r "search_term" _archive_/
```

## Metadata

Each archived file has corresponding JSON metadata in the `metadata/` directory containing:
- Original and archive paths
- Archival timestamp and reason
- File information (size, type, modification date)
- Dependency analysis results
- Reference count in codebase
- Git information (commit, branch)
- User who performed the archival

## Cleanup Log

The `cleanup-log.md` file maintains a chronological record of all cleanup actions in markdown table format for easy review.

## Best Practices

1. **Always provide clear reasons** for archiving files
2. **Check for references** before archiving to avoid breaking dependencies
3. **Review archived files periodically** to determine if they can be permanently deleted
4. **Keep the cleanup log updated** for audit purposes
5. **Test functionality** after archiving to ensure nothing is broken

## Maintenance

- Review archived files quarterly to identify candidates for permanent deletion
- Clean up metadata files for permanently deleted archives
- Compress old archives if disk space becomes a concern
- Update this documentation as the archive system evolves

---
*Archive system initialized on: TIMESTAMP_PLACEHOLDER*
EOF

# Replace timestamp placeholder
sed -i.bak "s/TIMESTAMP_PLACEHOLDER/$TIMESTAMP/" "$ARCHIVE_ROOT/README.md" && rm "$ARCHIVE_ROOT/README.md.bak"

echo -e "${GREEN}✓ Created archive README.md${NC}"

# Create cleanup log template
cat > "$ARCHIVE_ROOT/cleanup-log.md" << EOF
# TurdParty Cleanup Log

This file tracks all cleanup actions performed during the folder structure reorganization.

**Cleanup Started:** $TIMESTAMP  
**Project:** TurdParty  
**Cleanup Goal:** Organize folder structure and reduce root directory clutter  

## Summary Statistics

- **Total Files Archived:** 0
- **Root Directory Files Removed:** 0
- **Test Files Consolidated:** 0
- **Configuration Files Merged:** 0

## Archive Actions

| Date | File | Original Location | Reason | Notes |
|------|------|-------------------|--------|-------|

## Restoration Actions

| Date | File | Restored To | Reason | Notes |
|------|------|-------------|--------|-------|

## Notes

- All archived files include metadata headers with original location and archival reason
- Metadata JSON files are stored in the metadata/ directory
- Use scripts/restore-from-archive.sh to restore files if needed

---
*Log initialized on: $TIMESTAMP*
EOF

echo -e "${GREEN}✓ Created cleanup log template${NC}"

# Create archive validation script
cat > "$ARCHIVE_ROOT/validate-archive.sh" << 'EOF'
#!/bin/bash

# Archive Validation Script
# Validates the integrity of archived files and metadata

set -e

ARCHIVE_ROOT="_archive_"
ERRORS=0

echo "Validating archive integrity..."

# Check if all archived files have corresponding metadata
for archived_file in $(find "$ARCHIVE_ROOT" -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.html" -o -name "*.css" -o -name "*.sh" -o -name "*.md" | grep -v metadata | grep -v README.md | grep -v cleanup-log.md); do
    basename_no_ext=$(basename "$archived_file" | sed 's/\./_/g')
    metadata_file="$ARCHIVE_ROOT/metadata/${basename_no_ext}_metadata.json"
    
    if [ ! -f "$metadata_file" ]; then
        echo "ERROR: Missing metadata for $archived_file"
        ((ERRORS++))
    fi
done

# Check if all metadata files have corresponding archived files
for metadata_file in $(find "$ARCHIVE_ROOT/metadata" -name "*_metadata.json"); do
    # Extract original path from metadata
    if [ -f "$metadata_file" ]; then
        archive_path=$(grep '"archive_path"' "$metadata_file" | cut -d'"' -f4 | sed 's/^\///')
        if [ ! -f "$archive_path" ]; then
            echo "ERROR: Missing archived file for metadata $metadata_file"
            ((ERRORS++))
        fi
    fi
done

# Validate JSON metadata files
for metadata_file in $(find "$ARCHIVE_ROOT/metadata" -name "*_metadata.json"); do
    if ! python3 -m json.tool "$metadata_file" > /dev/null 2>&1; then
        echo "ERROR: Invalid JSON in $metadata_file"
        ((ERRORS++))
    fi
done

if [ $ERRORS -eq 0 ]; then
    echo "✓ Archive validation passed - no errors found"
    exit 0
else
    echo "✗ Archive validation failed - $ERRORS errors found"
    exit 1
fi
EOF

chmod +x "$ARCHIVE_ROOT/validate-archive.sh"
echo -e "${GREEN}✓ Created archive validation script${NC}"

# Create restore script template
cat > "scripts/restore-from-archive.sh" << 'EOF'
#!/bin/bash

# TurdParty File Restoration Script
# Restores a file from the archive back to its original location

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ARCHIVE_ROOT="_archive_"
CLEANUP_LOG="$ARCHIVE_ROOT/cleanup-log.md"

show_usage() {
    echo "Usage: $0 <archived_file_path>"
    echo ""
    echo "Arguments:"
    echo "  archived_file_path  Path to the archived file (e.g., _archive_/test-upload.js)"
    echo ""
    echo "Examples:"
    echo "  $0 _archive_/test-upload.js"
    echo "  $0 _archive_/scripts/debug_script.py"
    exit 1
}

if [ $# -ne 1 ]; then
    echo -e "${RED}Error: Incorrect number of arguments${NC}"
    show_usage
fi

ARCHIVED_FILE="$1"

if [ ! -f "$ARCHIVED_FILE" ]; then
    echo -e "${RED}Error: Archived file '$ARCHIVED_FILE' does not exist${NC}"
    exit 1
fi

# Extract original location from file header
ORIGINAL_LOCATION=$(head -10 "$ARCHIVED_FILE" | grep "Original Location:" | sed 's/.*Original Location: //' | sed 's/[[:space:]]*$//' | tr -d '\r')

if [ -z "$ORIGINAL_LOCATION" ]; then
    echo -e "${RED}Error: Could not determine original location from archived file${NC}"
    exit 1
fi

# Remove leading slash if present
ORIGINAL_LOCATION=$(echo "$ORIGINAL_LOCATION" | sed 's/^\/*//')

echo -e "${BLUE}Restoring file from archive${NC}"
echo -e "  Archived file: $ARCHIVED_FILE"
echo -e "  Original location: $ORIGINAL_LOCATION"

# Check if original location already exists
if [ -f "$ORIGINAL_LOCATION" ]; then
    echo -e "${YELLOW}Warning: File already exists at original location${NC}"
    read -p "Overwrite existing file? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Restoration cancelled${NC}"
        exit 0
    fi
fi

# Create directory if needed
mkdir -p "$(dirname "$ORIGINAL_LOCATION")"

# Extract original content (skip archive header)
awk '/\[Original file content follows\.\.\.\]/{flag=1; next} flag' "$ARCHIVED_FILE" > "$ORIGINAL_LOCATION"

# Verify restoration
if [ ! -f "$ORIGINAL_LOCATION" ]; then
    echo -e "${RED}Error: Failed to restore file${NC}"
    exit 1
fi

# Log restoration
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "| $(date '+%Y-%m-%d') | $(basename "$ORIGINAL_LOCATION") | /$ORIGINAL_LOCATION | Manual restoration | Restored from archive |" >> "$CLEANUP_LOG"

echo -e "${GREEN}✓ File successfully restored${NC}"
echo -e "  Restored to: ${BLUE}$ORIGINAL_LOCATION${NC}"
EOF

chmod +x "scripts/restore-from-archive.sh"
echo -e "${GREEN}✓ Created file restoration script${NC}"

# Make archive scripts executable
chmod +x "scripts/archive-file.sh" 2>/dev/null || true

# Create .gitignore for archive (optional - archives might want to be tracked)
cat > "$ARCHIVE_ROOT/.gitignore" << 'EOF'
# Archive .gitignore
# Uncomment lines below if you don't want to track archived files in git

# Archived files (uncomment to ignore)
# *.py
# *.js
# *.ts
# *.html
# *.css
# *.sh
# *.md

# Always track these important files
!README.md
!cleanup-log.md
!validate-archive.sh
!.gitignore

# Logs can be ignored if desired
# logs/
EOF

echo -e "${GREEN}✓ Created archive .gitignore${NC}"

# Create summary of what was set up
cat > "$ARCHIVE_ROOT/logs/setup_$(date +%Y%m%d_%H%M%S).log" << EOF
Archive System Setup Log
========================

Setup Date: $TIMESTAMP
Setup User: $(whoami)
Working Directory: $(pwd)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')
Git Branch: $(git branch --show-current 2>/dev/null || echo 'unknown')

Created Directories:
- $ARCHIVE_ROOT/api
- $ARCHIVE_ROOT/frontend
- $ARCHIVE_ROOT/ui
- $ARCHIVE_ROOT/scripts
- $ARCHIVE_ROOT/tests
- $ARCHIVE_ROOT/docs
- $ARCHIVE_ROOT/config
- $ARCHIVE_ROOT/root
- $ARCHIVE_ROOT/metadata
- $ARCHIVE_ROOT/logs

Created Files:
- $ARCHIVE_ROOT/README.md
- $ARCHIVE_ROOT/cleanup-log.md
- $ARCHIVE_ROOT/validate-archive.sh
- $ARCHIVE_ROOT/.gitignore
- scripts/restore-from-archive.sh

Scripts Available:
- scripts/archive-file.sh (archive individual files)
- scripts/restore-from-archive.sh (restore files from archive)
- $ARCHIVE_ROOT/validate-archive.sh (validate archive integrity)

Next Steps:
1. Review the archive system documentation
2. Begin using scripts/archive-file.sh to archive files
3. Regularly validate archive integrity
4. Update cleanup-log.md as files are archived

Archive system is ready for use.
EOF

echo -e "\n${GREEN}=======================================${NC}"
echo -e "${GREEN}   Archive System Setup Complete       ${NC}"
echo -e "${GREEN}=======================================${NC}"

echo -e "\n${BLUE}Archive system initialized successfully!${NC}"
echo -e "\nCreated:"
echo -e "  📁 Archive directory: ${BLUE}$ARCHIVE_ROOT/${NC}"
echo -e "  📖 Documentation: ${BLUE}$ARCHIVE_ROOT/README.md${NC}"
echo -e "  📝 Cleanup log: ${BLUE}$ARCHIVE_ROOT/cleanup-log.md${NC}"
echo -e "  🔧 Archive script: ${BLUE}scripts/archive-file.sh${NC}"
echo -e "  🔄 Restore script: ${BLUE}scripts/restore-from-archive.sh${NC}"
echo -e "  ✅ Validation script: ${BLUE}$ARCHIVE_ROOT/validate-archive.sh${NC}"

echo -e "\n${YELLOW}Usage:${NC}"
echo -e "  Archive a file: ${BLUE}./scripts/archive-file.sh path/to/file.ext \"Reason\"${NC}"
echo -e "  Restore a file: ${BLUE}./scripts/restore-from-archive.sh _archive_/path/to/file.ext${NC}"
echo -e "  Validate archive: ${BLUE}./_archive_/validate-archive.sh${NC}"

echo -e "\n${YELLOW}Next steps:${NC}"
echo -e "1. Review the archive documentation: ${BLUE}$ARCHIVE_ROOT/README.md${NC}"
echo -e "2. Begin archiving files using the provided scripts"
echo -e "3. Proceed with the cleanup phases as outlined in the PRD"

exit 0
