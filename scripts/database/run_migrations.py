#!/usr/bin/env python
"""
Script to run database migrations using Alembic.
"""
import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migrations():
    """Run database migrations using Alembic."""
    try:
        logger.info("Starting database migration...")
        
        # Ensure DATABASE_URL environment variable is set
        database_url = os.environ.get("DATABASE_URL")
        if not database_url:
            logger.error("DATABASE_URL environment variable is not set")
            sys.exit(1)
        
        # Run Alembic upgrade
        logger.info("Running Alembic upgrade to head...")
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True,
            check=False
        )
        
        # Log the output
        if result.stdout:
            logger.info(f"Migration output: {result.stdout}")
        
        # Check for errors
        if result.returncode != 0:
            logger.error(f"Migration failed with exit code {result.returncode}")
            if result.stderr:
                logger.error(f"Error details: {result.stderr}")
            sys.exit(1)
        
        logger.info("Database migration completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        return False

if __name__ == "__main__":
    success = run_migrations()
    sys.exit(0 if success else 1) 