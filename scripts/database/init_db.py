#!/usr/bin/env python
"""
Database initialization script.
"""
import sys
import os
import logging

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import inspect
from api.core.config import settings
from api.db.base_model import Base
from api.core.logging_config import setup_logging
from api.core.dependencies import check_dependencies

# Set up logging
logger = setup_logging(log_level=logging.INFO)

# Check dependencies
dependency_check = check_dependencies({
    "sqlalchemy": ">=2.0.0", 
    "psycopg2-binary": ">=2.9.0"
})
if dependency_check["failure"]:
    logger.warning(f"Some database dependencies have issues: {dependency_check['failure']}")

def init_db():
    """Initialize the database."""
    logger.info("Database initialization script started")

    try:
        # Import the sync engine after config is set up
        from api.db.session import sync_engine
        
        # Import models to ensure they're registered with Base.metadata
        # Import all models that need tables created
        from api.db.models.item import Item
        from api.db.models.user import User
        from api.db.models.file_upload import FileUpload
        from api.db.models.session import Session
        from api.db.models.hash_report import HashReport
        from api.db.models.vagrant_vm import Vagrant_vm
        
        # Create tables
        Base.metadata.create_all(bind=sync_engine)

        # Verify tables were created
        inspector = inspect(sync_engine)
        table_names = inspector.get_table_names()
        logger.info(f"Created tables: {', '.join(table_names) if table_names else 'No tables found'}")

        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    init_db()
