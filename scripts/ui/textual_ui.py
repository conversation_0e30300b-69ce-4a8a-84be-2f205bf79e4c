#!/usr/bin/env python3
"""
TurdParty Service Monitor - Textual UI

A Textual-based TUI for monitoring TurdParty services and their health status.
"""

import datetime
from typing import Dict, List

from textual.app import App, ComposeResult
from textual.containers import Container, Horizontal
from textual.widgets import Header, Footer, Static, DataTable, Button, Label
from textual import work

# Import the service monitoring functions from the main script
from service_monitor import (
    load_config,
    check_service_health,
    get_service_status_emoji,
    get_service_status_color,
    DEFAULT_CONFIG
)

# Global config
config = {}

class ServiceCard(Static):
    """A card displaying service information."""

    def __init__(self, service: Dict, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = service
        self.health = {}

    def compose(self) -> ComposeResult:
        """Compose the service card."""
        yield Label(self.service["name"], classes="service-name")
        yield Label(self.service.get("description", ""), classes="service-description")
        yield Horizontal(
            Label("Status:", classes="label"),
            Label("Unknown", id=f"status-{self.service['name']}", classes="value"),
            classes="status-row"
        )
        if self.service.get("container"):
            yield Horizontal(
                Label("Container:", classes="label"),
                Label(self.service["container"], id=f"container-{self.service['name']}", classes="value"),
                classes="info-row"
            )
        if self.service.get("port"):
            yield Horizontal(
                Label("Port:", classes="label"),
                Label(str(self.service["port"]), id=f"port-{self.service['name']}", classes="value"),
                classes="info-row"
            )
        if self.service.get("url"):
            yield Horizontal(
                Label("URL:", classes="label"),
                Label(self.service["url"], id=f"url-{self.service['name']}", classes="value"),
                classes="info-row"
            )
        yield Button("View Logs", id=f"logs-{self.service['name']}", classes="logs-button")

    def update_health(self, health: Dict):
        """Update the health information for this service."""
        self.health = health

        # Update status
        status_widget = self.query_one(f"#status-{self.service['name']}", Label)
        status_emoji = get_service_status_emoji(health["status"])
        status_text = f"{status_emoji} {health['status'].upper()}"
        status_widget.update(status_text)

        # Update container status if available
        if "container_status" in health and self.service.get("container"):
            container_widget = self.query_one(f"#container-{self.service['name']}", Label)
            container_status = health["container_status"].get("status", "unknown")
            container_widget.update(container_status)

        # Update port status if available
        if "port_open" in health and self.service.get("port"):
            port_widget = self.query_one(f"#port-{self.service['name']}", Label)
            port_status = "OPEN" if health["port_open"] else "CLOSED"
            port_widget.update(f"{self.service['port']} ({port_status})")

        # Update URL status if available
        if "url_accessible" in health and self.service.get("url"):
            url_widget = self.query_one(f"#url-{self.service['name']}", Label)
            url_status = "ACCESSIBLE" if health["url_accessible"] else "INACCESSIBLE"
            url_widget.update(f"{self.service['url']} ({url_status})")

        # Update classes based on status
        status_color = get_service_status_color(health["status"])
        self.add_class(status_color)
        for old_class in ["green", "yellow", "red", "orange1", "grey50", "grey70"]:
            if old_class != status_color:
                self.remove_class(old_class)

class ServiceTable(DataTable):
    """A table displaying service information."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cursor_type = "row"

    def on_mount(self):
        """Set up the table columns."""
        self.add_columns(
            "Service", "Status", "Container", "Port", "URL", "Description"
        )

    def update_services(self, services_health: List[Dict]):
        """Update the table with service health information."""
        self.clear()

        for health in services_health:
            status_emoji = get_service_status_emoji(health["status"])
            status_text = f"{status_emoji} {health['status'].upper()}"

            container_status = "-"
            if "container_status" in health:
                container_status = health["container_status"].get("status", "unknown")

            port_status = str(health.get("port", "-"))
            if "port_open" in health:
                port_status = f"{health['port']} ({'OPEN' if health['port_open'] else 'CLOSED'})"

            url_status = health.get("url", "-")
            if "url_accessible" in health:
                url_status = f"{health['url']} ({'✓' if health['url_accessible'] else '✗'})"

            self.add_row(
                health["name"],
                status_text,
                container_status,
                port_status,
                url_status,
                health["description"]
            )

class TextualApp(App):
    """The Textual TUI application."""

    TITLE = "TurdParty Service Monitor"
    SUB_TITLE = "A Textual-based TUI for monitoring TurdParty services"
    CSS_PATH = "textual_ui.css"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        global config
        config = load_config()
        self.services_health = []

    def compose(self) -> ComposeResult:
        """Compose the application layout."""
        yield Header()
        yield Container(
            Label("Last updated: Never", id="last-updated"),
            ServiceTable(id="service-table"),
            id="main-container"
        )
        yield Footer()

    def on_mount(self):
        """Start the service monitoring when the app is mounted."""
        self.monitor_services()

    @work(exclusive=True)
    async def monitor_services(self):
        """Monitor services in a background worker."""
        while True:
            self.services_health = []

            for service in config["services"]:
                health = check_service_health(service)
                self.services_health.append(health)

            # Update the table
            service_table = self.query_one("#service-table", ServiceTable)
            service_table.update_services(self.services_health)

            # Update the last updated time
            last_updated = self.query_one("#last-updated", Label)
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            last_updated.update(f"Last updated: {now}")

            # Wait for the next refresh
            import asyncio
            await asyncio.sleep(config["refresh_interval"])

if __name__ == "__main__":
    app = TextualApp()
    app.run()
