#!/bin/bash

# TurdParty Coverage Comparison Script
# Compares baseline and post-cleanup coverage reports to ensure no regression

set -e

# Make script executable
chmod +x "$0"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BASELINE_DIR="test-reports/baseline"
POST_CLEANUP_DIR="test-reports/post-cleanup"
COMPARISON_DIR="test-reports/comparison"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
REPORT_FILE="$COMPARISON_DIR/coverage_comparison_$TIMESTAMP.md"
LOG_FILE="$COMPARISON_DIR/comparison_$TIMESTAMP.log"

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}   TurdParty Coverage Comparison Tool    ${NC}"
echo -e "${BLUE}=======================================${NC}"

# Check if both baseline and post-cleanup reports exist
if [ ! -d "$BASELINE_DIR" ]; then
    echo -e "${RED}Error: Baseline coverage not found at $BASELINE_DIR${NC}"
    echo -e "${YELLOW}Please run generate_baseline_coverage.sh first${NC}"
    exit 1
fi

if [ ! -d "$POST_CLEANUP_DIR" ]; then
    echo -e "${RED}Error: Post-cleanup coverage not found at $POST_CLEANUP_DIR${NC}"
    echo -e "${YELLOW}Please run generate_post_cleanup_coverage.sh first${NC}"
    exit 1
fi

# Create comparison directory
mkdir -p "$COMPARISON_DIR"

# Start logging
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "Coverage comparison started at: $(date)"
echo "Baseline directory: $BASELINE_DIR"
echo "Post-cleanup directory: $POST_CLEANUP_DIR"
echo "Comparison directory: $COMPARISON_DIR"

# Function to extract coverage percentage from JSON
extract_python_coverage() {
    local json_file="$1"
    if [ -f "$json_file" ]; then
        python3 -c "
import json
import sys
try:
    with open('$json_file', 'r') as f:
        data = json.load(f)
        totals = data.get('totals', {})
        print(f\"{totals.get('percent_covered', 0):.2f}\")
except Exception as e:
    print('0.00')
"
    else
        echo "0.00"
    fi
}

# Function to count test results
count_test_results() {
    local log_file="$1"
    local pattern="$2"
    if [ -f "$log_file" ]; then
        grep -c "$pattern" "$log_file" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# Function to extract execution time
extract_execution_time() {
    local csv_file="$1"
    local test_type="$2"
    if [ -f "$csv_file" ]; then
        grep "^$test_type," "$csv_file" | cut -d',' -f2 | head -1
    else
        echo "0.00"
    fi
}

# Function to calculate percentage change
calculate_percentage_change() {
    local baseline="$1"
    local current="$2"
    python3 -c "
baseline = float('$baseline') if '$baseline' != '0.00' else 0.01
current = float('$current')
change = ((current - baseline) / baseline) * 100
print(f'{change:+.2f}')
"
}

# Function to determine status color
get_status_color() {
    local change="$1"
    local threshold="$2"
    
    if (( $(echo "$change >= $threshold" | bc -l) )); then
        echo "$GREEN"
    elif (( $(echo "$change >= -1.0" | bc -l) )); then
        echo "$YELLOW"
    else
        echo "$RED"
    fi
}

echo -e "\n${BLUE}=== ANALYZING PYTHON COVERAGE ===${NC}"

# Extract Python coverage data
BASELINE_PY_COVERAGE=$(extract_python_coverage "$BASELINE_DIR/json/python_coverage.json")
POST_PY_COVERAGE=$(extract_python_coverage "$POST_CLEANUP_DIR/json/python_coverage.json")
PY_COVERAGE_CHANGE=$(calculate_percentage_change "$BASELINE_PY_COVERAGE" "$POST_PY_COVERAGE")

echo "Python Coverage:"
echo "  Baseline: ${BASELINE_PY_COVERAGE}%"
echo "  Post-cleanup: ${POST_PY_COVERAGE}%"
echo "  Change: ${PY_COVERAGE_CHANGE}%"

echo -e "\n${BLUE}=== ANALYZING TEST COUNTS ===${NC}"

# Count test results
BASELINE_PY_PASSED=$(count_test_results "$BASELINE_DIR/logs/pytest_output.txt" "passed")
POST_PY_PASSED=$(count_test_results "$POST_CLEANUP_DIR/logs/pytest_output.txt" "passed")
BASELINE_PY_FAILED=$(count_test_results "$BASELINE_DIR/logs/pytest_output.txt" "failed")
POST_PY_FAILED=$(count_test_results "$POST_CLEANUP_DIR/logs/pytest_output.txt" "failed")

echo "Python Test Results:"
echo "  Baseline: $BASELINE_PY_PASSED passed, $BASELINE_PY_FAILED failed"
echo "  Post-cleanup: $POST_PY_PASSED passed, $POST_PY_FAILED failed"

echo -e "\n${BLUE}=== ANALYZING PERFORMANCE ===${NC}"

# Extract execution times
BASELINE_PY_TIME=$(extract_execution_time "$BASELINE_DIR/performance/execution_times.csv" "Running pytest with coverage")
POST_PY_TIME=$(extract_execution_time "$POST_CLEANUP_DIR/performance/execution_times.csv" "Running pytest with coverage")

if [ "$BASELINE_PY_TIME" != "0.00" ] && [ "$POST_PY_TIME" != "0.00" ]; then
    PY_TIME_CHANGE=$(calculate_percentage_change "$BASELINE_PY_TIME" "$POST_PY_TIME")
    echo "Python Test Execution Time:"
    echo "  Baseline: ${BASELINE_PY_TIME}s"
    echo "  Post-cleanup: ${POST_PY_TIME}s"
    echo "  Change: ${PY_TIME_CHANGE}%"
else
    echo "Python Test Execution Time: Data not available"
    PY_TIME_CHANGE="0.00"
fi

echo -e "\n${BLUE}=== ANALYZING FILE STRUCTURE ===${NC}"

# Count files
BASELINE_TEST_FILES=$(wc -l < "$BASELINE_DIR/inventory/test_files.txt" 2>/dev/null || echo "0")
POST_TEST_FILES=$(wc -l < "$POST_CLEANUP_DIR/inventory/test_files.txt" 2>/dev/null || echo "0")
BASELINE_ROOT_FILES=$(wc -l < "$BASELINE_DIR/inventory/root_files.txt" 2>/dev/null || echo "0")
POST_ROOT_FILES=$(wc -l < "$POST_CLEANUP_DIR/inventory/root_files.txt" 2>/dev/null || echo "0")

echo "File Structure Changes:"
echo "  Test files: $BASELINE_TEST_FILES → $POST_TEST_FILES"
echo "  Root files: $BASELINE_ROOT_FILES → $POST_ROOT_FILES"

# Calculate file reduction percentage
if [ "$BASELINE_ROOT_FILES" -gt 0 ]; then
    ROOT_REDUCTION=$(python3 -c "print(f'{((int('$BASELINE_ROOT_FILES') - int('$POST_ROOT_FILES')) / int('$BASELINE_ROOT_FILES')) * 100:.1f}')")
    echo "  Root directory reduction: ${ROOT_REDUCTION}%"
else
    ROOT_REDUCTION="0.0"
fi

echo -e "\n${BLUE}=== GENERATING COMPARISON REPORT ===${NC}"

# Determine overall status
OVERALL_STATUS="PASS"
STATUS_COLOR="$GREEN"

if (( $(echo "$PY_COVERAGE_CHANGE < -5.0" | bc -l) )); then
    OVERALL_STATUS="FAIL"
    STATUS_COLOR="$RED"
elif (( $(echo "$PY_COVERAGE_CHANGE < -1.0" | bc -l) )) || [ "$POST_PY_FAILED" -gt "$BASELINE_PY_FAILED" ]; then
    OVERALL_STATUS="WARNING"
    STATUS_COLOR="$YELLOW"
fi

# Generate detailed comparison report
cat > "$REPORT_FILE" << EOF
# TurdParty Coverage Comparison Report

**Generated:** $(date)  
**Status:** $OVERALL_STATUS  
**Baseline:** $BASELINE_DIR  
**Post-Cleanup:** $POST_CLEANUP_DIR  

## Executive Summary

The cleanup process has been completed and coverage analysis shows:

- **Python Coverage:** ${BASELINE_PY_COVERAGE}% → ${POST_PY_COVERAGE}% (${PY_COVERAGE_CHANGE}%)
- **Test Results:** ${BASELINE_PY_PASSED}/${BASELINE_PY_FAILED} → ${POST_PY_PASSED}/${POST_PY_FAILED} (passed/failed)
- **Root Directory Cleanup:** ${ROOT_REDUCTION}% reduction in files
- **Overall Status:** **$OVERALL_STATUS**

## Detailed Analysis

### Python Coverage Analysis

| Metric | Baseline | Post-Cleanup | Change |
|--------|----------|--------------|--------|
| Line Coverage | ${BASELINE_PY_COVERAGE}% | ${POST_PY_COVERAGE}% | ${PY_COVERAGE_CHANGE}% |
| Tests Passed | $BASELINE_PY_PASSED | $POST_PY_PASSED | $((POST_PY_PASSED - BASELINE_PY_PASSED)) |
| Tests Failed | $BASELINE_PY_FAILED | $POST_PY_FAILED | $((POST_PY_FAILED - BASELINE_PY_FAILED)) |
| Execution Time | ${BASELINE_PY_TIME}s | ${POST_PY_TIME}s | ${PY_TIME_CHANGE}% |

### File Structure Analysis

| Category | Baseline | Post-Cleanup | Change |
|----------|----------|--------------|--------|
| Test Files | $BASELINE_TEST_FILES | $POST_TEST_FILES | $((POST_TEST_FILES - BASELINE_TEST_FILES)) |
| Root Files | $BASELINE_ROOT_FILES | $POST_ROOT_FILES | $((POST_ROOT_FILES - BASELINE_ROOT_FILES)) |
| Root Reduction | - | - | ${ROOT_REDUCTION}% |

### Coverage Thresholds

- ✅ **Acceptable:** Coverage change ≥ -1%
- ⚠️  **Warning:** Coverage change -1% to -5%
- ❌ **Critical:** Coverage change < -5%

### Performance Analysis

EOF

# Add performance analysis
if [ "$PY_TIME_CHANGE" != "0.00" ]; then
    if (( $(echo "$PY_TIME_CHANGE > 20.0" | bc -l) )); then
        echo "- ⚠️ **Performance Regression:** Test execution time increased by ${PY_TIME_CHANGE}%" >> "$REPORT_FILE"
    elif (( $(echo "$PY_TIME_CHANGE < -10.0" | bc -l) )); then
        echo "- ✅ **Performance Improvement:** Test execution time decreased by ${PY_TIME_CHANGE}%" >> "$REPORT_FILE"
    else
        echo "- ✅ **Performance Stable:** Test execution time change within acceptable range (${PY_TIME_CHANGE}%)" >> "$REPORT_FILE"
    fi
else
    echo "- ℹ️ **Performance Data:** Not available for comparison" >> "$REPORT_FILE"
fi

# Add recommendations
cat >> "$REPORT_FILE" << EOF

## Recommendations

EOF

if [ "$OVERALL_STATUS" = "PASS" ]; then
    cat >> "$REPORT_FILE" << EOF
✅ **Cleanup Successful:** All metrics are within acceptable ranges.

- Coverage maintained at acceptable levels
- No significant test failures introduced
- File structure successfully cleaned up
- Performance impact minimal

**Next Steps:**
1. Review the cleaned structure for any remaining optimizations
2. Update documentation to reflect new structure
3. Consider this cleanup complete
EOF
elif [ "$OVERALL_STATUS" = "WARNING" ]; then
    cat >> "$REPORT_FILE" << EOF
⚠️ **Cleanup Completed with Warnings:** Some metrics show minor regressions.

**Issues to Address:**
- Coverage decreased by ${PY_COVERAGE_CHANGE}% (review if acceptable)
- Consider investigating any new test failures

**Next Steps:**
1. Review coverage regression causes
2. Ensure all moved tests are properly configured
3. Consider acceptable if regression is minimal
EOF
else
    cat >> "$REPORT_FILE" << EOF
❌ **Cleanup Requires Attention:** Significant regressions detected.

**Critical Issues:**
- Coverage decreased by ${PY_COVERAGE_CHANGE}% (exceeds -5% threshold)
- Test failures may have increased

**Required Actions:**
1. Investigate coverage regression causes
2. Verify all tests are properly moved and configured
3. Check for missing test files or broken imports
4. Consider rolling back if issues cannot be resolved
EOF
fi

cat >> "$REPORT_FILE" << EOF

## Detailed Reports

- **Baseline Coverage:** [$BASELINE_DIR/html/python/index.html]($BASELINE_DIR/html/python/index.html)
- **Post-Cleanup Coverage:** [$POST_CLEANUP_DIR/html/python/index.html]($POST_CLEANUP_DIR/html/python/index.html)
- **Comparison Log:** [$LOG_FILE]($LOG_FILE)

---
*Report generated by TurdParty Coverage Comparison Tool*
EOF

echo -e "\n${STATUS_COLOR}=======================================${NC}"
echo -e "${STATUS_COLOR}   Coverage Comparison Complete: $OVERALL_STATUS   ${NC}"
echo -e "${STATUS_COLOR}=======================================${NC}"

echo -e "\nComparison report: ${BLUE}$REPORT_FILE${NC}"
echo -e "Detailed log: ${BLUE}$LOG_FILE${NC}"

# Print summary to console
echo -e "\n${CYAN}Summary:${NC}"
echo -e "  Python Coverage: ${BASELINE_PY_COVERAGE}% → ${POST_PY_COVERAGE}% (${PY_COVERAGE_CHANGE}%)"
echo -e "  Root Files Reduced: ${ROOT_REDUCTION}%"
echo -e "  Overall Status: ${STATUS_COLOR}$OVERALL_STATUS${NC}"

if [ "$OVERALL_STATUS" = "FAIL" ]; then
    exit 1
else
    exit 0
fi
