#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update VM templates database with Windows template.
This script simulates updating the database, as we don't have direct DB access.
Instead, it shows what SQL commands would be executed.
"""
import os
import sys
import json
from typing import Dict, Any, List, Optional
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Template data
WINDOWS_TEMPLATE = {
    "value": "windows_10",
    "name": "WINDOWS_10",
    "description": "Windows 10 Professional - Modern Windows desktop OS",
    "box_name": "generic/windows10",
    "box_version": "2102.0.2303",
    "memory_default": 4096,
    "cpus_default": 2,
    "communicator": "winrm",
    "username": "vagrant",
    "password": "vagrant"
}

LINUX_TEMPLATES = {
    "ubuntu": {
        "box_name": "generic/ubuntu2004",
        "memory": 2048,
        "cpus": 2,
        "provider": "libvirt",
        "communicator": "ssh",
        "username": "vagrant",
        "password": "vagrant"
    },
    "debian": {
        "box_name": "generic/debian11",
        "memory": 2048,
        "cpus": 2,
        "provider": "libvirt",
        "communicator": "ssh",
        "username": "vagrant",
        "password": "vagrant"
    },
    "centos": {
        "box_name": "generic/centos8",
        "memory": 2048,
        "cpus": 2,
        "provider": "libvirt",
        "communicator": "ssh",
        "username": "vagrant",
        "password": "vagrant"
    },
    "alpine": {
        "box_name": "generic/alpine312",
        "memory": 512,
        "cpus": 1,
        "provider": "libvirt",
        "communicator": "ssh",
        "username": "vagrant",
        "password": "vagrant"
    }
}

def print_db_update_commands():
    """
    Print the SQL commands that would update the database.
    This is a simulation since we don't have direct DB access.
    """
    print("\n=== SQL Commands to Update Database ===")
    print("-- Check if template already exists")
    print("SELECT * FROM template_enum WHERE value = 'windows_10';")
    print("\n-- If not exists, insert the new template")
    print(f"INSERT INTO template_enum (value, name, description) VALUES")
    print(f"  ('{WINDOWS_TEMPLATE['value']}', '{WINDOWS_TEMPLATE['name']}', '{WINDOWS_TEMPLATE['description']}');")
    
    print("\n-- Insert template configuration")
    print("INSERT INTO template_config (template_id, box_name, box_version, memory_default, cpus_default, communicator)")
    print("SELECT id, " + 
          f"'{WINDOWS_TEMPLATE['box_name']}', " +
          f"'{WINDOWS_TEMPLATE['box_version']}', " +
          f"{WINDOWS_TEMPLATE['memory_default']}, " +
          f"{WINDOWS_TEMPLATE['cpus_default']}, " +
          f"'{WINDOWS_TEMPLATE['communicator']}'")
    print(f"FROM template_enum WHERE value = '{WINDOWS_TEMPLATE['value']}';")

def print_code_update_instructions():
    """
    Print instructions for updating the source code to include the Windows template.
    """
    print("\n=== Code Updates Required ===")
    print("1. Update api/models/vagrant_vm.py - VMTemplate enum:")
    print("   class VMTemplate(str, Enum):")
    print("       # ... existing templates ...")
    print(f"       {WINDOWS_TEMPLATE['name']} = \"{WINDOWS_TEMPLATE['value']}\"")
    print("       # ... other templates ...")
    
    print("\n2. Update api/routes/vagrant_vm.py - get_template_description function:")
    print("   def get_template_description(template: VMTemplate) -> str:")
    print("       descriptions = {")
    print("           # ... existing descriptions ...")
    print(f"           VMTemplate.{WINDOWS_TEMPLATE['name']}: \"{WINDOWS_TEMPLATE['description']}\",")
    print("           # ... other descriptions ...")
    print("       }")

    print("\n3. Verify Vagrant box availability:")
    print(f"   vagrant box add {WINDOWS_TEMPLATE['box_name']} --provider=libvirt")

def check_requirements():
    """Check if required tools are installed."""
    # Check Vagrant
    try:
        subprocess.run(["vagrant", "--version"], check=True, capture_output=True)
        logger.info("✓ Vagrant is installed")
    except subprocess.CalledProcessError:
        logger.error("✗ Vagrant is not installed")
        sys.exit(1)
    except FileNotFoundError:
        logger.error("✗ Vagrant is not installed")
        sys.exit(1)

    # Check libvirt
    try:
        subprocess.run(["virsh", "--version"], check=True, capture_output=True)
        logger.info("✓ libvirt is installed")
    except subprocess.CalledProcessError:
        logger.error("✗ libvirt is not installed")
        sys.exit(1)
    except FileNotFoundError:
        logger.error("✗ libvirt is not installed")
        sys.exit(1)

def check_box_exists(box_name: str) -> bool:
    """Check if a Vagrant box exists."""
    result = subprocess.run(
        ["vagrant", "box", "list", "--machine-readable"],
        capture_output=True,
        text=True
    )
    return box_name in result.stdout

def update_windows_template():
    """Update Windows template."""
    logger.info("Checking Windows template...")
    
    if not check_box_exists(WINDOWS_TEMPLATE["box_name"]):
        logger.warning(f"Windows box {WINDOWS_TEMPLATE['box_name']} not found")
        logger.info(f"Adding box {WINDOWS_TEMPLATE['box_name']}...")
        print(f"   vagrant box add {WINDOWS_TEMPLATE['box_name']} --provider=libvirt")
        return False
    
    return True

def update_linux_templates():
    """Update Linux templates."""
    missing_boxes = []
    
    for name, template in LINUX_TEMPLATES.items():
        logger.info(f"Checking {name} template...")
        
        if not check_box_exists(template["box_name"]):
            logger.warning(f"{name} box {template['box_name']} not found")
            missing_boxes.append(template["box_name"])
    
    if missing_boxes:
        logger.info("Missing boxes:")
        for box in missing_boxes:
            print(f"   vagrant box add {box} --provider=libvirt")
        return False
    
    return True

def main():
    """Main function"""
    logger.info("Starting template update check...")
    
    check_requirements()
    
    windows_ok = update_windows_template()
    linux_ok = update_linux_templates()
    
    if not windows_ok or not linux_ok:
        logger.warning("Some templates need to be added")
        sys.exit(1)
    
    logger.info("All templates are up to date!")

if __name__ == "__main__":
    main() 