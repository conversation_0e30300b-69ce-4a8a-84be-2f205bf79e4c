#!/usr/bin/env python3
"""
Script to register Windows VM template with the Vagrant VM API
"""
import requests
import json
import sys
import time
import os
from typing import Dict, Any, Optional

# Configuration
API_BASE = "http://localhost:3050/api/v1"

def get_auth_token() -> Optional[str]:
    """Get authentication token for API access"""
    print("Getting auth token...")
    try:
        resp = requests.post(f"{API_BASE}/auth/test-token")
        if resp.status_code == 200:
            token = resp.json().get("access_token")
            if token:
                print("Token obtained successfully")
                return token
        
        print(f"Failed to get token. Status code: {resp.status_code}")
        print(f"Response: {resp.text}")
        return None
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def check_existing_templates(token: str) -> bool:
    """Check if the Windows template already exists"""
    print("\nChecking existing templates...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        resp = requests.get(f"{API_BASE}/vagrant_vm/templates", headers=headers)
        print(f"Status code: {resp.status_code}")
        
        if resp.status_code == 200:
            templates = resp.json()
            print(f"Found {len(templates)} templates")
            
            # Check if Windows 10 is already in the templates
            for template in templates:
                if template.get("value") == "windows_10":
                    print("Windows 10 template already registered")
                    return True
            
            print("Windows 10 template not found")
            return False
        else:
            print(f"Failed to fetch templates: {resp.text}")
            return False
    except Exception as e:
        print(f"Error checking templates: {e}")
        return False

def read_vagrantfile() -> str:
    """Read the Windows Vagrantfile content"""
    try:
        with open("windows_template/Vagrantfile", "r") as f:
            return f.read()
    except Exception as e:
        print(f"Error reading Vagrantfile: {e}")
        sys.exit(1)

def update_db_template() -> None:
    """
    This function would normally update the database with the new template,
    but since we don't have direct database access, we're simulating the process.
    In a real scenario, this would involve modifying the database or API endpoints.
    """
    print("\nUpdating template in database (simulation)...")
    print("In a real implementation, this would:")
    print("1. Add the 'windows_10' template to the VMTemplate enum if not already present")
    print("2. Update the database with the template details")
    print("3. Make the template available in the UI")
    print("\nTemplate registration simulation complete!")

def main():
    """Main function to register the Windows template"""
    print("=== Windows VM Template Registration ===")
    
    # Get the Vagrantfile content
    vagrantfile_content = read_vagrantfile()
    print(f"Read Vagrantfile ({len(vagrantfile_content)} bytes)")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token. Exiting.")
        sys.exit(1)
    
    # Check if template already exists
    exists = check_existing_templates(token)
    
    if exists:
        print("\nWindows 10 template is already registered. No action needed.")
    else:
        # Register the template (simulation)
        update_db_template()
        
        print("\nTo make this template fully functional:")
        print("1. Ensure the VMTemplate enum includes 'WINDOWS_10 = \"windows_10\"'")
        print("2. Make sure the vagrant_vm.py route handler includes Windows 10 in get_template_description()")
        print("3. Have the VM service correctly handle Windows-specific configurations")
        print("4. Ensure the proper Vagrant box is available on the system")

if __name__ == "__main__":
    main() 