#!/bin/bash

# Script to start the Vagrant gRPC proxy server

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Vagrant gRPC proxy server...${NC}"

# Check if Python is installed
if ! command -v python &> /dev/null; then
    echo -e "${RED}Python is not installed${NC}"
    exit 1
fi

# Check if the proxy script exists
if [ ! -f "api/grpc/vagrant_proxy.py" ]; then
    echo -e "${RED}Proxy script not found${NC}"
    exit 1
fi

# Start the proxy server
echo -e "${YELLOW}Starting proxy server on port 50051...${NC}"
python -m api.grpc.vagrant_proxy --port 50051 --target localhost:40000

echo -e "${GREEN}Proxy server started${NC}"
