#!/bin/bash

# TurdParty Post-Cleanup Coverage Generation Script
# This script generates coverage reports after cleanup for comparison with baseline

set -e

# Make script executable
chmod +x "$0"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
POST_CLEANUP_DIR="test-reports/post-cleanup"
BASELINE_DIR="test-reports/baseline"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$POST_CLEANUP_DIR/post_cleanup_generation_$TIMESTAMP.log"

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}  TurdParty Post-Cleanup Coverage Report ${NC}"
echo -e "${BLUE}=======================================${NC}"

# Check if baseline exists
if [ ! -d "$BASELINE_DIR" ]; then
    echo -e "${RED}Error: Baseline coverage not found at $BASELINE_DIR${NC}"
    echo -e "${YELLOW}Please run generate_baseline_coverage.sh first${NC}"
    exit 1
fi

# Create post-cleanup directory structure
echo -e "${YELLOW}Setting up post-cleanup directory structure...${NC}"
mkdir -p "$POST_CLEANUP_DIR"/{html,xml,json,logs,inventory,performance}

# Start logging
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "Post-cleanup coverage generation started at: $(date)"
echo "Post-cleanup directory: $POST_CLEANUP_DIR"
echo "Baseline directory: $BASELINE_DIR"
echo "Log file: $LOG_FILE"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run command with error handling
run_with_error_handling() {
    local cmd="$1"
    local description="$2"
    
    echo -e "\n${YELLOW}$description...${NC}"
    echo "Executing: $cmd"
    
    if eval "$cmd"; then
        echo -e "${GREEN}✓ $description completed successfully${NC}"
        return 0
    else
        echo -e "${RED}✗ $description failed${NC}"
        return 1
    fi
}

# Function to measure execution time
measure_time() {
    local start_time=$(date +%s.%N)
    "$@"
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    echo "Execution time: ${duration}s"
    echo "$1,$duration" >> "$POST_CLEANUP_DIR/performance/execution_times.csv"
}

# Initialize performance tracking
echo "Test Type,Execution Time (seconds)" > "$POST_CLEANUP_DIR/performance/execution_times.csv"

# 1. Verify new structure
echo -e "\n${BLUE}=== VERIFYING NEW STRUCTURE ===${NC}"

run_with_error_handling \
    "find . -type f -name '*.py' -o -name '*.js' -o -name '*.ts' | grep -E '(test_|_test\.|\.test\.|\.spec\.)' > $POST_CLEANUP_DIR/inventory/test_files.txt" \
    "Cataloging test files in new structure"

run_with_error_handling \
    "find . -maxdepth 1 -type f -exec ls -lh {} \; > $POST_CLEANUP_DIR/inventory/root_files.txt" \
    "Cataloging root directory files after cleanup"

# Compare file counts
BASELINE_TEST_COUNT=$(wc -l < "$BASELINE_DIR/inventory/test_files.txt" 2>/dev/null || echo "0")
POST_TEST_COUNT=$(wc -l < "$POST_CLEANUP_DIR/inventory/test_files.txt")
BASELINE_ROOT_COUNT=$(wc -l < "$BASELINE_DIR/inventory/root_files.txt" 2>/dev/null || echo "0")
POST_ROOT_COUNT=$(wc -l < "$POST_CLEANUP_DIR/inventory/root_files.txt")

echo "File count comparison:"
echo "  Test files: $BASELINE_TEST_COUNT (baseline) → $POST_TEST_COUNT (post-cleanup)"
echo "  Root files: $BASELINE_ROOT_COUNT (baseline) → $POST_ROOT_COUNT (post-cleanup)"

# 2. Python/pytest coverage (using same parameters as baseline)
echo -e "\n${BLUE}=== GENERATING PYTHON COVERAGE ===${NC}"

if command_exists python && command_exists pytest; then
    # Set Python path
    export PYTHONPATH=.
    
    # Run pytest with same coverage settings as baseline
    measure_time run_with_error_handling \
        "python -m pytest \
            --cov=api \
            --cov=db \
            --cov=ui \
            --cov=scripts \
            --cov-report=html:$POST_CLEANUP_DIR/html/python \
            --cov-report=xml:$POST_CLEANUP_DIR/xml/python_coverage.xml \
            --cov-report=json:$POST_CLEANUP_DIR/json/python_coverage.json \
            --cov-report=term \
            -v \
            --tb=short \
            --maxfail=10 \
            > $POST_CLEANUP_DIR/logs/pytest_output.txt 2>&1" \
        "Running pytest with coverage (post-cleanup)"
    
    # Generate coverage summary
    if [ -f "$POST_CLEANUP_DIR/json/python_coverage.json" ]; then
        python -c "
import json
with open('$POST_CLEANUP_DIR/json/python_coverage.json', 'r') as f:
    data = json.load(f)
    summary = data.get('totals', {})
    print(f'Python Coverage Summary (Post-Cleanup):')
    print(f'  Lines: {summary.get(\"percent_covered\", 0):.2f}%')
    print(f'  Branches: {summary.get(\"percent_covered_display\", \"N/A\")}')
    print(f'  Files covered: {len(data.get(\"files\", {}))}')
" > "$POST_CLEANUP_DIR/logs/python_coverage_summary.txt"
    fi
else
    echo -e "${RED}Python or pytest not available, skipping Python coverage${NC}"
fi

# 3. JavaScript/Node.js coverage
echo -e "\n${BLUE}=== GENERATING JAVASCRIPT COVERAGE ===${NC}"

if command_exists npm && [ -f "package.json" ]; then
    if npm list jest >/dev/null 2>&1; then
        measure_time run_with_error_handling \
            "npm test -- --coverage --coverageDirectory=$POST_CLEANUP_DIR/html/javascript --coverageReporters=html,json,text > $POST_CLEANUP_DIR/logs/jest_output.txt 2>&1" \
            "Running Jest with coverage (post-cleanup)"
    else
        echo -e "${YELLOW}Jest not configured, skipping JavaScript unit test coverage${NC}"
    fi
else
    echo -e "${YELLOW}Node.js/npm not available, skipping JavaScript coverage${NC}"
fi

# 4. Playwright E2E coverage
echo -e "\n${BLUE}=== GENERATING PLAYWRIGHT COVERAGE ===${NC}"

if command_exists npx && [ -f "playwright.config.js" ]; then
    mkdir -p "$POST_CLEANUP_DIR/html/playwright"
    
    measure_time run_with_error_handling \
        "npx playwright test --reporter=html --output-dir=$POST_CLEANUP_DIR/html/playwright > $POST_CLEANUP_DIR/logs/playwright_output.txt 2>&1" \
        "Running Playwright tests (post-cleanup)"
    
    if [ -d "playwright-report" ]; then
        cp -r playwright-report/* "$POST_CLEANUP_DIR/html/playwright/" 2>/dev/null || true
    fi
else
    echo -e "${YELLOW}Playwright not available, skipping E2E coverage${NC}"
fi

# 5. Integration test coverage
echo -e "\n${BLUE}=== RUNNING INTEGRATION TESTS ===${NC}"

if [ -f "run-all-tests.sh" ]; then
    measure_time run_with_error_handling \
        "timeout 600 ./run-all-tests.sh > $POST_CLEANUP_DIR/logs/integration_tests.txt 2>&1" \
        "Running integration tests (post-cleanup, 10 min timeout)"
elif [ -f "run_integration_tests.sh" ]; then
    measure_time run_with_error_handling \
        "timeout 600 ./run_integration_tests.sh > $POST_CLEANUP_DIR/logs/integration_tests.txt 2>&1" \
        "Running integration tests (post-cleanup, 10 min timeout)"
else
    echo -e "${YELLOW}No integration test script found, skipping${NC}"
fi

# 6. Generate system information
echo -e "\n${BLUE}=== COLLECTING SYSTEM INFORMATION ===${NC}"

cat > "$POST_CLEANUP_DIR/logs/system_info.txt" << EOF
System Information - Post-Cleanup Coverage Generation
Generated: $(date)
Hostname: $(hostname)
OS: $(uname -a)
Python Version: $(python --version 2>&1 || echo "Not available")
Node Version: $(node --version 2>&1 || echo "Not available")
Git Commit: $(git rev-parse HEAD 2>&1 || echo "Not available")
Git Branch: $(git branch --show-current 2>&1 || echo "Not available")
Working Directory: $(pwd)
EOF

# 7. Copy current dependency files
echo -e "\n${BLUE}=== COLLECTING DEPENDENCY INFORMATION ===${NC}"

for file in requirements.txt pyproject.toml package.json package-lock.json; do
    if [ -f "$file" ]; then
        cp "$file" "$POST_CLEANUP_DIR/inventory/"
    fi
done

# 8. Generate post-cleanup summary report
echo -e "\n${BLUE}=== GENERATING POST-CLEANUP SUMMARY ===${NC}"

cat > "$POST_CLEANUP_DIR/post_cleanup_summary.md" << EOF
# TurdParty Post-Cleanup Coverage Report

Generated: $(date)
Post-Cleanup Directory: $POST_CLEANUP_DIR
Baseline Directory: $BASELINE_DIR

## Test Execution Summary

### Python Tests
- Coverage Report: [HTML]($POST_CLEANUP_DIR/html/python/index.html)
- Coverage Data: [JSON]($POST_CLEANUP_DIR/json/python_coverage.json)
- Test Output: [Log]($POST_CLEANUP_DIR/logs/pytest_output.txt)

### JavaScript Tests
- Coverage Report: [HTML]($POST_CLEANUP_DIR/html/javascript/index.html)
- Test Output: [Log]($POST_CLEANUP_DIR/logs/jest_output.txt)

### Playwright E2E Tests
- Test Report: [HTML]($POST_CLEANUP_DIR/html/playwright/index.html)
- Test Output: [Log]($POST_CLEANUP_DIR/logs/playwright_output.txt)

### Integration Tests
- Test Output: [Log]($POST_CLEANUP_DIR/logs/integration_tests.txt)

## Performance Metrics
- Execution Times: [CSV]($POST_CLEANUP_DIR/performance/execution_times.csv)

## File Inventory Changes
- Test Files: $BASELINE_TEST_COUNT → $POST_TEST_COUNT
- Root Files: $BASELINE_ROOT_COUNT → $POST_ROOT_COUNT

## Next Steps
1. Run compare_coverage_reports.sh to analyze differences
2. Review any coverage regressions
3. Validate all functionality works as expected
EOF

# 9. Create metadata for comparison
cat > "$POST_CLEANUP_DIR/metadata.json" << EOF
{
    "generation_date": "$(date -Iseconds)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
    "post_cleanup_dir": "$POST_CLEANUP_DIR",
    "baseline_dir": "$BASELINE_DIR",
    "file_counts": {
        "test_files_baseline": $BASELINE_TEST_COUNT,
        "test_files_post": $POST_TEST_COUNT,
        "root_files_baseline": $BASELINE_ROOT_COUNT,
        "root_files_post": $POST_ROOT_COUNT
    },
    "reports": {
        "python_coverage_json": "$POST_CLEANUP_DIR/json/python_coverage.json",
        "python_coverage_xml": "$POST_CLEANUP_DIR/xml/python_coverage.xml",
        "python_coverage_html": "$POST_CLEANUP_DIR/html/python/index.html",
        "playwright_html": "$POST_CLEANUP_DIR/html/playwright/index.html",
        "execution_times": "$POST_CLEANUP_DIR/performance/execution_times.csv"
    }
}
EOF

echo -e "\n${GREEN}=======================================${NC}"
echo -e "${GREEN}  Post-Cleanup Coverage Generation Complete ${NC}"
echo -e "${GREEN}=======================================${NC}"

echo -e "\nPost-cleanup reports generated in: ${BLUE}$POST_CLEANUP_DIR${NC}"
echo -e "Summary report: ${BLUE}$POST_CLEANUP_DIR/post_cleanup_summary.md${NC}"
echo -e "Log file: ${BLUE}$LOG_FILE${NC}"

echo -e "\n${YELLOW}Next steps:${NC}"
echo -e "1. Review the generated reports"
echo -e "2. Run compare_coverage_reports.sh to analyze differences"
echo -e "3. Address any coverage regressions if found"

exit 0
