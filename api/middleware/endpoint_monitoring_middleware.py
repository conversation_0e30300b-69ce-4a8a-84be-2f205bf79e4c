"""
Middleware for monitoring endpoint usage.

This middleware logs all requests to VM-related endpoints to track the migration
from old endpoints to the new consolidated endpoints.
"""
import time
import logging
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from api.core.config import settings

# Set up a dedicated logger for endpoint monitoring
logger = logging.getLogger("endpoint_monitoring")
logger.setLevel(logging.INFO)

# Create a file handler
import os
os.makedirs("logs", exist_ok=True)
handler = logging.FileHandler("logs/endpoint_monitoring.log")
handler.setLevel(logging.INFO)

# Create a formatter
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
handler.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(handler)


class EndpointMonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware for monitoring endpoint usage.
    
    This middleware logs all requests to VM-related endpoints to track the migration
    from old endpoints to the new consolidated endpoints.
    """
    
    async def dispatch(self, request: Request, call_next):
        """
        Process the request and log VM-related endpoint usage.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            The response from the next middleware or route handler
        """
        # Get the request path
        path = request.url.path
        
        # Check if it's a VM-related endpoint
        is_old_endpoint = any([
            "/api/v1/vagrant/" in path,
            "/api/v1/vagrant_vm/" in path,
            "/api/v1/vagrant-vm/" in path,
            "/api/v1/vm_injection/" in path,
            "/api/v1/vm-injection/" in path
        ])
        
        is_new_endpoint = "/api/v1/virtual-machines" in path
        
        # Start timer for response time measurement
        start_time = time.time()
        
        # Process the request
        response = await call_next(request)
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # If it's a VM-related endpoint, log the request
        if is_old_endpoint or is_new_endpoint:
            # Get client information
            client_ip = request.client.host if request.client else "unknown"
            user_agent = request.headers.get("user-agent", "unknown")
            auth_header = request.headers.get("authorization", "")
            
            # Anonymize the auth header (just keep the type)
            if auth_header:
                auth_parts = auth_header.split()
                if len(auth_parts) > 0:
                    auth_type = auth_parts[0]
                    auth_header = f"{auth_type} [REDACTED]"
            
            # Log the request
            logger.info(
                f"VM Endpoint Request: "
                f"path={path}, "
                f"method={request.method}, "
                f"status_code={response.status_code}, "
                f"response_time={response_time:.4f}s, "
                f"client_ip={client_ip}, "
                f"user_agent={user_agent}, "
                f"is_old_endpoint={is_old_endpoint}, "
                f"is_new_endpoint={is_new_endpoint}"
            )
        
        return response
