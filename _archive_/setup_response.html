<!-- ARCHIVED FILE -->
<!-- Original Location: /setup_response.html -->
<!-- Archived Date: 2025-06-03 23:56:16 -->
<!-- Reason: Root directory cleanup - temporary/debug file -->
<!-- File Size: 149991 bytes -->
<!-- Last Modified: 2025-05-15 19:09:39.296744971 +0200 -->
<!-- File Type: HTML document, ASCII text -->
<!-- Dependencies: None identified -->
<!-- References: 6 potential references found in codebase -->
<!--  -->
<!-- [Original file content follows...] -->
<!--  -->


<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <meta name="env" content="production">
    <meta name="token" content="p0oFtYL26hHrKfK791kHjpWOgwO20ZWRAsBYUrSP">

    <link rel="icon" type="image/png" href="http://localhost:3501/img/favicon.ico">
    <link rel="shortcut icon" href="http://localhost:3501/img/favicon.png" type="image/x-icon">

    <link rel="apple-touch-icon" href="http://localhost:3501/img/apple-touch-icon.png">
    <link rel="apple-touch-icon" sizes="57x57" href="http://localhost:3501/img/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="72x72" href="http://localhost:3501/img/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="114x114" href="http://localhost:3501/img/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="http://localhost:3501/img/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="http://localhost:3501/img/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="http://localhost:3501/img/apple-touch-icon-152x152.png">

    <title>Setup</title>

        
        <link rel="stylesheet" href="http://localhost:3501/dist/css/dashboard/dashboard.css?id=b6a9327dd3b9b7c7ed2d">
    
    
    <script type="text/javascript">
        var Global = {};
        Global.locale = '';
    </script>

    <script src="http://localhost:3501/dist/js/manifest.js?id=40dcfff9d09d402daf38"></script>
    <script src="http://localhost:3501/dist/js/vendor.js?id=e330d11061bb920e9441"></script>
</head>

<body class="">
    <div class="content" id="app">
        <div class="setup-page">
    <div class="text-center">
        <img class="logo" height="90" src="http://localhost:3501/img/cachet-logo.svg" alt="Install Cachet">
    </div>
    <div class="col-xs-12 col-xs-offset-0 col-sm-8 col-sm-offset-2">
        <div class="steps">
            <div class="step active">
                Environment Setup
                <span></span>
            </div>
            <div class="step">
                Status Page Setup
                <span></span>
            </div>
            <div class="step">
                Administrator Account
                <span></span>
            </div>
            <div class="step">
                Complete Setup
                <span></span>
            </div>
        </div>

        <div class="clearfix"></div>

        <setup inline-template>
            <form class="form-horizontal" name="SetupForm" method="POST" id="setup-form" role="form">
                <div class="step block-1">
                    <fieldset>
                        <div class="form-group">
                            <div class="row">
                                <div class="col-xs-4">
                                    <label>Cache Driver</label>
                                    <select name="env[cache_driver]" class="form-control" required v-model="env.cache_driver">
                                        <option disabled>Cache Driver</option>
                                                                                <option value="apc" selected>APC(u)</option>
                                                                                <option value="array" >Array</option>
                                                                                <option value="database" >Database</option>
                                                                                <option value="file" >File</option>
                                                                                <option value="memcached" >Memcached</option>
                                                                                <option value="redis" >Redis</option>
                                                                            </select>
                                                                    </div>
                                <div class="col-xs-4">
                                    <label>Queue Driver</label>
                                    <select name="env[queue_driver]" class="form-control" required v-model="env.queue_driver">
                                        <option disabled>Queue Driver</option>
                                                                                <option value="null" >None</option>
                                                                                <option value="sync" >Synchronous</option>
                                                                                <option value="database" selected>Database</option>
                                                                                <option value="beanstalkd" >Beanstalk</option>
                                                                                <option value="sqs" >Amazon SQS</option>
                                                                                <option value="redis" >Redis</option>
                                                                            </select>
                                                                    </div>
                                <div class="col-xs-4">
                                    <label>Session Driver</label>
                                    <select name="env[session_driver]" class="form-control" required v-model="env.session_driver">
                                        <option disabled>Session Driver</option>
                                                                                <option value="apc" selected>APC(u)</option>
                                                                                <option value="array" >Array</option>
                                                                                <option value="database" >Database</option>
                                                                                <option value="file" >File</option>
                                                                                <option value="memcached" >Memcached</option>
                                                                                <option value="redis" >Redis</option>
                                                                            </select>
                                                                    </div>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label>Mail Driver</label>
                            <select name="env[mail_driver]" class="form-control" required v-model="env.mail_driver">
                                <option disabled>Mail Driver</option>
                                                                <option value="smtp" selected>SMTP</option>
                                                                <option value="mail" >Mail</option>
                                                                <option value="sendmail" >Sendmail</option>
                                                                <option value="mailgun" >Mailgun</option>
                                                                <option value="mandrill" >Mandrill</option>
                                                                <option value="ses" >Amazon SES</option>
                                                                <option value="sparkpost" >SparkPost</option>
                                                                <option value="log" >Log (Testing)</option>
                                                            </select>
                                                    </div>
                        <div class="form-group">
                            <label>Mail Host</label>
                            <input type="text" class="form-control" name="env[mail_host]" value="localhost" placeholder="Mail Host" :required="mail.requiresHost">
                                                    </div>
                        <div class="form-group">
                            <label>Mail From Address</label>
                            <input type="text" class="form-control" name="env[mail_address]" value="" placeholder="<EMAIL>">
                                                    </div>
                        <div class="form-group">
                            <label>Mail Username</label>
                            <input type="text" class="form-control" name="env[mail_username]" value="" placeholder="Mail Username" :required="mail.requiresUsername">
                                                    </div>
                        <div class="form-group">
                            <label>Mail Password</label>
                            <input type="password" class="form-control" name="env[mail_password]" value="" autocomplete="off" placeholder="Mail Password" :required="mail.requiresUsername">
                                                    </div>
                    </fieldset>
                    <hr>
                    <div class="form-group text-center">
                        <span class="wizard-next btn btn-success" data-current-block="1" data-next-block="2" data-loading-text="<i class='icon ion-load-c'></i>">
                            Next
                        </span>
                    </div>
                </div>
                <div class="step block-2 hidden">
                    <fieldset>
                        <div class="form-group">
                            <label>Site Name</label>
                            <input type="text" name="settings[app_name]" class="form-control" placeholder="Site Name" value="" required>
                                                    </div>
                        <div class="form-group">
                            <label>Site Domain</label>
                            <input type="text" name="settings[app_domain]" class="form-control" placeholder="Site Domain" value="http://localhost:3501" required>
                                                    </div>
                        <div class="form-group">
                            <label>Select your timezone</label>
                            <select name="settings[app_timezone]" class="form-control" required>
                                <option value="">Select Timezone</option>
                                                                <optgroup label="UTC">
                                                                <option value="UTC" >
                                    UTC - 17:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Africa">
                                                                <option value="Africa/Abidjan" >
                                    Abidjan - 17:09
                                </option>
                                                                <option value="Africa/Accra" >
                                    Accra - 17:09
                                </option>
                                                                <option value="Africa/Addis_Ababa" >
                                    Addis Ababa - 20:09
                                </option>
                                                                <option value="Africa/Algiers" >
                                    Algiers - 18:09
                                </option>
                                                                <option value="Africa/Asmara" >
                                    Asmara - 20:09
                                </option>
                                                                <option value="Africa/Bamako" >
                                    Bamako - 17:09
                                </option>
                                                                <option value="Africa/Bangui" >
                                    Bangui - 18:09
                                </option>
                                                                <option value="Africa/Banjul" >
                                    Banjul - 17:09
                                </option>
                                                                <option value="Africa/Bissau" >
                                    Bissau - 17:09
                                </option>
                                                                <option value="Africa/Blantyre" >
                                    Blantyre - 19:09
                                </option>
                                                                <option value="Africa/Brazzaville" >
                                    Brazzaville - 18:09
                                </option>
                                                                <option value="Africa/Bujumbura" >
                                    Bujumbura - 19:09
                                </option>
                                                                <option value="Africa/Cairo" >
                                    Cairo - 19:09
                                </option>
                                                                <option value="Africa/Casablanca" >
                                    Casablanca - 18:09
                                </option>
                                                                <option value="Africa/Ceuta" >
                                    Ceuta - 19:09
                                </option>
                                                                <option value="Africa/Conakry" >
                                    Conakry - 17:09
                                </option>
                                                                <option value="Africa/Dakar" >
                                    Dakar - 17:09
                                </option>
                                                                <option value="Africa/Dar_es_Salaam" >
                                    Dar es Salaam - 20:09
                                </option>
                                                                <option value="Africa/Djibouti" >
                                    Djibouti - 20:09
                                </option>
                                                                <option value="Africa/Douala" >
                                    Douala - 18:09
                                </option>
                                                                <option value="Africa/El_Aaiun" >
                                    El Aaiun - 18:09
                                </option>
                                                                <option value="Africa/Freetown" >
                                    Freetown - 17:09
                                </option>
                                                                <option value="Africa/Gaborone" >
                                    Gaborone - 19:09
                                </option>
                                                                <option value="Africa/Harare" >
                                    Harare - 19:09
                                </option>
                                                                <option value="Africa/Johannesburg" >
                                    Johannesburg - 19:09
                                </option>
                                                                <option value="Africa/Juba" >
                                    Juba - 20:09
                                </option>
                                                                <option value="Africa/Kampala" >
                                    Kampala - 20:09
                                </option>
                                                                <option value="Africa/Khartoum" >
                                    Khartoum - 19:09
                                </option>
                                                                <option value="Africa/Kigali" >
                                    Kigali - 19:09
                                </option>
                                                                <option value="Africa/Kinshasa" >
                                    Kinshasa - 18:09
                                </option>
                                                                <option value="Africa/Lagos" >
                                    Lagos - 18:09
                                </option>
                                                                <option value="Africa/Libreville" >
                                    Libreville - 18:09
                                </option>
                                                                <option value="Africa/Lome" >
                                    Lome - 17:09
                                </option>
                                                                <option value="Africa/Luanda" >
                                    Luanda - 18:09
                                </option>
                                                                <option value="Africa/Lubumbashi" >
                                    Lubumbashi - 19:09
                                </option>
                                                                <option value="Africa/Lusaka" >
                                    Lusaka - 19:09
                                </option>
                                                                <option value="Africa/Malabo" >
                                    Malabo - 18:09
                                </option>
                                                                <option value="Africa/Maputo" >
                                    Maputo - 19:09
                                </option>
                                                                <option value="Africa/Maseru" >
                                    Maseru - 19:09
                                </option>
                                                                <option value="Africa/Mbabane" >
                                    Mbabane - 19:09
                                </option>
                                                                <option value="Africa/Mogadishu" >
                                    Mogadishu - 20:09
                                </option>
                                                                <option value="Africa/Monrovia" >
                                    Monrovia - 17:09
                                </option>
                                                                <option value="Africa/Nairobi" >
                                    Nairobi - 20:09
                                </option>
                                                                <option value="Africa/Ndjamena" >
                                    Ndjamena - 18:09
                                </option>
                                                                <option value="Africa/Niamey" >
                                    Niamey - 18:09
                                </option>
                                                                <option value="Africa/Nouakchott" >
                                    Nouakchott - 17:09
                                </option>
                                                                <option value="Africa/Ouagadougou" >
                                    Ouagadougou - 17:09
                                </option>
                                                                <option value="Africa/Porto-Novo" >
                                    Porto-Novo - 18:09
                                </option>
                                                                <option value="Africa/Sao_Tome" >
                                    Sao Tome - 17:09
                                </option>
                                                                <option value="Africa/Tripoli" >
                                    Tripoli - 19:09
                                </option>
                                                                <option value="Africa/Tunis" >
                                    Tunis - 18:09
                                </option>
                                                                <option value="Africa/Windhoek" >
                                    Windhoek - 19:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="America">
                                                                <option value="America/Adak" >
                                    Adak - 08:09
                                </option>
                                                                <option value="America/Anchorage" >
                                    Anchorage - 09:09
                                </option>
                                                                <option value="America/Anguilla" >
                                    Anguilla - 13:09
                                </option>
                                                                <option value="America/Antigua" >
                                    Antigua - 13:09
                                </option>
                                                                <option value="America/Araguaina" >
                                    Araguaina - 14:09
                                </option>
                                                                <option value="America/Argentina/Buenos_Aires" >
                                    Argentina/Buenos Aires - 14:09
                                </option>
                                                                <option value="America/Argentina/Catamarca" >
                                    Argentina/Catamarca - 14:09
                                </option>
                                                                <option value="America/Argentina/Cordoba" >
                                    Argentina/Cordoba - 14:09
                                </option>
                                                                <option value="America/Argentina/Jujuy" >
                                    Argentina/Jujuy - 14:09
                                </option>
                                                                <option value="America/Argentina/La_Rioja" >
                                    Argentina/La Rioja - 14:09
                                </option>
                                                                <option value="America/Argentina/Mendoza" >
                                    Argentina/Mendoza - 14:09
                                </option>
                                                                <option value="America/Argentina/Rio_Gallegos" >
                                    Argentina/Rio Gallegos - 14:09
                                </option>
                                                                <option value="America/Argentina/Salta" >
                                    Argentina/Salta - 14:09
                                </option>
                                                                <option value="America/Argentina/San_Juan" >
                                    Argentina/San Juan - 14:09
                                </option>
                                                                <option value="America/Argentina/San_Luis" >
                                    Argentina/San Luis - 14:09
                                </option>
                                                                <option value="America/Argentina/Tucuman" >
                                    Argentina/Tucuman - 14:09
                                </option>
                                                                <option value="America/Argentina/Ushuaia" >
                                    Argentina/Ushuaia - 14:09
                                </option>
                                                                <option value="America/Aruba" >
                                    Aruba - 13:09
                                </option>
                                                                <option value="America/Asuncion" >
                                    Asuncion - 13:09
                                </option>
                                                                <option value="America/Atikokan" >
                                    Atikokan - 12:09
                                </option>
                                                                <option value="America/Bahia" >
                                    Bahia - 14:09
                                </option>
                                                                <option value="America/Bahia_Banderas" >
                                    Bahia Banderas - 12:09
                                </option>
                                                                <option value="America/Barbados" >
                                    Barbados - 13:09
                                </option>
                                                                <option value="America/Belem" >
                                    Belem - 14:09
                                </option>
                                                                <option value="America/Belize" >
                                    Belize - 11:09
                                </option>
                                                                <option value="America/Blanc-Sablon" >
                                    Blanc-Sablon - 13:09
                                </option>
                                                                <option value="America/Boa_Vista" >
                                    Boa Vista - 13:09
                                </option>
                                                                <option value="America/Bogota" >
                                    Bogota - 12:09
                                </option>
                                                                <option value="America/Boise" >
                                    Boise - 11:09
                                </option>
                                                                <option value="America/Cambridge_Bay" >
                                    Cambridge Bay - 11:09
                                </option>
                                                                <option value="America/Campo_Grande" >
                                    Campo Grande - 13:09
                                </option>
                                                                <option value="America/Cancun" >
                                    Cancun - 12:09
                                </option>
                                                                <option value="America/Caracas" >
                                    Caracas - 13:09
                                </option>
                                                                <option value="America/Cayenne" >
                                    Cayenne - 14:09
                                </option>
                                                                <option value="America/Cayman" >
                                    Cayman - 12:09
                                </option>
                                                                <option value="America/Chicago" >
                                    Chicago - 12:09
                                </option>
                                                                <option value="America/Chihuahua" >
                                    Chihuahua - 11:09
                                </option>
                                                                <option value="America/Costa_Rica" >
                                    Costa Rica - 11:09
                                </option>
                                                                <option value="America/Creston" >
                                    Creston - 10:09
                                </option>
                                                                <option value="America/Cuiaba" >
                                    Cuiaba - 13:09
                                </option>
                                                                <option value="America/Curacao" >
                                    Curacao - 13:09
                                </option>
                                                                <option value="America/Danmarkshavn" >
                                    Danmarkshavn - 17:09
                                </option>
                                                                <option value="America/Dawson" >
                                    Dawson - 10:09
                                </option>
                                                                <option value="America/Dawson_Creek" >
                                    Dawson Creek - 10:09
                                </option>
                                                                <option value="America/Denver" >
                                    Denver - 11:09
                                </option>
                                                                <option value="America/Detroit" >
                                    Detroit - 13:09
                                </option>
                                                                <option value="America/Dominica" >
                                    Dominica - 13:09
                                </option>
                                                                <option value="America/Edmonton" >
                                    Edmonton - 11:09
                                </option>
                                                                <option value="America/Eirunepe" >
                                    Eirunepe - 12:09
                                </option>
                                                                <option value="America/El_Salvador" >
                                    El Salvador - 11:09
                                </option>
                                                                <option value="America/Fort_Nelson" >
                                    Fort Nelson - 10:09
                                </option>
                                                                <option value="America/Fortaleza" >
                                    Fortaleza - 14:09
                                </option>
                                                                <option value="America/Glace_Bay" >
                                    Glace Bay - 14:09
                                </option>
                                                                <option value="America/Godthab" >
                                    Godthab - 15:09
                                </option>
                                                                <option value="America/Goose_Bay" >
                                    Goose Bay - 14:09
                                </option>
                                                                <option value="America/Grand_Turk" >
                                    Grand Turk - 13:09
                                </option>
                                                                <option value="America/Grenada" >
                                    Grenada - 13:09
                                </option>
                                                                <option value="America/Guadeloupe" >
                                    Guadeloupe - 13:09
                                </option>
                                                                <option value="America/Guatemala" >
                                    Guatemala - 11:09
                                </option>
                                                                <option value="America/Guayaquil" >
                                    Guayaquil - 12:09
                                </option>
                                                                <option value="America/Guyana" >
                                    Guyana - 13:09
                                </option>
                                                                <option value="America/Halifax" >
                                    Halifax - 14:09
                                </option>
                                                                <option value="America/Havana" >
                                    Havana - 13:09
                                </option>
                                                                <option value="America/Hermosillo" >
                                    Hermosillo - 10:09
                                </option>
                                                                <option value="America/Indiana/Indianapolis" >
                                    Indiana/Indianapolis - 13:09
                                </option>
                                                                <option value="America/Indiana/Knox" >
                                    Indiana/Knox - 12:09
                                </option>
                                                                <option value="America/Indiana/Marengo" >
                                    Indiana/Marengo - 13:09
                                </option>
                                                                <option value="America/Indiana/Petersburg" >
                                    Indiana/Petersburg - 13:09
                                </option>
                                                                <option value="America/Indiana/Tell_City" >
                                    Indiana/Tell City - 12:09
                                </option>
                                                                <option value="America/Indiana/Vevay" >
                                    Indiana/Vevay - 13:09
                                </option>
                                                                <option value="America/Indiana/Vincennes" >
                                    Indiana/Vincennes - 13:09
                                </option>
                                                                <option value="America/Indiana/Winamac" >
                                    Indiana/Winamac - 13:09
                                </option>
                                                                <option value="America/Inuvik" >
                                    Inuvik - 11:09
                                </option>
                                                                <option value="America/Iqaluit" >
                                    Iqaluit - 13:09
                                </option>
                                                                <option value="America/Jamaica" >
                                    Jamaica - 12:09
                                </option>
                                                                <option value="America/Juneau" >
                                    Juneau - 09:09
                                </option>
                                                                <option value="America/Kentucky/Louisville" >
                                    Kentucky/Louisville - 13:09
                                </option>
                                                                <option value="America/Kentucky/Monticello" >
                                    Kentucky/Monticello - 13:09
                                </option>
                                                                <option value="America/Kralendijk" >
                                    Kralendijk - 13:09
                                </option>
                                                                <option value="America/La_Paz" >
                                    La Paz - 13:09
                                </option>
                                                                <option value="America/Lima" >
                                    Lima - 12:09
                                </option>
                                                                <option value="America/Los_Angeles" >
                                    Los Angeles - 10:09
                                </option>
                                                                <option value="America/Lower_Princes" >
                                    Lower Princes - 13:09
                                </option>
                                                                <option value="America/Maceio" >
                                    Maceio - 14:09
                                </option>
                                                                <option value="America/Managua" >
                                    Managua - 11:09
                                </option>
                                                                <option value="America/Manaus" >
                                    Manaus - 13:09
                                </option>
                                                                <option value="America/Marigot" >
                                    Marigot - 13:09
                                </option>
                                                                <option value="America/Martinique" >
                                    Martinique - 13:09
                                </option>
                                                                <option value="America/Matamoros" >
                                    Matamoros - 12:09
                                </option>
                                                                <option value="America/Mazatlan" >
                                    Mazatlan - 11:09
                                </option>
                                                                <option value="America/Menominee" >
                                    Menominee - 12:09
                                </option>
                                                                <option value="America/Merida" >
                                    Merida - 12:09
                                </option>
                                                                <option value="America/Metlakatla" >
                                    Metlakatla - 09:09
                                </option>
                                                                <option value="America/Mexico_City" >
                                    Mexico City - 12:09
                                </option>
                                                                <option value="America/Miquelon" >
                                    Miquelon - 15:09
                                </option>
                                                                <option value="America/Moncton" >
                                    Moncton - 14:09
                                </option>
                                                                <option value="America/Monterrey" >
                                    Monterrey - 12:09
                                </option>
                                                                <option value="America/Montevideo" >
                                    Montevideo - 14:09
                                </option>
                                                                <option value="America/Montserrat" >
                                    Montserrat - 13:09
                                </option>
                                                                <option value="America/Nassau" >
                                    Nassau - 13:09
                                </option>
                                                                <option value="America/New_York" >
                                    New York - 13:09
                                </option>
                                                                <option value="America/Nipigon" >
                                    Nipigon - 13:09
                                </option>
                                                                <option value="America/Nome" >
                                    Nome - 09:09
                                </option>
                                                                <option value="America/Noronha" >
                                    Noronha - 15:09
                                </option>
                                                                <option value="America/North_Dakota/Beulah" >
                                    North Dakota/Beulah - 12:09
                                </option>
                                                                <option value="America/North_Dakota/Center" >
                                    North Dakota/Center - 12:09
                                </option>
                                                                <option value="America/North_Dakota/New_Salem" >
                                    North Dakota/New Salem - 12:09
                                </option>
                                                                <option value="America/Ojinaga" >
                                    Ojinaga - 11:09
                                </option>
                                                                <option value="America/Panama" >
                                    Panama - 12:09
                                </option>
                                                                <option value="America/Pangnirtung" >
                                    Pangnirtung - 13:09
                                </option>
                                                                <option value="America/Paramaribo" >
                                    Paramaribo - 14:09
                                </option>
                                                                <option value="America/Phoenix" >
                                    Phoenix - 10:09
                                </option>
                                                                <option value="America/Port-au-Prince" >
                                    Port-au-Prince - 13:09
                                </option>
                                                                <option value="America/Port_of_Spain" >
                                    Port of Spain - 13:09
                                </option>
                                                                <option value="America/Porto_Velho" >
                                    Porto Velho - 13:09
                                </option>
                                                                <option value="America/Puerto_Rico" >
                                    Puerto Rico - 13:09
                                </option>
                                                                <option value="America/Punta_Arenas" >
                                    Punta Arenas - 14:09
                                </option>
                                                                <option value="America/Rainy_River" >
                                    Rainy River - 12:09
                                </option>
                                                                <option value="America/Rankin_Inlet" >
                                    Rankin Inlet - 12:09
                                </option>
                                                                <option value="America/Recife" >
                                    Recife - 14:09
                                </option>
                                                                <option value="America/Regina" >
                                    Regina - 11:09
                                </option>
                                                                <option value="America/Resolute" >
                                    Resolute - 12:09
                                </option>
                                                                <option value="America/Rio_Branco" >
                                    Rio Branco - 12:09
                                </option>
                                                                <option value="America/Santarem" >
                                    Santarem - 14:09
                                </option>
                                                                <option value="America/Santiago" >
                                    Santiago - 13:09
                                </option>
                                                                <option value="America/Santo_Domingo" >
                                    Santo Domingo - 13:09
                                </option>
                                                                <option value="America/Sao_Paulo" >
                                    Sao Paulo - 14:09
                                </option>
                                                                <option value="America/Scoresbysund" >
                                    Scoresbysund - 17:09
                                </option>
                                                                <option value="America/Sitka" >
                                    Sitka - 09:09
                                </option>
                                                                <option value="America/St_Barthelemy" >
                                    St Barthelemy - 13:09
                                </option>
                                                                <option value="America/St_Johns" >
                                    St Johns - 14:39
                                </option>
                                                                <option value="America/St_Kitts" >
                                    St Kitts - 13:09
                                </option>
                                                                <option value="America/St_Lucia" >
                                    St Lucia - 13:09
                                </option>
                                                                <option value="America/St_Thomas" >
                                    St Thomas - 13:09
                                </option>
                                                                <option value="America/St_Vincent" >
                                    St Vincent - 13:09
                                </option>
                                                                <option value="America/Swift_Current" >
                                    Swift Current - 11:09
                                </option>
                                                                <option value="America/Tegucigalpa" >
                                    Tegucigalpa - 11:09
                                </option>
                                                                <option value="America/Thule" >
                                    Thule - 14:09
                                </option>
                                                                <option value="America/Thunder_Bay" >
                                    Thunder Bay - 13:09
                                </option>
                                                                <option value="America/Tijuana" >
                                    Tijuana - 10:09
                                </option>
                                                                <option value="America/Toronto" >
                                    Toronto - 13:09
                                </option>
                                                                <option value="America/Tortola" >
                                    Tortola - 13:09
                                </option>
                                                                <option value="America/Vancouver" >
                                    Vancouver - 10:09
                                </option>
                                                                <option value="America/Whitehorse" >
                                    Whitehorse - 10:09
                                </option>
                                                                <option value="America/Winnipeg" >
                                    Winnipeg - 12:09
                                </option>
                                                                <option value="America/Yakutat" >
                                    Yakutat - 09:09
                                </option>
                                                                <option value="America/Yellowknife" >
                                    Yellowknife - 11:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Antarctica">
                                                                <option value="Antarctica/Casey" >
                                    Casey - 01:09
                                </option>
                                                                <option value="Antarctica/Davis" >
                                    Davis - 00:09
                                </option>
                                                                <option value="Antarctica/DumontDUrville" >
                                    DumontDUrville - 03:09
                                </option>
                                                                <option value="Antarctica/Macquarie" >
                                    Macquarie - 04:09
                                </option>
                                                                <option value="Antarctica/Mawson" >
                                    Mawson - 22:09
                                </option>
                                                                <option value="Antarctica/McMurdo" >
                                    McMurdo - 05:09
                                </option>
                                                                <option value="Antarctica/Palmer" >
                                    Palmer - 14:09
                                </option>
                                                                <option value="Antarctica/Rothera" >
                                    Rothera - 14:09
                                </option>
                                                                <option value="Antarctica/Syowa" >
                                    Syowa - 20:09
                                </option>
                                                                <option value="Antarctica/Troll" >
                                    Troll - 19:09
                                </option>
                                                                <option value="Antarctica/Vostok" >
                                    Vostok - 23:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Arctic">
                                                                <option value="Arctic/Longyearbyen" >
                                    Longyearbyen - 19:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Asia">
                                                                <option value="Asia/Aden" >
                                    Aden - 20:09
                                </option>
                                                                <option value="Asia/Almaty" >
                                    Almaty - 23:09
                                </option>
                                                                <option value="Asia/Amman" >
                                    Amman - 20:09
                                </option>
                                                                <option value="Asia/Anadyr" >
                                    Anadyr - 05:09
                                </option>
                                                                <option value="Asia/Aqtau" >
                                    Aqtau - 22:09
                                </option>
                                                                <option value="Asia/Aqtobe" >
                                    Aqtobe - 22:09
                                </option>
                                                                <option value="Asia/Ashgabat" >
                                    Ashgabat - 22:09
                                </option>
                                                                <option value="Asia/Atyrau" >
                                    Atyrau - 22:09
                                </option>
                                                                <option value="Asia/Baghdad" >
                                    Baghdad - 20:09
                                </option>
                                                                <option value="Asia/Bahrain" >
                                    Bahrain - 20:09
                                </option>
                                                                <option value="Asia/Baku" >
                                    Baku - 21:09
                                </option>
                                                                <option value="Asia/Bangkok" >
                                    Bangkok - 00:09
                                </option>
                                                                <option value="Asia/Barnaul" >
                                    Barnaul - 00:09
                                </option>
                                                                <option value="Asia/Beirut" >
                                    Beirut - 20:09
                                </option>
                                                                <option value="Asia/Bishkek" >
                                    Bishkek - 23:09
                                </option>
                                                                <option value="Asia/Brunei" >
                                    Brunei - 01:09
                                </option>
                                                                <option value="Asia/Chita" >
                                    Chita - 02:09
                                </option>
                                                                <option value="Asia/Choibalsan" >
                                    Choibalsan - 01:09
                                </option>
                                                                <option value="Asia/Colombo" >
                                    Colombo - 22:39
                                </option>
                                                                <option value="Asia/Damascus" >
                                    Damascus - 20:09
                                </option>
                                                                <option value="Asia/Dhaka" >
                                    Dhaka - 23:09
                                </option>
                                                                <option value="Asia/Dili" >
                                    Dili - 02:09
                                </option>
                                                                <option value="Asia/Dubai" >
                                    Dubai - 21:09
                                </option>
                                                                <option value="Asia/Dushanbe" >
                                    Dushanbe - 22:09
                                </option>
                                                                <option value="Asia/Famagusta" >
                                    Famagusta - 20:09
                                </option>
                                                                <option value="Asia/Gaza" >
                                    Gaza - 20:09
                                </option>
                                                                <option value="Asia/Hebron" >
                                    Hebron - 20:09
                                </option>
                                                                <option value="Asia/Ho_Chi_Minh" >
                                    Ho Chi Minh - 00:09
                                </option>
                                                                <option value="Asia/Hong_Kong" >
                                    Hong Kong - 01:09
                                </option>
                                                                <option value="Asia/Hovd" >
                                    Hovd - 00:09
                                </option>
                                                                <option value="Asia/Irkutsk" >
                                    Irkutsk - 01:09
                                </option>
                                                                <option value="Asia/Jakarta" >
                                    Jakarta - 00:09
                                </option>
                                                                <option value="Asia/Jayapura" >
                                    Jayapura - 02:09
                                </option>
                                                                <option value="Asia/Jerusalem" >
                                    Jerusalem - 20:09
                                </option>
                                                                <option value="Asia/Kabul" >
                                    Kabul - 21:39
                                </option>
                                                                <option value="Asia/Kamchatka" >
                                    Kamchatka - 05:09
                                </option>
                                                                <option value="Asia/Karachi" >
                                    Karachi - 22:09
                                </option>
                                                                <option value="Asia/Kathmandu" >
                                    Kathmandu - 22:54
                                </option>
                                                                <option value="Asia/Khandyga" >
                                    Khandyga - 02:09
                                </option>
                                                                <option value="Asia/Kolkata" >
                                    Kolkata - 22:39
                                </option>
                                                                <option value="Asia/Krasnoyarsk" >
                                    Krasnoyarsk - 00:09
                                </option>
                                                                <option value="Asia/Kuala_Lumpur" >
                                    Kuala Lumpur - 01:09
                                </option>
                                                                <option value="Asia/Kuching" >
                                    Kuching - 01:09
                                </option>
                                                                <option value="Asia/Kuwait" >
                                    Kuwait - 20:09
                                </option>
                                                                <option value="Asia/Macau" >
                                    Macau - 01:09
                                </option>
                                                                <option value="Asia/Magadan" >
                                    Magadan - 04:09
                                </option>
                                                                <option value="Asia/Makassar" >
                                    Makassar - 01:09
                                </option>
                                                                <option value="Asia/Manila" >
                                    Manila - 01:09
                                </option>
                                                                <option value="Asia/Muscat" >
                                    Muscat - 21:09
                                </option>
                                                                <option value="Asia/Nicosia" >
                                    Nicosia - 20:09
                                </option>
                                                                <option value="Asia/Novokuznetsk" >
                                    Novokuznetsk - 00:09
                                </option>
                                                                <option value="Asia/Novosibirsk" >
                                    Novosibirsk - 00:09
                                </option>
                                                                <option value="Asia/Omsk" >
                                    Omsk - 23:09
                                </option>
                                                                <option value="Asia/Oral" >
                                    Oral - 22:09
                                </option>
                                                                <option value="Asia/Phnom_Penh" >
                                    Phnom Penh - 00:09
                                </option>
                                                                <option value="Asia/Pontianak" >
                                    Pontianak - 00:09
                                </option>
                                                                <option value="Asia/Pyongyang" >
                                    Pyongyang - 02:09
                                </option>
                                                                <option value="Asia/Qatar" >
                                    Qatar - 20:09
                                </option>
                                                                <option value="Asia/Qostanay" >
                                    Qostanay - 23:09
                                </option>
                                                                <option value="Asia/Qyzylorda" >
                                    Qyzylorda - 22:09
                                </option>
                                                                <option value="Asia/Riyadh" >
                                    Riyadh - 20:09
                                </option>
                                                                <option value="Asia/Sakhalin" >
                                    Sakhalin - 04:09
                                </option>
                                                                <option value="Asia/Samarkand" >
                                    Samarkand - 22:09
                                </option>
                                                                <option value="Asia/Seoul" >
                                    Seoul - 02:09
                                </option>
                                                                <option value="Asia/Shanghai" >
                                    Shanghai - 01:09
                                </option>
                                                                <option value="Asia/Singapore" >
                                    Singapore - 01:09
                                </option>
                                                                <option value="Asia/Srednekolymsk" >
                                    Srednekolymsk - 04:09
                                </option>
                                                                <option value="Asia/Taipei" >
                                    Taipei - 01:09
                                </option>
                                                                <option value="Asia/Tashkent" >
                                    Tashkent - 22:09
                                </option>
                                                                <option value="Asia/Tbilisi" >
                                    Tbilisi - 21:09
                                </option>
                                                                <option value="Asia/Tehran" >
                                    Tehran - 21:39
                                </option>
                                                                <option value="Asia/Thimphu" >
                                    Thimphu - 23:09
                                </option>
                                                                <option value="Asia/Tokyo" >
                                    Tokyo - 02:09
                                </option>
                                                                <option value="Asia/Tomsk" >
                                    Tomsk - 00:09
                                </option>
                                                                <option value="Asia/Ulaanbaatar" >
                                    Ulaanbaatar - 01:09
                                </option>
                                                                <option value="Asia/Urumqi" >
                                    Urumqi - 23:09
                                </option>
                                                                <option value="Asia/Ust-Nera" >
                                    Ust-Nera - 03:09
                                </option>
                                                                <option value="Asia/Vientiane" >
                                    Vientiane - 00:09
                                </option>
                                                                <option value="Asia/Vladivostok" >
                                    Vladivostok - 03:09
                                </option>
                                                                <option value="Asia/Yakutsk" >
                                    Yakutsk - 02:09
                                </option>
                                                                <option value="Asia/Yangon" >
                                    Yangon - 23:39
                                </option>
                                                                <option value="Asia/Yekaterinburg" >
                                    Yekaterinburg - 22:09
                                </option>
                                                                <option value="Asia/Yerevan" >
                                    Yerevan - 21:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Atlantic">
                                                                <option value="Atlantic/Azores" >
                                    Azores - 17:09
                                </option>
                                                                <option value="Atlantic/Bermuda" >
                                    Bermuda - 14:09
                                </option>
                                                                <option value="Atlantic/Canary" >
                                    Canary - 18:09
                                </option>
                                                                <option value="Atlantic/Cape_Verde" >
                                    Cape Verde - 16:09
                                </option>
                                                                <option value="Atlantic/Faroe" >
                                    Faroe - 18:09
                                </option>
                                                                <option value="Atlantic/Madeira" >
                                    Madeira - 18:09
                                </option>
                                                                <option value="Atlantic/Reykjavik" >
                                    Reykjavik - 17:09
                                </option>
                                                                <option value="Atlantic/South_Georgia" >
                                    South Georgia - 15:09
                                </option>
                                                                <option value="Atlantic/St_Helena" >
                                    St Helena - 17:09
                                </option>
                                                                <option value="Atlantic/Stanley" >
                                    Stanley - 14:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Australia">
                                                                <option value="Australia/Adelaide" >
                                    Adelaide - 02:39
                                </option>
                                                                <option value="Australia/Brisbane" >
                                    Brisbane - 03:09
                                </option>
                                                                <option value="Australia/Broken_Hill" >
                                    Broken Hill - 02:39
                                </option>
                                                                <option value="Australia/Currie" >
                                    Currie - 03:09
                                </option>
                                                                <option value="Australia/Darwin" >
                                    Darwin - 02:39
                                </option>
                                                                <option value="Australia/Eucla" >
                                    Eucla - 01:54
                                </option>
                                                                <option value="Australia/Hobart" >
                                    Hobart - 03:09
                                </option>
                                                                <option value="Australia/Lindeman" >
                                    Lindeman - 03:09
                                </option>
                                                                <option value="Australia/Lord_Howe" >
                                    Lord Howe - 03:39
                                </option>
                                                                <option value="Australia/Melbourne" >
                                    Melbourne - 03:09
                                </option>
                                                                <option value="Australia/Perth" >
                                    Perth - 01:09
                                </option>
                                                                <option value="Australia/Sydney" >
                                    Sydney - 03:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Europe">
                                                                <option value="Europe/Amsterdam" >
                                    Amsterdam - 19:09
                                </option>
                                                                <option value="Europe/Andorra" >
                                    Andorra - 19:09
                                </option>
                                                                <option value="Europe/Astrakhan" >
                                    Astrakhan - 21:09
                                </option>
                                                                <option value="Europe/Athens" >
                                    Athens - 20:09
                                </option>
                                                                <option value="Europe/Belgrade" >
                                    Belgrade - 19:09
                                </option>
                                                                <option value="Europe/Berlin" >
                                    Berlin - 19:09
                                </option>
                                                                <option value="Europe/Bratislava" >
                                    Bratislava - 19:09
                                </option>
                                                                <option value="Europe/Brussels" >
                                    Brussels - 19:09
                                </option>
                                                                <option value="Europe/Bucharest" >
                                    Bucharest - 20:09
                                </option>
                                                                <option value="Europe/Budapest" >
                                    Budapest - 19:09
                                </option>
                                                                <option value="Europe/Busingen" >
                                    Busingen - 19:09
                                </option>
                                                                <option value="Europe/Chisinau" >
                                    Chisinau - 20:09
                                </option>
                                                                <option value="Europe/Copenhagen" >
                                    Copenhagen - 19:09
                                </option>
                                                                <option value="Europe/Dublin" >
                                    Dublin - 18:09
                                </option>
                                                                <option value="Europe/Gibraltar" >
                                    Gibraltar - 19:09
                                </option>
                                                                <option value="Europe/Guernsey" >
                                    Guernsey - 18:09
                                </option>
                                                                <option value="Europe/Helsinki" >
                                    Helsinki - 20:09
                                </option>
                                                                <option value="Europe/Isle_of_Man" >
                                    Isle of Man - 18:09
                                </option>
                                                                <option value="Europe/Istanbul" >
                                    Istanbul - 20:09
                                </option>
                                                                <option value="Europe/Jersey" >
                                    Jersey - 18:09
                                </option>
                                                                <option value="Europe/Kaliningrad" >
                                    Kaliningrad - 19:09
                                </option>
                                                                <option value="Europe/Kiev" >
                                    Kiev - 20:09
                                </option>
                                                                <option value="Europe/Kirov" >
                                    Kirov - 20:09
                                </option>
                                                                <option value="Europe/Lisbon" >
                                    Lisbon - 18:09
                                </option>
                                                                <option value="Europe/Ljubljana" >
                                    Ljubljana - 19:09
                                </option>
                                                                <option value="Europe/London" >
                                    London - 18:09
                                </option>
                                                                <option value="Europe/Luxembourg" >
                                    Luxembourg - 19:09
                                </option>
                                                                <option value="Europe/Madrid" >
                                    Madrid - 19:09
                                </option>
                                                                <option value="Europe/Malta" >
                                    Malta - 19:09
                                </option>
                                                                <option value="Europe/Mariehamn" >
                                    Mariehamn - 20:09
                                </option>
                                                                <option value="Europe/Minsk" >
                                    Minsk - 20:09
                                </option>
                                                                <option value="Europe/Monaco" >
                                    Monaco - 19:09
                                </option>
                                                                <option value="Europe/Moscow" >
                                    Moscow - 20:09
                                </option>
                                                                <option value="Europe/Oslo" >
                                    Oslo - 19:09
                                </option>
                                                                <option value="Europe/Paris" >
                                    Paris - 19:09
                                </option>
                                                                <option value="Europe/Podgorica" >
                                    Podgorica - 19:09
                                </option>
                                                                <option value="Europe/Prague" >
                                    Prague - 19:09
                                </option>
                                                                <option value="Europe/Riga" >
                                    Riga - 20:09
                                </option>
                                                                <option value="Europe/Rome" >
                                    Rome - 19:09
                                </option>
                                                                <option value="Europe/Samara" >
                                    Samara - 21:09
                                </option>
                                                                <option value="Europe/San_Marino" >
                                    San Marino - 19:09
                                </option>
                                                                <option value="Europe/Sarajevo" >
                                    Sarajevo - 19:09
                                </option>
                                                                <option value="Europe/Saratov" >
                                    Saratov - 21:09
                                </option>
                                                                <option value="Europe/Simferopol" >
                                    Simferopol - 20:09
                                </option>
                                                                <option value="Europe/Skopje" >
                                    Skopje - 19:09
                                </option>
                                                                <option value="Europe/Sofia" >
                                    Sofia - 20:09
                                </option>
                                                                <option value="Europe/Stockholm" >
                                    Stockholm - 19:09
                                </option>
                                                                <option value="Europe/Tallinn" >
                                    Tallinn - 20:09
                                </option>
                                                                <option value="Europe/Tirane" >
                                    Tirane - 19:09
                                </option>
                                                                <option value="Europe/Ulyanovsk" >
                                    Ulyanovsk - 21:09
                                </option>
                                                                <option value="Europe/Uzhgorod" >
                                    Uzhgorod - 20:09
                                </option>
                                                                <option value="Europe/Vaduz" >
                                    Vaduz - 19:09
                                </option>
                                                                <option value="Europe/Vatican" >
                                    Vatican - 19:09
                                </option>
                                                                <option value="Europe/Vienna" >
                                    Vienna - 19:09
                                </option>
                                                                <option value="Europe/Vilnius" >
                                    Vilnius - 20:09
                                </option>
                                                                <option value="Europe/Volgograd" >
                                    Volgograd - 21:09
                                </option>
                                                                <option value="Europe/Warsaw" >
                                    Warsaw - 19:09
                                </option>
                                                                <option value="Europe/Zagreb" >
                                    Zagreb - 19:09
                                </option>
                                                                <option value="Europe/Zaporozhye" >
                                    Zaporozhye - 20:09
                                </option>
                                                                <option value="Europe/Zurich" >
                                    Zurich - 19:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Indian">
                                                                <option value="Indian/Antananarivo" >
                                    Antananarivo - 20:09
                                </option>
                                                                <option value="Indian/Chagos" >
                                    Chagos - 23:09
                                </option>
                                                                <option value="Indian/Christmas" >
                                    Christmas - 00:09
                                </option>
                                                                <option value="Indian/Cocos" >
                                    Cocos - 23:39
                                </option>
                                                                <option value="Indian/Comoro" >
                                    Comoro - 20:09
                                </option>
                                                                <option value="Indian/Kerguelen" >
                                    Kerguelen - 22:09
                                </option>
                                                                <option value="Indian/Mahe" >
                                    Mahe - 21:09
                                </option>
                                                                <option value="Indian/Maldives" >
                                    Maldives - 22:09
                                </option>
                                                                <option value="Indian/Mauritius" >
                                    Mauritius - 21:09
                                </option>
                                                                <option value="Indian/Mayotte" >
                                    Mayotte - 20:09
                                </option>
                                                                <option value="Indian/Reunion" >
                                    Reunion - 21:09
                                </option>
                                                                </optgroup>
                                                                <optgroup label="Pacific">
                                                                <option value="Pacific/Apia" >
                                    Apia - 06:09
                                </option>
                                                                <option value="Pacific/Auckland" >
                                    Auckland - 05:09
                                </option>
                                                                <option value="Pacific/Bougainville" >
                                    Bougainville - 04:09
                                </option>
                                                                <option value="Pacific/Chatham" >
                                    Chatham - 05:54
                                </option>
                                                                <option value="Pacific/Chuuk" >
                                    Chuuk - 03:09
                                </option>
                                                                <option value="Pacific/Easter" >
                                    Easter - 11:09
                                </option>
                                                                <option value="Pacific/Efate" >
                                    Efate - 04:09
                                </option>
                                                                <option value="Pacific/Enderbury" >
                                    Enderbury - 06:09
                                </option>
                                                                <option value="Pacific/Fakaofo" >
                                    Fakaofo - 06:09
                                </option>
                                                                <option value="Pacific/Fiji" >
                                    Fiji - 05:09
                                </option>
                                                                <option value="Pacific/Funafuti" >
                                    Funafuti - 05:09
                                </option>
                                                                <option value="Pacific/Galapagos" >
                                    Galapagos - 11:09
                                </option>
                                                                <option value="Pacific/Gambier" >
                                    Gambier - 08:09
                                </option>
                                                                <option value="Pacific/Guadalcanal" >
                                    Guadalcanal - 04:09
                                </option>
                                                                <option value="Pacific/Guam" >
                                    Guam - 03:09
                                </option>
                                                                <option value="Pacific/Honolulu" >
                                    Honolulu - 07:09
                                </option>
                                                                <option value="Pacific/Kiritimati" >
                                    Kiritimati - 07:09
                                </option>
                                                                <option value="Pacific/Kosrae" >
                                    Kosrae - 04:09
                                </option>
                                                                <option value="Pacific/Kwajalein" >
                                    Kwajalein - 05:09
                                </option>
                                                                <option value="Pacific/Majuro" >
                                    Majuro - 05:09
                                </option>
                                                                <option value="Pacific/Marquesas" >
                                    Marquesas - 07:39
                                </option>
                                                                <option value="Pacific/Midway" >
                                    Midway - 06:09
                                </option>
                                                                <option value="Pacific/Nauru" >
                                    Nauru - 05:09
                                </option>
                                                                <option value="Pacific/Niue" >
                                    Niue - 06:09
                                </option>
                                                                <option value="Pacific/Norfolk" >
                                    Norfolk - 04:09
                                </option>
                                                                <option value="Pacific/Noumea" >
                                    Noumea - 04:09
                                </option>
                                                                <option value="Pacific/Pago_Pago" >
                                    Pago Pago - 06:09
                                </option>
                                                                <option value="Pacific/Palau" >
                                    Palau - 02:09
                                </option>
                                                                <option value="Pacific/Pitcairn" >
                                    Pitcairn - 09:09
                                </option>
                                                                <option value="Pacific/Pohnpei" >
                                    Pohnpei - 04:09
                                </option>
                                                                <option value="Pacific/Port_Moresby" >
                                    Port Moresby - 03:09
                                </option>
                                                                <option value="Pacific/Rarotonga" >
                                    Rarotonga - 07:09
                                </option>
                                                                <option value="Pacific/Saipan" >
                                    Saipan - 03:09
                                </option>
                                                                <option value="Pacific/Tahiti" >
                                    Tahiti - 07:09
                                </option>
                                                                <option value="Pacific/Tarawa" >
                                    Tarawa - 05:09
                                </option>
                                                                <option value="Pacific/Tongatapu" >
                                    Tongatapu - 06:09
                                </option>
                                                                <option value="Pacific/Wake" >
                                    Wake - 05:09
                                </option>
                                                                <option value="Pacific/Wallis" >
                                    Wallis - 05:09
                                </option>
                                                                </optgroup>
                                                            </select>
                                                    </div>
                        <div class="form-group">
                            <label>Select your language</label>
                            <select name="settings[app_locale]" class="form-control" required>
                                <option value="">Select Language</option>
                                                                <option value="af" >
                                    Afrikaans
                                </option>
                                                                <option value="af-ZA" >
                                    af-ZA
                                </option>
                                                                <option value="ar" >
                                    Arabic
                                </option>
                                                                <option value="ar-SA" >
                                    ar-SA
                                </option>
                                                                <option value="ca" >
                                    Catalan
                                </option>
                                                                <option value="ca-ES" >
                                    ca-ES
                                </option>
                                                                <option value="cs" >
                                    Czech
                                </option>
                                                                <option value="cs-CZ" >
                                    cs-CZ
                                </option>
                                                                <option value="da" >
                                    Danish
                                </option>
                                                                <option value="da-DK" >
                                    da-DK
                                </option>
                                                                <option value="de" >
                                    Deutsch
                                </option>
                                                                <option value="de-DE" >
                                    de-DE
                                </option>
                                                                <option value="el" >
                                    Greek
                                </option>
                                                                <option value="el-GR" >
                                    el-GR
                                </option>
                                                                <option value="en"  selected >
                                    English
                                </option>
                                                                <option value="en-UD" >
                                    CrowdIn - InContext Localization
                                </option>
                                                                <option value="en-US" >
                                    en-US
                                </option>
                                                                <option value="es" >
                                    Español
                                </option>
                                                                <option value="es-ES" >
                                    es-ES
                                </option>
                                                                <option value="et-EE" >
                                    et-EE
                                </option>
                                                                <option value="fa" >
                                    Persian
                                </option>
                                                                <option value="fa-IR" >
                                    fa-IR
                                </option>
                                                                <option value="fi" >
                                    Finnish
                                </option>
                                                                <option value="fi-FI" >
                                    fi-FI
                                </option>
                                                                <option value="fr" >
                                    Français
                                </option>
                                                                <option value="fr-FR" >
                                    fr-FR
                                </option>
                                                                <option value="he" >
                                    Hebrew
                                </option>
                                                                <option value="he-IL" >
                                    he-IL
                                </option>
                                                                <option value="hu" >
                                    Hungarian
                                </option>
                                                                <option value="hu-HU" >
                                    hu-HU
                                </option>
                                                                <option value="id" >
                                    Indonesian
                                </option>
                                                                <option value="id-ID" >
                                    id-ID
                                </option>
                                                                <option value="it" >
                                    Italiano
                                </option>
                                                                <option value="it-IT" >
                                    it-IT
                                </option>
                                                                <option value="ja" >
                                    Japanese
                                </option>
                                                                <option value="ja-JP" >
                                    ja-JP
                                </option>
                                                                <option value="ko" >
                                    한글
                                </option>
                                                                <option value="ko-KR" >
                                    ko-KR
                                </option>
                                                                <option value="mn-MN" >
                                    mn-MN
                                </option>
                                                                <option value="nl" >
                                    Nederlands
                                </option>
                                                                <option value="nl-NL" >
                                    nl-NL
                                </option>
                                                                <option value="no" >
                                    Norwegian
                                </option>
                                                                <option value="no-NO" >
                                    no-NO
                                </option>
                                                                <option value="pl" >
                                    Polski
                                </option>
                                                                <option value="pl-PL" >
                                    pl-PL
                                </option>
                                                                <option value="pt-BR" >
                                    Portuguese, Brazilian
                                </option>
                                                                <option value="pt-PT" >
                                    Portuguese, Portugal
                                </option>
                                                                <option value="ro" >
                                    Romanian
                                </option>
                                                                <option value="ro-RO" >
                                    ro-RO
                                </option>
                                                                <option value="ru" >
                                    Русский
                                </option>
                                                                <option value="ru-RU" >
                                    ru-RU
                                </option>
                                                                <option value="sl-SI" >
                                    sl-SI
                                </option>
                                                                <option value="sq" >
                                    Albanian
                                </option>
                                                                <option value="sq-AL" >
                                    sq-AL
                                </option>
                                                                <option value="sr" >
                                    Sebrian (Cyrillic)
                                </option>
                                                                <option value="sv-SE" >
                                    Swedish
                                </option>
                                                                <option value="th-TH" >
                                    th-TH
                                </option>
                                                                <option value="tr" >
                                    Turkish
                                </option>
                                                                <option value="tr-TR" >
                                    tr-TR
                                </option>
                                                                <option value="uk" >
                                    Ukranian
                                </option>
                                                                <option value="uk-UA" >
                                    uk-UA
                                </option>
                                                                <option value="vi" >
                                    Vietnamese
                                </option>
                                                                <option value="vi-VN" >
                                    vi-VN
                                </option>
                                                                <option value="zh-CN" >
                                    简体中文
                                </option>
                                                                <option value="zh-TW" >
                                    繁體中文
                                </option>
                                                                <option value="zu-ZA" >
                                    zu-ZA
                                </option>
                                                            </select>
                                                    </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="settings[show_support]" value="1" checked>
                                Show support for Cachet?
                            </label>
                        </div>
                        <hr>
                        <div class="form-group text-center">
                            <span class="wizard-next btn btn-info" data-current-block="2" data-next-block="1">
                                Previous
                            </span>
                            <span class="wizard-next btn btn-success" data-current-block="2" data-next-block="3" data-loading-text="<i class='icon ion-load-c'></i>">
                                Next
                            </span>
                        </div>
                    </fieldset>
                </div>
                <div class="step block-3 hidden">
                    <fieldset>
                        <div class="form-group">
                            <label>Username</label>
                            <input type="text" name="user[username]" class="form-control" placeholder="Username" value="" required>
                                                    </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="text" name="user[email]" class="form-control" placeholder="Email" value="" required>
                                                    </div>
                        <div class="form-group">
                            <label>Password</label>
                            <input type="password" name="user[password]" class="form-control" placeholder="Password" value="" required>
                                                    </div>
                    </fieldset>
                    <hr >
                    <div class="form-group text-center">
                        <input type="hidden" name="settings[app_incident_days]" value="7" >
                        <input type="hidden" name="settings[app_refresh_rate]" value="0" >
                        <span class="wizard-next btn btn-info" data-current-block="3" data-next-block="2">
                            Previous
                        </span>
                        <span class="wizard-next btn btn-success" data-current-block="3" data-next-block="4" data-loading-text="<i class='icon ion-load-c'></i>">
                            Complete Setup
                        </span>
                    </div>
                </div>
                <div class="step block-4 hidden">
                    <div class="setup-success">
                        <i class="ion ion-checkmark-circled"></i>
                        <h3>
                            Cachet has been configured successfully!
                        </h3>
                        <a href="http://localhost:3501/dashboard" class="btn btn-default">
                            <span>Go to dashboard</span>
                        </a>
                    </div>
                </div>
            </form>
        </setup>
    </div>
</div>
    </div>
<link rel='stylesheet' type='text/css' property='stylesheet' href='//localhost:3501/_debugbar/assets/stylesheets?v=1600698121'><script type='text/javascript' src='//localhost:3501/_debugbar/assets/javascript?v=1600698121'></script><script type="text/javascript">jQuery.noConflict(true);</script>
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[\]\/\\])/g, idRx = /\bsf-dump-\d+-ref[012]\w+\b/, keyHint = 0 <= navigator.platform.toUpperCase().indexOf('MAC') ? 'Cmd' : 'Ctrl', addEventListener = function (e, n, cb) { e.addEventListener(n, cb, false); }; (doc.documentElement.firstElementChild || doc.documentElement.children[0]).appendChild(refStyle); if (!doc.addEventListener) { addEventListener = function (element, eventName, callback) { element.attachEvent('on' + eventName, function (e) { e.preventDefault = function () {e.returnValue = false;}; e.target = e.srcElement; callback(e); }); }; } function toggle(a, recursive) { var s = a.nextSibling || {}, oldClass = s.className, arrow, newClass; if (/\bsf-dump-compact\b/.test(oldClass)) { arrow = '▼'; newClass = 'sf-dump-expanded'; } else if (/\bsf-dump-expanded\b/.test(oldClass)) { arrow = '▶'; newClass = 'sf-dump-compact'; } else { return false; } if (doc.createEvent && s.dispatchEvent) { var event = doc.createEvent('Event'); event.initEvent('sf-dump-expanded' === newClass ? 'sfbeforedumpexpand' : 'sfbeforedumpcollapse', true, false); s.dispatchEvent(event); } a.lastChild.innerHTML = arrow; s.className = s.className.replace(/\bsf-dump-(compact|expanded)\b/, newClass); if (recursive) { try { a = s.querySelectorAll('.'+oldClass); for (s = 0; s < a.length; ++s) { if (-1 == a[s].className.indexOf(newClass)) { a[s].className = newClass; a[s].previousSibling.lastChild.innerHTML = arrow; } } } catch (e) { } } return true; }; function collapse(a, recursive) { var s = a.nextSibling || {}, oldClass = s.className; if (/\bsf-dump-expanded\b/.test(oldClass)) { toggle(a, recursive); return true; } return false; }; function expand(a, recursive) { var s = a.nextSibling || {}, oldClass = s.className; if (/\bsf-dump-compact\b/.test(oldClass)) { toggle(a, recursive); return true; } return false; }; function collapseAll(root) { var a = root.querySelector('a.sf-dump-toggle'); if (a) { collapse(a, true); expand(a); return true; } return false; } function reveal(node) { var previous, parents = []; while ((node = node.parentNode || {}) && (previous = node.previousSibling) && 'A' === previous.tagName) { parents.push(previous); } if (0 !== parents.length) { parents.forEach(function (parent) { expand(parent); }); return true; } return false; } function highlight(root, activeNode, nodes) { resetHighlightedNodes(root); Array.from(nodes||[]).forEach(function (node) { if (!/\bsf-dump-highlight\b/.test(node.className)) { node.className = node.className + ' sf-dump-highlight'; } }); if (!/\bsf-dump-highlight-active\b/.test(activeNode.className)) { activeNode.className = activeNode.className + ' sf-dump-highlight-active'; } } function resetHighlightedNodes(root) { Array.from(root.querySelectorAll('.sf-dump-str, .sf-dump-key, .sf-dump-public, .sf-dump-protected, .sf-dump-private')).forEach(function (strNode) { strNode.className = strNode.className.replace(/\bsf-dump-highlight\b/, ''); strNode.className = strNode.className.replace(/\bsf-dump-highlight-active\b/, ''); }); } return function (root, x) { root = doc.getElementById(root); var indentRx = new RegExp('^('+(root.getAttribute('data-indent-pad') || ' ').replace(rxEsc, '\\$1')+')+', 'm'), options = {"maxDepth":1,"maxStringLength":160,"fileLinkFormat":false}, elt = root.getElementsByTagName('A'), len = elt.length, i = 0, s, h, t = []; while (i < len) t.push(elt[i++]); for (i in x) { options[i] = x[i]; } function a(e, f) { addEventListener(root, e, function (e, n) { if ('A' == e.target.tagName) { f(e.target, e); } else if ('A' == e.target.parentNode.tagName) { f(e.target.parentNode, e); } else { n = /\bsf-dump-ellipsis\b/.test(e.target.className) ? e.target.parentNode : e.target; if ((n = n.nextElementSibling) && 'A' == n.tagName) { if (!/\bsf-dump-toggle\b/.test(n.className)) { n = n.nextElementSibling || n; } f(n, e, true); } } }); }; function isCtrlKey(e) { return e.ctrlKey || e.metaKey; } function xpathString(str) { var parts = str.match(/[^'"]+|['"]/g).map(function (part) { if ("'" == part) { return '"\'"'; } if ('"' == part) { return "'\"'"; } return "'" + part + "'"; }); return "concat(" + parts.join(",") + ", '')"; } function xpathHasClass(className) { return "contains(concat(' ', normalize-space(@class), ' '), ' " + className +" ')"; } addEventListener(root, 'mouseover', function (e) { if ('' != refStyle.innerHTML) { refStyle.innerHTML = ''; } }); a('mouseover', function (a, e, c) { if (c) { e.target.style.cursor = "pointer"; } else if (a = idRx.exec(a.className)) { try { refStyle.innerHTML = '.phpdebugbar pre.sf-dump .'+a[0]+'{background-color: #B729D9; color: #FFF !important; border-radius: 2px}'; } catch (e) { } } }); a('click', function (a, e, c) { if (/\bsf-dump-toggle\b/.test(a.className)) { e.preventDefault(); if (!toggle(a, isCtrlKey(e))) { var r = doc.getElementById(a.getAttribute('href').substr(1)), s = r.previousSibling, f = r.parentNode, t = a.parentNode; t.replaceChild(r, a); f.replaceChild(a, s); t.insertBefore(s, r); f = f.firstChild.nodeValue.match(indentRx); t = t.firstChild.nodeValue.match(indentRx); if (f && t && f[0] !== t[0]) { r.innerHTML = r.innerHTML.replace(new RegExp('^'+f[0].replace(rxEsc, '\\$1'), 'mg'), t[0]); } if (/\bsf-dump-compact\b/.test(r.className)) { toggle(s, isCtrlKey(e)); } } if (c) { } else if (doc.getSelection) { try { doc.getSelection().removeAllRanges(); } catch (e) { doc.getSelection().empty(); } } else { doc.selection.empty(); } } else if (/\bsf-dump-str-toggle\b/.test(a.className)) { e.preventDefault(); e = a.parentNode.parentNode; e.className = e.className.replace(/\bsf-dump-str-(expand|collapse)\b/, a.parentNode.className); } }); elt = root.getElementsByTagName('SAMP'); len = elt.length; i = 0; while (i < len) t.push(elt[i++]); len = t.length; for (i = 0; i < len; ++i) { elt = t[i]; if ('SAMP' == elt.tagName) { a = elt.previousSibling || {}; if ('A' != a.tagName) { a = doc.createElement('A'); a.className = 'sf-dump-ref'; elt.parentNode.insertBefore(a, elt); } else { a.innerHTML += ' '; } a.title = (a.title ? a.title+'\n[' : '[')+keyHint+'+click] Expand all children'; a.innerHTML += '<span>▼</span>'; a.className += ' sf-dump-toggle'; x = 1; if ('sf-dump' != elt.parentNode.className) { x += elt.parentNode.getAttribute('data-depth')/1; } elt.setAttribute('data-depth', x); var className = elt.className; elt.className = 'sf-dump-expanded'; if (className ? 'sf-dump-expanded' !== className : (x > options.maxDepth)) { toggle(a); } } else if (/\bsf-dump-ref\b/.test(elt.className) && (a = elt.getAttribute('href'))) { a = a.substr(1); elt.className += ' '+a; if (/[\[{]$/.test(elt.previousSibling.nodeValue)) { a = a != elt.nextSibling.id && doc.getElementById(a); try { s = a.nextSibling; elt.appendChild(a); s.parentNode.insertBefore(a, s); if (/^[@#]/.test(elt.innerHTML)) { elt.innerHTML += ' <span>▶</span>'; } else { elt.innerHTML = '<span>▶</span>'; elt.className = 'sf-dump-ref'; } elt.className += ' sf-dump-toggle'; } catch (e) { if ('&' == elt.innerHTML.charAt(0)) { elt.innerHTML = '…'; elt.className = 'sf-dump-ref'; } } } } } if (doc.evaluate && Array.from && root.children.length > 1) { root.setAttribute('tabindex', 0); SearchState = function () { this.nodes = []; this.idx = 0; }; SearchState.prototype = { next: function () { if (this.isEmpty()) { return this.current(); } this.idx = this.idx < (this.nodes.length - 1) ? this.idx + 1 : 0; return this.current(); }, previous: function () { if (this.isEmpty()) { return this.current(); } this.idx = this.idx > 0 ? this.idx - 1 : (this.nodes.length - 1); return this.current(); }, isEmpty: function () { return 0 === this.count(); }, current: function () { if (this.isEmpty()) { return null; } return this.nodes[this.idx]; }, reset: function () { this.nodes = []; this.idx = 0; }, count: function () { return this.nodes.length; }, }; function showCurrent(state) { var currentNode = state.current(), currentRect, searchRect; if (currentNode) { reveal(currentNode); highlight(root, currentNode, state.nodes); if ('scrollIntoView' in currentNode) { currentNode.scrollIntoView(true); currentRect = currentNode.getBoundingClientRect(); searchRect = search.getBoundingClientRect(); if (currentRect.top < (searchRect.top + searchRect.height)) { window.scrollBy(0, -(searchRect.top + searchRect.height + 5)); } } } counter.textContent = (state.isEmpty() ? 0 : state.idx + 1) + ' of ' + state.count(); } var search = doc.createElement('div'); search.className = 'sf-dump-search-wrapper sf-dump-search-hidden'; search.innerHTML = ' <input type="text" class="sf-dump-search-input"> <span class="sf-dump-search-count">0 of 0<\/span> <button type="button" class="sf-dump-search-input-previous" tabindex="-1"> <svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1683 1331l-166 165q-19 19-45 19t-45-19L896 965l-531 531q-19 19-45 19t-45-19l-166-165q-19-19-19-45.5t19-45.5l742-741q19-19 45-19t45 19l742 741q19 19 19 45.5t-19 45.5z"\/><\/svg> <\/button> <button type="button" class="sf-dump-search-input-next" tabindex="-1"> <svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1683 808l-742 741q-19 19-45 19t-45-19L109 808q-19-19-19-45.5t19-45.5l166-165q19-19 45-19t45 19l531 531 531-531q19-19 45-19t45 19l166 165q19 19 19 45.5t-19 45.5z"\/><\/svg> <\/button> '; root.insertBefore(search, root.firstChild); var state = new SearchState(); var searchInput = search.querySelector('.sf-dump-search-input'); var counter = search.querySelector('.sf-dump-search-count'); var searchInputTimer = 0; var previousSearchQuery = ''; addEventListener(searchInput, 'keyup', function (e) { var searchQuery = e.target.value; /* Don't perform anything if the pressed key didn't change the query */ if (searchQuery === previousSearchQuery) { return; } previousSearchQuery = searchQuery; clearTimeout(searchInputTimer); searchInputTimer = setTimeout(function () { state.reset(); collapseAll(root); resetHighlightedNodes(root); if ('' === searchQuery) { counter.textContent = '0 of 0'; return; } var classMatches = [ "sf-dump-str", "sf-dump-key", "sf-dump-public", "sf-dump-protected", "sf-dump-private", ].map(xpathHasClass).join(' or '); var xpathResult = doc.evaluate('.//span[' + classMatches + '][contains(translate(child::text(), ' + xpathString(searchQuery.toUpperCase()) + ', ' + xpathString(searchQuery.toLowerCase()) + '), ' + xpathString(searchQuery.toLowerCase()) + ')]', root, null, XPathResult.ORDERED_NODE_ITERATOR_TYPE, null); while (node = xpathResult.iterateNext()) state.nodes.push(node); showCurrent(state); }, 400); }); Array.from(search.querySelectorAll('.sf-dump-search-input-next, .sf-dump-search-input-previous')).forEach(function (btn) { addEventListener(btn, 'click', function (e) { e.preventDefault(); -1 !== e.target.className.indexOf('next') ? state.next() : state.previous(); searchInput.focus(); collapseAll(root); showCurrent(state); }) }); addEventListener(root, 'keydown', function (e) { var isSearchActive = !/\bsf-dump-search-hidden\b/.test(search.className); if ((114 === e.keyCode && !isSearchActive) || (isCtrlKey(e) && 70 === e.keyCode)) { /* F3 or CMD/CTRL + F */ if (70 === e.keyCode && document.activeElement === searchInput) { /* * If CMD/CTRL + F is hit while having focus on search input, * the user probably meant to trigger browser search instead. * Let the browser execute its behavior: */ return; } e.preventDefault(); search.className = search.className.replace(/\bsf-dump-search-hidden\b/, ''); searchInput.focus(); } else if (isSearchActive) { if (27 === e.keyCode) { /* ESC key */ search.className += ' sf-dump-search-hidden'; e.preventDefault(); resetHighlightedNodes(root); searchInput.value = ''; } else if ( (isCtrlKey(e) && 71 === e.keyCode) /* CMD/CTRL + G */ || 13 === e.keyCode /* Enter */ || 114 === e.keyCode /* F3 */ ) { e.preventDefault(); e.shiftKey ? state.previous() : state.next(); collapseAll(root); showCurrent(state); } } }); } if (0 >= options.maxStringLength) { return; } try { elt = root.querySelectorAll('.sf-dump-str'); len = elt.length; i = 0; t = []; while (i < len) t.push(elt[i++]); len = t.length; for (i = 0; i < len; ++i) { elt = t[i]; s = elt.innerText || elt.textContent; x = s.length - options.maxStringLength; if (0 < x) { h = elt.innerHTML; elt[elt.innerText ? 'innerText' : 'textContent'] = s.substring(0, options.maxStringLength); elt.className += ' sf-dump-str-collapse'; elt.innerHTML = '<span class=sf-dump-str-collapse>'+h+'<a class="sf-dump-ref sf-dump-str-toggle" title="Collapse"> ◀</a></span>'+ '<span class=sf-dump-str-expand>'+elt.innerHTML+'<a class="sf-dump-ref sf-dump-str-toggle" title="'+x+' remaining characters"> ▶</a></span>'; } } } catch (e) { } }; })(document); </script><style> .phpdebugbar pre.sf-dump { display: block; white-space: pre; padding: 5px; overflow: initial !important; } .phpdebugbar pre.sf-dump:after { content: ""; visibility: hidden; display: block; height: 0; clear: both; } .phpdebugbar pre.sf-dump span { display: inline; } .phpdebugbar pre.sf-dump .sf-dump-compact { display: none; } .phpdebugbar pre.sf-dump a { text-decoration: none; cursor: pointer; border: 0; outline: none; color: inherit; } .phpdebugbar pre.sf-dump img { max-width: 50em; max-height: 50em; margin: .5em 0 0 0; padding: 0; background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAHUlEQVQY02O8zAABilCaiQEN0EeA8QuUcX9g3QEAAjcC5piyhyEAAAAASUVORK5CYII=) #D3D3D3; } .phpdebugbar pre.sf-dump .sf-dump-ellipsis { display: inline-block; overflow: visible; text-overflow: ellipsis; max-width: 5em; white-space: nowrap; overflow: hidden; vertical-align: top; } .phpdebugbar pre.sf-dump .sf-dump-ellipsis+.sf-dump-ellipsis { max-width: none; } .phpdebugbar pre.sf-dump code { display:inline; padding:0; background:none; } .sf-dump-str-collapse .sf-dump-str-collapse { display: none; } .sf-dump-str-expand .sf-dump-str-expand { display: none; } .sf-dump-public.sf-dump-highlight, .sf-dump-protected.sf-dump-highlight, .sf-dump-private.sf-dump-highlight, .sf-dump-str.sf-dump-highlight, .sf-dump-key.sf-dump-highlight { background: rgba(111, 172, 204, 0.3); border: 1px solid #7DA0B1; border-radius: 3px; } .sf-dump-public.sf-dump-highlight-active, .sf-dump-protected.sf-dump-highlight-active, .sf-dump-private.sf-dump-highlight-active, .sf-dump-str.sf-dump-highlight-active, .sf-dump-key.sf-dump-highlight-active { background: rgba(253, 175, 0, 0.4); border: 1px solid #ffa500; border-radius: 3px; } .phpdebugbar pre.sf-dump .sf-dump-search-hidden { display: none !important; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper { font-size: 0; white-space: nowrap; margin-bottom: 5px; display: flex; position: -webkit-sticky; position: sticky; top: 5px; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > * { vertical-align: top; box-sizing: border-box; height: 21px; font-weight: normal; border-radius: 0; background: #FFF; color: #757575; border: 1px solid #BBB; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > input.sf-dump-search-input { padding: 3px; height: 21px; font-size: 12px; border-right: none; border-top-left-radius: 3px; border-bottom-left-radius: 3px; color: #000; min-width: 15px; width: 100%; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next, .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-previous { background: #F2F2F2; outline: none; border-left: none; font-size: 0; line-height: 0; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next { border-top-right-radius: 3px; border-bottom-right-radius: 3px; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next > svg, .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-previous > svg { pointer-events: none; width: 12px; height: 12px; } .phpdebugbar pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-count { display: inline-block; padding: 0 5px; margin: 0; border-left: none; line-height: 21px; font-size: 12px; }.phpdebugbar pre.sf-dump, .phpdebugbar pre.sf-dump .sf-dump-default{word-wrap: break-word; white-space: pre-wrap; word-break: normal}.phpdebugbar pre.sf-dump .sf-dump-num{font-weight:bold; color:#1299DA}.phpdebugbar pre.sf-dump .sf-dump-const{font-weight:bold}.phpdebugbar pre.sf-dump .sf-dump-str{font-weight:bold; color:#3A9B26}.phpdebugbar pre.sf-dump .sf-dump-note{color:#1299DA}.phpdebugbar pre.sf-dump .sf-dump-ref{color:#7B7B7B}.phpdebugbar pre.sf-dump .sf-dump-public{color:#000000}.phpdebugbar pre.sf-dump .sf-dump-protected{color:#000000}.phpdebugbar pre.sf-dump .sf-dump-private{color:#000000}.phpdebugbar pre.sf-dump .sf-dump-meta{color:#B729D9}.phpdebugbar pre.sf-dump .sf-dump-key{color:#3A9B26}.phpdebugbar pre.sf-dump .sf-dump-index{color:#1299DA}.phpdebugbar pre.sf-dump .sf-dump-ellipsis{color:#A0A000}.phpdebugbar pre.sf-dump .sf-dump-ns{user-select:none;}.phpdebugbar pre.sf-dump .sf-dump-ellipsis-note{color:#1299DA}</style>
<script type="text/javascript">
var phpdebugbar = new PhpDebugBar.DebugBar();
phpdebugbar.addIndicator("php_version", new PhpDebugBar.DebugBar.Indicator({"icon":"code","tooltip":"Version"}), "right");
phpdebugbar.addTab("messages", new PhpDebugBar.DebugBar.Tab({"icon":"list-alt","title":"Messages", "widget": new PhpDebugBar.Widgets.MessagesWidget()}));
phpdebugbar.addIndicator("time", new PhpDebugBar.DebugBar.Indicator({"icon":"clock-o","tooltip":"Request Duration"}), "right");
phpdebugbar.addTab("timeline", new PhpDebugBar.DebugBar.Tab({"icon":"tasks","title":"Timeline", "widget": new PhpDebugBar.Widgets.TimelineWidget()}));
phpdebugbar.addIndicator("memory", new PhpDebugBar.DebugBar.Indicator({"icon":"cogs","tooltip":"Memory Usage"}), "right");
phpdebugbar.addTab("exceptions", new PhpDebugBar.DebugBar.Tab({"icon":"bug","title":"Exceptions", "widget": new PhpDebugBar.Widgets.ExceptionsWidget()}));
phpdebugbar.addTab("views", new PhpDebugBar.DebugBar.Tab({"icon":"leaf","title":"Views", "widget": new PhpDebugBar.Widgets.TemplatesWidget()}));
phpdebugbar.addTab("route", new PhpDebugBar.DebugBar.Tab({"icon":"share","title":"Route", "widget": new PhpDebugBar.Widgets.VariableListWidget()}));
phpdebugbar.addIndicator("currentroute", new PhpDebugBar.DebugBar.Indicator({"icon":"share","tooltip":"Route"}), "right");
phpdebugbar.addTab("queries", new PhpDebugBar.DebugBar.Tab({"icon":"database","title":"Queries", "widget": new PhpDebugBar.Widgets.LaravelSQLQueriesWidget()}));
phpdebugbar.addTab("emails", new PhpDebugBar.DebugBar.Tab({"icon":"inbox","title":"Mails", "widget": new PhpDebugBar.Widgets.MailsWidget()}));
phpdebugbar.addTab("auth", new PhpDebugBar.DebugBar.Tab({"icon":"lock","title":"Auth", "widget": new PhpDebugBar.Widgets.VariableListWidget()}));
phpdebugbar.addIndicator("auth.name", new PhpDebugBar.DebugBar.Indicator({"icon":"user","tooltip":"Auth status"}), "right");
phpdebugbar.addTab("gate", new PhpDebugBar.DebugBar.Tab({"icon":"list-alt","title":"Gate", "widget": new PhpDebugBar.Widgets.MessagesWidget()}));
phpdebugbar.addTab("session", new PhpDebugBar.DebugBar.Tab({"icon":"archive","title":"Session", "widget": new PhpDebugBar.Widgets.VariableListWidget()}));
phpdebugbar.addTab("request", new PhpDebugBar.DebugBar.Tab({"icon":"tags","title":"Request", "widget": new PhpDebugBar.Widgets.HtmlVariableListWidget()}));
phpdebugbar.setDataMap({
"php_version": ["php.version", ],
"messages": ["messages.messages", []],
"messages:badge": ["messages.count", null],
"time": ["time.duration_str", '0ms'],
"timeline": ["time", {}],
"memory": ["memory.peak_usage_str", '0B'],
"exceptions": ["exceptions.exceptions", []],
"exceptions:badge": ["exceptions.count", null],
"views": ["views", []],
"views:badge": ["views.nb_templates", 0],
"route": ["route", {}],
"currentroute": ["route.uri", ],
"queries": ["queries", []],
"queries:badge": ["queries.nb_statements", 0],
"emails": ["swiftmailer_mails.mails", []],
"emails:badge": ["swiftmailer_mails.count", null],
"auth": ["auth.guards", {}],
"auth.name": ["auth.names", ],
"gate": ["gate.messages", []],
"gate:badge": ["gate.count", null],
"session": ["session", {}],
"request": ["request", {}]
});
phpdebugbar.restoreState();
phpdebugbar.ajaxHandler = new PhpDebugBar.AjaxHandler(phpdebugbar, undefined, true);
phpdebugbar.ajaxHandler.bindToFetch();
phpdebugbar.ajaxHandler.bindToXHR();
phpdebugbar.setOpenHandler(new PhpDebugBar.OpenHandler({"url":"http:\/\/localhost:3501\/_debugbar\/open"}));
phpdebugbar.addDataSet({"__meta":{"id":"X4dd4240acb8bd07424d4ee21eba4d110","datetime":"2025-05-15 17:09:39","utime":1747328979.292877,"method":"GET","uri":"\/setup","ip":"**********"},"php":{"version":"7.3.14","interface":"fpm-fcgi"},"messages":{"count":0,"messages":[]},"time":{"start":1747328979.257207,"end":1747328979.292887,"duration":0.03568005561828613,"duration_str":"35.68ms","measures":[{"label":"Booting","start":1747328979.257207,"relative_start":0,"end":1747328979.259429,"relative_end":1747328979.259429,"duration":0.0022220611572265625,"duration_str":"2.22ms","params":[],"collector":null},{"label":"Application","start":1747328979.259475,"relative_start":0.002268075942993164,"end":1747328979.292889,"relative_end":2.1457672119140625e-6,"duration":0.03341412544250488,"duration_str":"33.41ms","params":[],"collector":null}]},"memory":{"peak_usage":5891792,"peak_usage_str":"6MB"},"exceptions":{"count":0,"exceptions":[]},"views":{"nb_templates":3,"templates":[{"name":"setup.index (resources\/views\/setup\/index.blade.php)","param_count":42,"params":["pageTitle","cacheDrivers","queueDrivers","mailDrivers","userLanguage","appUrl","cacheConfig","sessionConfig","queueConfig","mailConfig","__env","app","errors","aboutApp","appAnalytics","appAnalyticsGoSquared","appAnalyticsPiwikUrl","appAnalyticsPiwikSiteId","appBanner","appBannerStyleFullWidth","appBannerType","appDomain","appGraphs","appLocale","appStylesheet","appHeader","appFooter","appName","showSupport","automaticLocalization","enableExternalDependencies","showTimezone","appRefreshRate","timezone","siteTitle","fontSubset","onlyDisruptedDays","dashboardLink","enableSubscribers","currentUser","timezones","langs"],"type":"blade"},{"name":"layout.clean (resources\/views\/layout\/clean.blade.php)","param_count":52,"params":["obLevel","__env","app","errors","pageTitle","cacheDrivers","queueDrivers","mailDrivers","userLanguage","appUrl","cacheConfig","sessionConfig","queueConfig","mailConfig","aboutApp","appAnalytics","appAnalyticsGoSquared","appAnalyticsPiwikUrl","appAnalyticsPiwikSiteId","appBanner","appBannerStyleFullWidth","appBannerType","appDomain","appGraphs","appLocale","appStylesheet","appHeader","appFooter","appName","showSupport","automaticLocalization","enableExternalDependencies","showTimezone","appRefreshRate","timezone","siteTitle","fontSubset","onlyDisruptedDays","dashboardLink","enableSubscribers","currentUser","timezones","langs","__currentLoopData","driverName","driver","loop","list","region","name","lang","key"],"type":"blade"},{"name":"partials.crowdin (resources\/views\/partials\/crowdin.blade.php)","param_count":52,"params":["obLevel","__env","app","errors","pageTitle","cacheDrivers","queueDrivers","mailDrivers","userLanguage","appUrl","cacheConfig","sessionConfig","queueConfig","mailConfig","aboutApp","appAnalytics","appAnalyticsGoSquared","appAnalyticsPiwikUrl","appAnalyticsPiwikSiteId","appBanner","appBannerStyleFullWidth","appBannerType","appDomain","appGraphs","appLocale","appStylesheet","appHeader","appFooter","appName","showSupport","automaticLocalization","enableExternalDependencies","showTimezone","appRefreshRate","timezone","siteTitle","fontSubset","onlyDisruptedDays","dashboardLink","enableSubscribers","currentUser","timezones","langs","__currentLoopData","driverName","driver","loop","list","region","name","lang","key"],"type":"blade"}]},"route":{"uri":"GET setup","middleware":"Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, CachetHQ\\Cachet\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, setup","as":"core::get:setup","controller":"CachetHQ\\Cachet\\Http\\Controllers\\SetupController@getIndex","namespace":"CachetHQ\\Cachet\\Http\\Controllers","prefix":"\/setup","where":[],"file":"app\/Http\/Controllers\/SetupController.php:135-184"},"queries":{"nb_statements":1,"nb_failed_statements":0,"accumulated_duration":0.00235,"accumulated_duration_str":"2.35ms","statements":[{"sql":"select * from \"chq_settings\" where \"name\" = 'app_name' limit 1","type":"query","params":[],"bindings":["app_name"],"hints":["Use <code>SELECT *<\/code> only if you need all columns from table","<code>LIMIT<\/code> without <code>ORDER BY<\/code> causes non-deterministic results, depending on the query execution plan"],"backtrace":[{"index":15,"namespace":null,"name":"\/app\/Settings\/Repository.php","line":116},{"index":16,"namespace":"middleware","name":"setup","line":58},{"index":17,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":18,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":19,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Middleware\/SubstituteBindings.php","line":41},{"index":20,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":21,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":22,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Foundation\/Http\/Middleware\/VerifyCsrfToken.php","line":75},{"index":23,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":24,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":25,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/View\/Middleware\/ShareErrorsFromSession.php","line":49},{"index":26,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":27,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":28,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Session\/Middleware\/StartSession.php","line":63},{"index":29,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":30,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":31,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Cookie\/Middleware\/AddQueuedCookiesToResponse.php","line":37},{"index":32,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":33,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":34,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Cookie\/Middleware\/EncryptCookies.php","line":66},{"index":35,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":36,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":37,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":104},{"index":38,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Router.php","line":684},{"index":39,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Router.php","line":659},{"index":40,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Router.php","line":625},{"index":41,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Router.php","line":614},{"index":42,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Foundation\/Http\/Kernel.php","line":176},{"index":43,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":30},{"index":45,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":46,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53},{"index":47,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Foundation\/Http\/Middleware\/CheckForMaintenanceMode.php","line":62},{"index":48,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Pipeline\/Pipeline.php","line":163},{"index":49,"namespace":null,"name":"\/vendor\/laravel\/framework\/src\/Illuminate\/Routing\/Pipeline.php","line":53}],"duration":0.00235,"duration_str":"2.35ms","stmt_id":"\/app\/Settings\/Repository.php:116","connection":"cachet"}]},"swiftmailer_mails":{"count":0,"mails":[]},"auth":{"guards":{"web":"null","api":"null"},"names":""},"gate":{"count":0,"messages":[]},"session":{"_token":"p0oFtYL26hHrKfK791kHjpWOgwO20ZWRAsBYUrSP","_previous":"array:1 [\n  \"url\" => \"http:\/\/localhost:3501\/setup\"\n]","PHPDEBUGBAR_STACK_DATA":"[]"},"request":{"path_info":"\/setup","status_code":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200<\/span>\n<\/pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})<\/script>\n","status_text":"OK","format":"html","content_type":"text\/html; charset=UTF-8","request_query":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n<\/pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})<\/script>\n","request_request":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n<\/pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})<\/script>\n","request_headers":"<pre class=sf-dump id=sf-dump-530836627 data-indent-pad=\"  \"><span class=sf-dump-note>array:5<\/span> [<samp>\n  \"<span class=sf-dump-key>accept<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"3 characters\">*\/*<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>user-agent<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"11 characters\">curl\/8.12.1<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>host<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:3501<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>content-length<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"\"\n  <\/samp>]\n  \"<span class=sf-dump-key>content-type<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"\"\n  <\/samp>]\n<\/samp>]\n<\/pre><script>Sfdump(\"sf-dump-530836627\", {\"maxDepth\":0})<\/script>\n","request_server":"<pre class=sf-dump id=sf-dump-2058341335 data-indent-pad=\"  \"><span class=sf-dump-note>array:36<\/span> [<samp>\n  \"<span class=sf-dump-key>CACHE_DRIVER<\/span>\" => \"\"\n  \"<span class=sf-dump-key>DB_PASSWORD<\/span>\" => \"<span class=sf-dump-str title=\"6 characters\">******<\/span>\"\n  \"<span class=sf-dump-key>DB_USERNAME<\/span>\" => \"<span class=sf-dump-str title=\"8 characters\">postgres<\/span>\"\n  \"<span class=sf-dump-key>DB_DATABASE<\/span>\" => \"<span class=sf-dump-str title=\"6 characters\">cachet<\/span>\"\n  \"<span class=sf-dump-key>DB_HOST<\/span>\" => \"<span class=sf-dump-str title=\"15 characters\">postgres_cachet<\/span>\"\n  \"<span class=sf-dump-key>DB_DRIVER<\/span>\" => \"<span class=sf-dump-str title=\"5 characters\">pgsql<\/span>\"\n  \"<span class=sf-dump-key>USER<\/span>\" => \"<span class=sf-dump-str title=\"8 characters\">www-data<\/span>\"\n  \"<span class=sf-dump-key>HOME<\/span>\" => \"<span class=sf-dump-str title=\"14 characters\">\/home\/<USER>\/span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT<\/span>\" => \"<span class=sf-dump-str title=\"3 characters\">*\/*<\/span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT<\/span>\" => \"<span class=sf-dump-str title=\"11 characters\">curl\/8.12.1<\/span>\"\n  \"<span class=sf-dump-key>HTTP_HOST<\/span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:3501<\/span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME<\/span>\" => \"<span class=sf-dump-str title=\"30 characters\">\/var\/www\/html\/public\/index.php<\/span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS<\/span>\" => \"<span class=sf-dump-str title=\"3 characters\">200<\/span>\"\n  \"<span class=sf-dump-key>SERVER_NAME<\/span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost<\/span>\"\n  \"<span class=sf-dump-key>SERVER_PORT<\/span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000<\/span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR<\/span>\" => \"<span class=sf-dump-str title=\"11 characters\">**********4<\/span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT<\/span>\" => \"<span class=sf-dump-str title=\"5 characters\">46258<\/span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR<\/span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********<\/span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE<\/span>\" => \"<span class=sf-dump-str title=\"12 characters\">nginx\/1.17.8<\/span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE<\/span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI\/1.1<\/span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME<\/span>\" => \"<span class=sf-dump-str title=\"4 characters\">http<\/span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL<\/span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP\/1.1<\/span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT<\/span>\" => \"<span class=sf-dump-str title=\"20 characters\">\/var\/www\/html\/public<\/span>\"\n  \"<span class=sf-dump-key>DOCUMENT_URI<\/span>\" => \"<span class=sf-dump-str title=\"10 characters\">\/index.php<\/span>\"\n  \"<span class=sf-dump-key>REQUEST_URI<\/span>\" => \"<span class=sf-dump-str title=\"6 characters\">\/setup<\/span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME<\/span>\" => \"<span class=sf-dump-str title=\"10 characters\">\/index.php<\/span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH<\/span>\" => \"\"\n  \"<span class=sf-dump-key>CONTENT_TYPE<\/span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_METHOD<\/span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET<\/span>\"\n  \"<span class=sf-dump-key>QUERY_STRING<\/span>\" => \"\"\n  \"<span class=sf-dump-key>FCGI_ROLE<\/span>\" => \"<span class=sf-dump-str title=\"9 characters\">RESPONDER<\/span>\"\n  \"<span class=sf-dump-key>PHP_SELF<\/span>\" => \"<span class=sf-dump-str title=\"10 characters\">\/index.php<\/span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT<\/span>\" => <span class=sf-dump-num>1747328979.2572<\/span>\n  \"<span class=sf-dump-key>REQUEST_TIME<\/span>\" => <span class=sf-dump-num>1747328979<\/span>\n  \"<span class=sf-dump-key>argv<\/span>\" => []\n  \"<span class=sf-dump-key>argc<\/span>\" => <span class=sf-dump-num>0<\/span>\n<\/samp>]\n<\/pre><script>Sfdump(\"sf-dump-2058341335\", {\"maxDepth\":0})<\/script>\n","request_cookies":"<pre class=sf-dump id=sf-dump-869839540 data-indent-pad=\"  \">[]\n<\/pre><script>Sfdump(\"sf-dump-869839540\", {\"maxDepth\":0})<\/script>\n","response_headers":"<pre class=sf-dump id=sf-dump-477815737 data-indent-pad=\"  \"><span class=sf-dump-note>array:5<\/span> [<samp>\n  \"<span class=sf-dump-key>cache-control<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>date<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 15 May 2025 17:09:39 GMT<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>content-type<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"24 characters\">text\/html; charset=UTF-8<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>set-cookie<\/span>\" => <span class=sf-dump-note>array:2<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"316 characters\">XSRF-TOKEN=eyJpdiI6ImIzaXE1d3RvckhsZmZPdXhwdDdQckE9PSIsInZhbHVlIjoiaHdua2ZDald0WlJcL3MzQ1JVMk5lbHk5TTkzd1JcL1ZsV2ZEcDVvd0Z3R2FHanBmTlFKWlJjSmhiMmRhVnJsY3JOIiwibWFjIjoiZDY4OWEzZTQyYTBkNTYyMTVlNDk3ZDkxNWY0N2M2ZDI4NDVjOGRiOWNkMWM2Nzg3ZDVjYjJjYjY3NTVmYjA0YiJ9; expires=Thu, 15-May-2025 19:09:39 GMT; Max-Age=7200; path=\/<\/span>\"\n    <span class=sf-dump-index>1<\/span> => \"<span class=sf-dump-str title=\"335 characters\">laravel_session=eyJpdiI6IitSeTBzbUxtcUhFN0U0RmdTZTAyNGc9PSIsInZhbHVlIjoiWXB4YjVNSkc1RlN2bFdIeUhUODVyNGhnV1kyNTd5ZGgzeURBODRYYXFMTDJFMjNUdlU1bUJpQm9iUGsyZzJQOSIsIm1hYyI6ImExNzFiMTU1MjhjYzUyMjMzZWRlNzMzNTI3NDYyZDFiYzZlY2ZjYzBjYTlmOWMwMTA0MTYyYTdjYjBjM2M3YTMifQ%3D%3D; expires=Thu, 15-May-2025 19:09:39 GMT; Max-Age=7200; path=\/; httponly<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>Set-Cookie<\/span>\" => <span class=sf-dump-note>array:2<\/span> [<samp>\n    <span class=sf-dump-index>0<\/span> => \"<span class=sf-dump-str title=\"302 characters\">XSRF-TOKEN=eyJpdiI6ImIzaXE1d3RvckhsZmZPdXhwdDdQckE9PSIsInZhbHVlIjoiaHdua2ZDald0WlJcL3MzQ1JVMk5lbHk5TTkzd1JcL1ZsV2ZEcDVvd0Z3R2FHanBmTlFKWlJjSmhiMmRhVnJsY3JOIiwibWFjIjoiZDY4OWEzZTQyYTBkNTYyMTVlNDk3ZDkxNWY0N2M2ZDI4NDVjOGRiOWNkMWM2Nzg3ZDVjYjJjYjY3NTVmYjA0YiJ9; expires=Thu, 15-May-2025 19:09:39 GMT; path=\/<\/span>\"\n    <span class=sf-dump-index>1<\/span> => \"<span class=sf-dump-str title=\"321 characters\">laravel_session=eyJpdiI6IitSeTBzbUxtcUhFN0U0RmdTZTAyNGc9PSIsInZhbHVlIjoiWXB4YjVNSkc1RlN2bFdIeUhUODVyNGhnV1kyNTd5ZGgzeURBODRYYXFMTDJFMjNUdlU1bUJpQm9iUGsyZzJQOSIsIm1hYyI6ImExNzFiMTU1MjhjYzUyMjMzZWRlNzMzNTI3NDYyZDFiYzZlY2ZjYzBjYTlmOWMwMTA0MTYyYTdjYjBjM2M3YTMifQ%3D%3D; expires=Thu, 15-May-2025 19:09:39 GMT; path=\/; httponly<\/span>\"\n  <\/samp>]\n<\/samp>]\n<\/pre><script>Sfdump(\"sf-dump-477815737\", {\"maxDepth\":0})<\/script>\n","session_attributes":"<pre class=sf-dump id=sf-dump-41490729 data-indent-pad=\"  \"><span class=sf-dump-note>array:3<\/span> [<samp>\n  \"<span class=sf-dump-key>_token<\/span>\" => \"<span class=sf-dump-str title=\"40 characters\">p0oFtYL26hHrKfK791kHjpWOgwO20ZWRAsBYUrSP<\/span>\"\n  \"<span class=sf-dump-key>_previous<\/span>\" => <span class=sf-dump-note>array:1<\/span> [<samp>\n    \"<span class=sf-dump-key>url<\/span>\" => \"<span class=sf-dump-str title=\"27 characters\">http:\/\/localhost:3501\/setup<\/span>\"\n  <\/samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA<\/span>\" => []\n<\/samp>]\n<\/pre><script>Sfdump(\"sf-dump-41490729\", {\"maxDepth\":0})<\/script>\n"}}, "X4dd4240acb8bd07424d4ee21eba4d110");

</script>
</body>
<script src="http://localhost:3501/dist/js/all.js?id=bbc128edf91b2898cdf0"></script>
</html>
