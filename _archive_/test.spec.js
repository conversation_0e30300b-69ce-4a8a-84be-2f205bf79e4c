// ARCHIVED FILE
// Original Location: /test.spec.js
// Archived Date: 2025-06-03 23:56:15
// Reason: Root directory cleanup - moved to /tests directory
// File Size: 199 bytes
// Last Modified: 2025-05-14 17:16:51.781966718 +0200
// File Type: JavaScript source, ASCII text
// Dependencies: JavaScript imports found: 1 import statements
// References: 28 potential references found in codebase
// 
// [Original file content follows...]
// 


const { test, expect } = require('@playwright/test');

test('homepage has title', async ({ page }) => {
  await page.goto('http://localhost:3000');
  await expect(page).toHaveTitle(/TurdParty/);
}); 