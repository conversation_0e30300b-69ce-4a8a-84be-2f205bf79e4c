// ARCHIVED FILE
// Original Location: /test-file-upload-ui.js
// Archived Date: 2025-06-03 23:56:12
// Reason: Root directory cleanup - moved to /tests directory
// File Size: 6921 bytes
// Last Modified: 2025-05-14 17:16:51.780966726 +0200
// File Type: JavaScript source, ASCII text
// Dependencies: JavaScript imports found: 3 import statements
// References: 11 potential references found in codebase
// 
// [Original file content follows...]
// 


const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

(async () => {
  console.log('Starting browser test for file upload page...');
  
  // Create test file
  const testFilePath = '/tmp/test-upload.txt';
  fs.writeFileSync(testFilePath, 'This is a test file for upload testing.');
  console.log(`Test file created at ${testFilePath}`);
  
  // Ensure screenshot directory exists
  const screenshotDir = '/tmp/screenshots';
  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir, { recursive: true });
  }
  
  // Launch the browser
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Inside the container, we need to use the container hostname
    console.log('Navigating to http://turdparty_test_frontend:3000/file_upload');
    await page.goto('http://turdparty_test_frontend:3000/file_upload', { timeout: 30000 });
    
    // Wait for something to appear on the page
    await page.waitForTimeout(3000);
    
    // Take a screenshot
    await page.screenshot({ path: '/tmp/file-upload-page.png' });
    console.log('Screenshot saved to /tmp/file-upload-page.png');
    
    // Check for error messages
    const errorAlert = await page.$('.ant-alert-error');
    if (errorAlert) {
      console.log('Error alert found:');
      const errorMessage = await page.textContent('.ant-alert-message');
      const errorDescription = await page.textContent('.ant-alert-description');
      console.log('- Message:', errorMessage);
      console.log('- Description:', errorDescription);
      
      // Check if this is an authentication error and try to refresh the token
      if (errorDescription && errorDescription.includes('Authentication')) {
        console.log('Authentication error detected, trying to refresh token...');
        const refreshButton = await page.$('button:has-text("Refresh Authentication")');
        if (refreshButton) {
          await refreshButton.click();
          console.log('Refresh Authentication button clicked');
          await page.waitForTimeout(3000);
          
          // Take another screenshot after refresh
          await page.screenshot({ path: '/tmp/after-token-refresh.png' });
          console.log('Screenshot saved to /tmp/after-token-refresh.png');
        } else if (await page.$('button:has-text("Get Authentication Token")')) {
          // Alternative button text
          await page.click('button:has-text("Get Authentication Token")');
          console.log('Get Authentication Token button clicked');
          await page.waitForTimeout(3000);
          
          await page.screenshot({ path: '/tmp/after-token-refresh.png' });
          console.log('Screenshot saved to /tmp/after-token-refresh.png');
        }
      }
    } else {
      console.log('No authentication error alerts found');
    }
    
    // Check if we can click the upload button
    const uploadButton = await page.$('button:has-text("Upload New File")');
    if (uploadButton) {
      console.log('Found Upload New File button, clicking it...');
      await uploadButton.click();
      await page.waitForTimeout(2000);
      
      // Take a screenshot of the upload dialog
      await page.screenshot({ path: '/tmp/upload-dialog.png' });
      console.log('Screenshot saved to /tmp/upload-dialog.png');
      
      // Check if there's a file input
      const fileInput = await page.$('input[type="file"]');
      if (fileInput) {
        console.log('Found file input, uploading test file...');
        await fileInput.setInputFiles(testFilePath);
        await page.waitForTimeout(2000);
        
        // Take a screenshot after file selection
        await page.screenshot({ path: '/tmp/after-file-selection.png' });
        console.log('Screenshot saved to /tmp/after-file-selection.png');
        
        // Add a description
        const descInput = await page.$('textarea[placeholder="Description"]');
        if (descInput) {
          await descInput.fill('Test upload from e2e test script');
          console.log('Added description text');
        }
        
        // Click the upload button in the dialog
        const submitButton = await page.$('.ant-modal-footer button.ant-btn-primary');
        if (submitButton) {
          console.log('Clicking submit button...');
          await submitButton.click();
          await page.waitForTimeout(5000); // Wait for upload to complete
          
          // Take a screenshot after upload attempt
          await page.screenshot({ path: '/tmp/after-upload-attempt.png' });
          console.log('Screenshot saved to /tmp/after-upload-attempt.png');
          
          // Check for success message
          const successMessage = await page.$('.ant-message-success');
          if (successMessage) {
            console.log('SUCCESS: Upload completed successfully!');
          } else {
            console.log('No success message found, checking for errors...');
            const errorMessage = await page.$('.ant-message-error');
            if (errorMessage) {
              console.log('ERROR: Upload failed with error message');
              const errorText = await page.textContent('.ant-message-error');
              console.log('Error text:', errorText);
            } else {
              console.log('No error message found, status unknown');
            }
          }
        }
      } else {
        console.log('No file input found in the upload dialog');
      }
    } else {
      console.log('Upload New File button not found');
    }
    
    // Check for the file table
    const fileTable = await page.$('.file-upload-table');
    if (fileTable) {
      console.log('File table found');
      
      // Count number of rows
      const rowCount = await page.$$eval('tbody tr', rows => rows.length);
      console.log(`Table has ${rowCount} rows`);
      
      // Get text content of the table to see if our file is there
      const tableContent = await page.textContent('.file-upload-table');
      console.log('Table content contains uploaded file:', tableContent.includes('test-upload.txt'));
    } else {
      console.log('No file table found');
    }
    
    // Get the HTML content of the page
    const html = await page.content();
    console.log('\nPage HTML (first 500 chars):');
    console.log(html.substring(0, 500) + '...');
    
    console.log('\nTest completed successfully');
  } catch (error) {
    console.error('Test error:', error);
    try {
      await page.screenshot({ path: '/tmp/file-upload-error.png' });
      console.log('Error screenshot saved to /tmp/file-upload-error.png');
    } catch (screenshotError) {
      console.error('Could not save error screenshot:', screenshotError);
    }
  } finally {
    await browser.close();
  }
})(); 