# ARCHIVED FILE
# Original Location: /test_item_model.py
# Archived Date: 2025-06-03 23:56:13
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 5628 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 5 import statements
# References: 41 potential references found in codebase
# 
# [Original file content follows...]
# 


#!/usr/bin/env python3

"""
Simple database test using SQLite directly.
This test helps verify we can use SQLite properly after removing Streamlit.
"""

import os
import sys
import sqlite3
import unittest
from datetime import datetime

class TestSQLiteDatabase(unittest.TestCase):
    """Test basic SQLite database functionality."""
    
    def setUp(self):
        """Set up the test database."""
        # Create an in-memory SQLite database
        self.conn = sqlite3.connect(':memory:')
        self.cursor = self.conn.cursor()
        
        # Create test tables
        self.cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            username TEXT NOT NULL,
            email TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        self.cursor.execute('''
        CREATE TABLE items (
            id INTEGER PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            owner_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (owner_id) REFERENCES users (id)
        )
        ''')
        
        self.conn.commit()
        
    def tearDown(self):
        """Clean up after the test."""
        self.conn.close()
        
    def test_create_user(self):
        """Test creating a user."""
        self.cursor.execute(
            "INSERT INTO users (username, email) VALUES (?, ?)",
            ("testuser", "<EMAIL>")
        )
        self.conn.commit()
        
        self.cursor.execute("SELECT * FROM users")
        users = self.cursor.fetchall()
        
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0][1], "testuser")
        self.assertEqual(users[0][2], "<EMAIL>")
        
    def test_create_item(self):
        """Test creating an item with a user reference."""
        # Create a user first
        self.cursor.execute(
            "INSERT INTO users (username, email) VALUES (?, ?)",
            ("itemowner", "<EMAIL>")
        )
        self.conn.commit()
        
        # Get the user ID
        self.cursor.execute("SELECT id FROM users WHERE username = ?", ("itemowner",))
        user_id = self.cursor.fetchone()[0]
        
        # Create an item
        self.cursor.execute(
            "INSERT INTO items (title, description, owner_id) VALUES (?, ?, ?)",
            ("Test Item", "This is a test item", user_id)
        )
        self.conn.commit()
        
        # Check that the item was created
        self.cursor.execute("SELECT * FROM items")
        items = self.cursor.fetchall()
        
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0][1], "Test Item")
        self.assertEqual(items[0][2], "This is a test item")
        self.assertEqual(items[0][3], user_id)
        
    def test_update_item(self):
        """Test updating an item."""
        # Create a user first
        self.cursor.execute(
            "INSERT INTO users (username, email) VALUES (?, ?)",
            ("updateuser", "<EMAIL>")
        )
        self.conn.commit()
        
        # Get the user ID
        self.cursor.execute("SELECT id FROM users WHERE username = ?", ("updateuser",))
        user_id = self.cursor.fetchone()[0]
        
        # Create an item
        self.cursor.execute(
            "INSERT INTO items (title, description, owner_id) VALUES (?, ?, ?)",
            ("Original Title", "Original Description", user_id)
        )
        self.conn.commit()
        
        # Get the item ID
        self.cursor.execute("SELECT id FROM items WHERE title = ?", ("Original Title",))
        item_id = self.cursor.fetchone()[0]
        
        # Update the item
        self.cursor.execute(
            "UPDATE items SET title = ?, description = ? WHERE id = ?",
            ("Updated Title", "Updated Description", item_id)
        )
        self.conn.commit()
        
        # Check that the item was updated
        self.cursor.execute("SELECT title, description FROM items WHERE id = ?", (item_id,))
        updated_item = self.cursor.fetchone()
        
        self.assertEqual(updated_item[0], "Updated Title")
        self.assertEqual(updated_item[1], "Updated Description")
        
    def test_delete_item(self):
        """Test deleting an item."""
        # Create a user first
        self.cursor.execute(
            "INSERT INTO users (username, email) VALUES (?, ?)",
            ("deleteuser", "<EMAIL>")
        )
        self.conn.commit()
        
        # Get the user ID
        self.cursor.execute("SELECT id FROM users WHERE username = ?", ("deleteuser",))
        user_id = self.cursor.fetchone()[0]
        
        # Create an item
        self.cursor.execute(
            "INSERT INTO items (title, description, owner_id) VALUES (?, ?, ?)",
            ("Delete Me", "This item will be deleted", user_id)
        )
        self.conn.commit()
        
        # Get the item ID
        self.cursor.execute("SELECT id FROM items WHERE title = ?", ("Delete Me",))
        item_id = self.cursor.fetchone()[0]
        
        # Delete the item
        self.cursor.execute("DELETE FROM items WHERE id = ?", (item_id,))
        self.conn.commit()
        
        # Check that the item was deleted
        self.cursor.execute("SELECT * FROM items WHERE id = ?", (item_id,))
        deleted_item = self.cursor.fetchone()
        
        self.assertIsNone(deleted_item)

if __name__ == "__main__":
    unittest.main() 