#!/bin/bash

# Archive Validation Script
# Validates the integrity of archived files and metadata

set -e

ARCHIVE_ROOT="_archive_"
ERRORS=0

echo "Validating archive integrity..."

# Check if all archived files have corresponding metadata
for archived_file in $(find "$ARCHIVE_ROOT" -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.html" -o -name "*.css" -o -name "*.sh" -o -name "*.md" | grep -v metadata | grep -v README.md | grep -v cleanup-log.md); do
    basename_no_ext=$(basename "$archived_file" | sed 's/\./_/g')
    metadata_file="$ARCHIVE_ROOT/metadata/${basename_no_ext}_metadata.json"
    
    if [ ! -f "$metadata_file" ]; then
        echo "ERROR: Missing metadata for $archived_file"
        ((ERRORS++))
    fi
done

# Check if all metadata files have corresponding archived files
for metadata_file in $(find "$ARCHIVE_ROOT/metadata" -name "*_metadata.json"); do
    # Extract original path from metadata
    if [ -f "$metadata_file" ]; then
        archive_path=$(grep '"archive_path"' "$metadata_file" | cut -d'"' -f4 | sed 's/^\///')
        if [ ! -f "$archive_path" ]; then
            echo "ERROR: Missing archived file for metadata $metadata_file"
            ((ERRORS++))
        fi
    fi
done

# Validate JSON metadata files
for metadata_file in $(find "$ARCHIVE_ROOT/metadata" -name "*_metadata.json"); do
    if ! python3 -m json.tool "$metadata_file" > /dev/null 2>&1; then
        echo "ERROR: Invalid JSON in $metadata_file"
        ((ERRORS++))
    fi
done

if [ $ERRORS -eq 0 ]; then
    echo "✓ Archive validation passed - no errors found"
    exit 0
else
    echo "✗ Archive validation failed - $ERRORS errors found"
    exit 1
fi
