# ARCHIVED FILE
# Original Location: /test_routes.py
# Archived Date: 2025-06-03 23:56:13
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 5065 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 4 import statements
# References: 60 potential references found in codebase
# 
# [Original file content follows...]
# 


#!/usr/bin/env python3
"""
Script to test the API routes.
"""
import sys
import logging
from typing import Dict, Any

from ui.services.item_service import ItemService

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_item_routes(api_url: str) -> Dict[str, Any]:
    """
    Test all item-related API routes.
    
    Args:
        api_url: Base URL for the API
        
    Returns:
        Dictionary with test results
    """
    results = {
        "success": True,
        "tests": {
            "get_items": {"status": "not run"},
            "create_item": {"status": "not run"},
            "get_item": {"status": "not run"},
            "update_item": {"status": "not run"},
            "delete_item": {"status": "not run"}
        }
    }
    
    logger.info(f"Testing API routes at {api_url}")
    item_service = ItemService(api_url)
    item_id = None
    
    try:
        # Test GET /items
        logger.info("Testing GET /items")
        items = item_service.get_items()
        results["tests"]["get_items"] = {
            "status": "success",
            "items_count": len(items)
        }
        logger.info(f"Found {len(items)} items")
        
        # Test POST /items
        logger.info("Testing POST /items")
        new_item = item_service.create_item(
            name="Test Item",
            description="Created during route testing"
        )
        if new_item and new_item.id:
            item_id = new_item.id
            results["tests"]["create_item"] = {
                "status": "success",
                "item_id": item_id
            }
            logger.info(f"Created item with ID {item_id}")
        else:
            results["success"] = False
            results["tests"]["create_item"] = {
                "status": "failed",
                "error": "Could not create item"
            }
            logger.error("Failed to create item")
            return results
        
        # Test GET /items/{id}
        logger.info(f"Testing GET /items/{item_id}")
        item = item_service.get_item(item_id)
        if item and item.id == item_id:
            results["tests"]["get_item"] = {
                "status": "success",
                "item_name": item.name
            }
            logger.info(f"Got item: {item.name}")
        else:
            results["success"] = False
            results["tests"]["get_item"] = {
                "status": "failed",
                "error": "Could not get item"
            }
            logger.error(f"Failed to get item {item_id}")
            return results
        
        # Test PUT /items/{id}
        logger.info(f"Testing PUT /items/{item_id}")
        updated_item = item_service.update_item(
            item_id=item_id,
            name="Updated Test Item",
            description="Updated during route testing"
        )
        if updated_item and updated_item.name == "Updated Test Item":
            results["tests"]["update_item"] = {
                "status": "success",
                "item_name": updated_item.name
            }
            logger.info(f"Updated item: {updated_item.name}")
        else:
            results["success"] = False
            results["tests"]["update_item"] = {
                "status": "failed",
                "error": "Could not update item"
            }
            logger.error(f"Failed to update item {item_id}")
            return results
        
        # Test DELETE /items/{id}
        logger.info(f"Testing DELETE /items/{item_id}")
        delete_result = item_service.delete_item(item_id)
        if delete_result:
            results["tests"]["delete_item"] = {
                "status": "success"
            }
            logger.info(f"Deleted item {item_id}")
        else:
            results["success"] = False
            results["tests"]["delete_item"] = {
                "status": "failed",
                "error": "Could not delete item"
            }
            logger.error(f"Failed to delete item {item_id}")
            
    except Exception as e:
        logger.error(f"Error during route testing: {str(e)}")
        results["success"] = False
        results["error"] = str(e)
        
    return results

def main():
    """Main function."""
    if len(sys.argv) > 1:
        api_url = sys.argv[1]
    else:
        api_url = "http://localhost:8000"
    
    results = test_item_routes(api_url)
    
    if results["success"]:
        logger.info("All route tests passed!")
        for test_name, test_result in results["tests"].items():
            logger.info(f"{test_name}: {test_result['status']}")
    else:
        logger.error("Route tests failed!")
        for test_name, test_result in results["tests"].items():
            if test_result["status"] == "failed":
                logger.error(f"{test_name}: {test_result.get('error', 'Unknown error')}")
    
    return 0 if results["success"] else 1

if __name__ == "__main__":
    sys.exit(main()) 