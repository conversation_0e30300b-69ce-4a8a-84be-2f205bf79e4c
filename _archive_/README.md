# TurdParty Archive Directory

This directory contains files that have been removed from the main codebase during the cleanup process. Each file is preserved with metadata indicating its original location and the reason for archiving.

## Purpose

The archive system serves several purposes:
- **Preservation**: Keeps deleted files for potential future reference
- **Documentation**: Records why files were removed and when
- **Safety**: Provides a rollback mechanism if needed
- **Audit Trail**: Maintains a complete history of cleanup actions

## Structure

The archive maintains the same directory structure as the original codebase:

```
_archive_/
├── README.md                    # This file
├── cleanup-log.md              # Detailed log of all cleanup actions
├── api/                        # Archived API files
├── frontend/                   # Archived frontend files
├── ui/                         # Archived UI files
├── scripts/                    # Archived scripts
├── tests/                      # Archived test files
├── docs/                       # Archived documentation
├── config/                     # Archived configuration files
├── root/                       # Archived root directory files
├── metadata/                   # JSON metadata for each archived file
└── logs/                       # Archive operation logs
```

## File Format

Each archived file includes a header comment with:
- Original location
- Date of archiving
- Reason for archiving
- File metadata (size, type, last modified)
- Dependency analysis
- Reference count in codebase

### Example Header (Python file):
```python
# ARCHIVED FILE
# Original Location: /test_old.py
# Archived Date: 2024-01-15 14:30:00
# Reason: Duplicate functionality, replaced by /tests/test_main.py
# File Size: 1024 bytes
# Last Modified: 2024-01-10 10:15:00
# File Type: Python script
# Dependencies: None identified
# References: No references found in codebase
#
# [Original file content follows...]
```

## Usage

### Archiving Files
Use the archive script to move files to the archive:
```bash
./scripts/archive-file.sh path/to/file.py "Reason for archiving" "Optional notes"
```

### Restoring Files
Use the restore script to bring files back:
```bash
./scripts/restore-from-archive.sh _archive_/path/to/file.py
```

### Searching Archives
Find archived files by name or content:
```bash
# Find by filename
find _archive_ -name "*.py" -type f

# Search content in archived files
grep -r "search_term" _archive_/
```

## Metadata

Each archived file has corresponding JSON metadata in the `metadata/` directory containing:
- Original and archive paths
- Archival timestamp and reason
- File information (size, type, modification date)
- Dependency analysis results
- Reference count in codebase
- Git information (commit, branch)
- User who performed the archival

## Cleanup Log

The `cleanup-log.md` file maintains a chronological record of all cleanup actions in markdown table format for easy review.

## Best Practices

1. **Always provide clear reasons** for archiving files
2. **Check for references** before archiving to avoid breaking dependencies
3. **Review archived files periodically** to determine if they can be permanently deleted
4. **Keep the cleanup log updated** for audit purposes
5. **Test functionality** after archiving to ensure nothing is broken

## Maintenance

- Review archived files quarterly to identify candidates for permanent deletion
- Clean up metadata files for permanently deleted archives
- Compress old archives if disk space becomes a concern
- Update this documentation as the archive system evolves

---
*Archive system initialized on: 2025-06-03 23:44:07*
