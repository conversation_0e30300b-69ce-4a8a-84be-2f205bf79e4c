{"original_path": "/file_upload.png", "archive_path": "/_archive_/file_upload.png", "archived_date": "2025-06-03 23:56:17", "reason": "Root directory cleanup - temporary/debug file", "additional_notes": "", "file_info": {"size_bytes": "5593", "last_modified": "2025-05-14 17:16:51.776966757 +0200", "file_type": "PNG image data, 1280 x 720, 8-bit/color RGB, non-interlaced", "extension": "png"}, "analysis": {"dependencies": "", "references_found": 13}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}