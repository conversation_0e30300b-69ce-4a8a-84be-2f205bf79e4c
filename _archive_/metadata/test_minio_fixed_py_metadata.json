{"original_path": "/test_minio_fixed.py", "archive_path": "/_archive_/test_minio_fixed.py", "archived_date": "2025-06-03 23:56:13", "reason": "Root directory cleanup - moved to /tests directory", "additional_notes": "", "file_info": {"size_bytes": "3755", "last_modified": "2025-05-14 17:16:51.781966718 +0200", "file_type": "Python script, ASCII text executable", "extension": "py"}, "analysis": {"dependencies": "Python imports found: 5 import statements", "references_found": 12}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}