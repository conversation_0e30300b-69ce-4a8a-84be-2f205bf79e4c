{"original_path": "/./docker-compose.testing.yml", "archive_path": "/_archive_/./docker-compose.testing.yml", "archived_date": "2025-06-03 23:56:19", "reason": "Root directory cleanup - duplicate configuration", "additional_notes": "", "file_info": {"size_bytes": "1951", "last_modified": "2025-05-14 17:16:51.775966765 +0200", "file_type": "ASCII text", "extension": "yml"}, "analysis": {"dependencies": "", "references_found": 14}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}