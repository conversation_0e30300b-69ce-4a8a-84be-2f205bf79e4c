{"original_path": "/debug_openapi.py", "archive_path": "/_archive_/debug_openapi.py", "archived_date": "2025-06-03 23:56:18", "reason": "Root directory cleanup - temporary/debug file", "additional_notes": "", "file_info": {"size_bytes": "2152", "last_modified": "2025-05-15 11:32:58.000000000 +0200", "file_type": "Python script, Unicode text, UTF-8 text executable", "extension": "py"}, "analysis": {"dependencies": "Python imports found: 4 import statements", "references_found": 3}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}