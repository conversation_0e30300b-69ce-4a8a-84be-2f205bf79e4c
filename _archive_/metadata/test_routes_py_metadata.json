{"original_path": "/test_routes.py", "archive_path": "/_archive_/test_routes.py", "archived_date": "2025-06-03 23:56:13", "reason": "Root directory cleanup - moved to /tests directory", "additional_notes": "", "file_info": {"size_bytes": "5065", "last_modified": "2025-05-14 17:16:51.781966718 +0200", "file_type": "Python script, ASCII text executable", "extension": "py"}, "analysis": {"dependencies": "Python imports found: 4 import statements", "references_found": 60}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}