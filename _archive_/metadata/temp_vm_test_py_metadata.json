{"original_path": "/temp_vm_test.py", "archive_path": "/_archive_/temp_vm_test.py", "archived_date": "2025-06-03 23:56:18", "reason": "Root directory cleanup - temporary/debug file", "additional_notes": "", "file_info": {"size_bytes": "4256", "last_modified": "2025-05-14 17:16:51.780966726 +0200", "file_type": "Python script, ASCII text executable", "extension": "py"}, "analysis": {"dependencies": "Python imports found: 4 import statements", "references_found": 14}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}