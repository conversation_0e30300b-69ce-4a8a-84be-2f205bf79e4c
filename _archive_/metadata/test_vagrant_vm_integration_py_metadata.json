{"original_path": "/api/tests/test_vagrant_vm_integration.py", "archive_path": "/_archive_/api/tests/test_vagrant_vm_integration.py", "archived_date": "2025-06-03 23:54:14", "reason": "Duplicate test file - functionality exists in api/tests/test_integration/test_vagrant_vm_integration.py", "additional_notes": "", "file_info": {"size_bytes": "15272", "last_modified": "2025-05-14 17:16:51.773966781 +0200", "file_type": "Python script, ASCII text executable", "extension": "py"}, "analysis": {"dependencies": "Python imports found: 5 import statements", "references_found": 42}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}