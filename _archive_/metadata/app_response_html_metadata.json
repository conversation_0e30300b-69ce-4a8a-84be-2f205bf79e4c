{"original_path": "/app_response.html", "archive_path": "/_archive_/app_response.html", "archived_date": "2025-06-03 23:56:15", "reason": "Root directory cleanup - temporary/debug file", "additional_notes": "", "file_info": {"size_bytes": "356250", "last_modified": "2025-05-15 19:09:39.362744405 +0200", "file_type": "HTML document, ASCII text, with very long lines (14749)", "extension": "html"}, "analysis": {"dependencies": "", "references_found": 4}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}