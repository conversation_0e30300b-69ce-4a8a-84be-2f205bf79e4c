{"original_path": "/ui_test_screenshot.png", "archive_path": "/_archive_/ui_test_screenshot.png", "archived_date": "2025-06-03 23:56:17", "reason": "Root directory cleanup - temporary/debug file", "additional_notes": "", "file_info": {"size_bytes": "5486", "last_modified": "2025-05-14 17:16:51.781966718 +0200", "file_type": "PNG image data, 1280 x 720, 8-bit/color RGB, non-interlaced", "extension": "png"}, "analysis": {"dependencies": "", "references_found": 3}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}