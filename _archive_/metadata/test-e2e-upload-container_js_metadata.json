{"original_path": "/test-e2e-upload-container.js", "archive_path": "/_archive_/test-e2e-upload-container.js", "archived_date": "2025-06-03 23:56:12", "reason": "Root directory cleanup - moved to /tests directory", "additional_notes": "", "file_info": {"size_bytes": "5060", "last_modified": "2025-05-14 17:16:51.780966726 +0200", "file_type": "JavaScript source, Unicode text, UTF-8 text", "extension": "js"}, "analysis": {"dependencies": "JavaScript imports found: 3 import statements", "references_found": 7}, "archived_by": "cvr", "git_commit": "329809977debac584ebc204925f01bdf14c28dba", "git_branch": "refactor/folder-structure-cleanup"}