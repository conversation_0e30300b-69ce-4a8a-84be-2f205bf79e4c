# ARCHIVED FILE
# Original Location: /./docker-compose.diagnostic.yml
# Archived Date: 2025-06-03 23:56:19
# Reason: Root directory cleanup - duplicate configuration
# File Size: 1132 bytes
# Last Modified: 2025-05-14 17:16:51.775966765 +0200
# File Type: ASCII text
# Dependencies: None identified
# References: 3 potential references found in codebase
# 
# [Original file content follows...]
# 


services:
  diagnostic:
    build:
      context: .
      dockerfile: Dockerfile.diagnostic
    volumes:
      - ./:/app
      - ./tests:/app/tests:ro
    environment:
      - PYTHONPATH=/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - DOCKER_NETWORK=true
    depends_on:
      - postgres
      - minio
    networks:
      - test_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty_test
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - test_network
      
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - test_network

networks:
  test_network:
    driver: bridge

volumes:
  postgres_data:
  minio_data: 