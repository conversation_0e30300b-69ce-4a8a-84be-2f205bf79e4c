# ARCHIVED FILE
# Original Location: /test_upload.py
# Archived Date: 2025-06-03 23:56:14
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 431 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: ASCII text
# Dependencies: Python imports found: 2 import statements
# References: 7 potential references found in codebase
# 
# [Original file content follows...]
# 


import requests
import os

# Create a test file
with open("test.txt", "w") as f:
    f.write("Test file content")

# Upload the file
url = "http://localhost:8000/api/v1/file_upload/"
files = {"file": open("test.txt", "rb")}
data = {"description": "Test file"}

response = requests.post(url, files=files, data=data)
print(f"Status code: {response.status_code}")
print(f"Response: {response.text}")

# Clean up
os.remove("test.txt")
