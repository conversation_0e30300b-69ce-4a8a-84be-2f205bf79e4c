# ARCHIVED FILE
# Original Location: /test_redirect.py
# Archived Date: 2025-06-03 23:56:13
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 1180 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 2 import statements
# References: 15 potential references found in codebase
# 
# [Original file content follows...]
# 


from playwright.sync_api import sync_playwright
import sys

def test_api_root_redirect():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # Navigate to the API root
        page.goto('http://localhost:3050/api/v1/')
        
        # Wait for navigation to complete
        page.wait_for_load_state('networkidle')
        
        # Get the current URL after redirect
        current_url = page.url
        print(f"Redirected to: {current_url}")
        
        # Take a screenshot
        page.screenshot(path='redirect_result.png')
        
        browser.close()
        
        # Return the URL for analysis
        return current_url

if __name__ == "__main__":
    redirected_url = test_api_root_redirect()
    print(f"Final URL after redirect: {redirected_url}")
    
    # Check if the redirect is correct
    if redirected_url == "http://localhost:3050/api/v1/docs":
        print("SUCCESS: Redirect is correct")
        sys.exit(0)
    else:
        print(f"ERROR: Redirect is incorrect. Expected http://localhost:3050/api/v1/docs but got {redirected_url}")
        sys.exit(1)
