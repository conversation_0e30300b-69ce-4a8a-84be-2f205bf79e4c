# ARCHIVED FILE
# Original Location: /debug_file_upload.py
# Archived Date: 2025-06-03 23:56:18
# Reason: Root directory cleanup - temporary/debug file
# File Size: 1007 bytes
# Last Modified: 2025-05-14 17:16:51.775966765 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 4 import statements
# References: 3 potential references found in codebase
# 
# [Original file content follows...]
# 


import asyncio
import requests
import os
import json

async def main():
    # Path to the file to upload
    file_path = '/app/service_monitor.py'
    
    # Check if the file exists
    if not os.path.exists(file_path):
        print(f'File not found: {file_path}')
        return
    
    # Get the file size
    file_size = os.path.getsize(file_path)
    print(f'File size: {file_size} bytes')
    
    # Read the file content
    with open(file_path, 'rb') as f:
        file_content = f.read()
    
    # Create a multipart form data
    files = {'file': ('service_monitor.py', file_content, 'text/x-python')}
    data = {'description': 'Test upload'}
    
    # Make the request
    try:
        response = requests.post('http://127.0.0.1:8000/api/v1/file_upload/', files=files, data=data)
        print(f'Status code: {response.status_code}')
        print(f'Response: {response.text}')
    except Exception as e:
        print(f'Error: {str(e)}')

if __name__ == '__main__':
    asyncio.run(main())
