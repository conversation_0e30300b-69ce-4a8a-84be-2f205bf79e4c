# ARCHIVED FILE
# Original Location: /test_minio_fixed.py
# Archived Date: 2025-06-03 23:56:13
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 3755 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 5 import statements
# References: 12 potential references found in codebase
# 
# [Original file content follows...]
# 


import os
import sys
import logging
import pytest
from minio import Minio
from minio.error import S3Error

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use environment variables or defaults for MinIO connection
MINIO_HOST = os.environ.get("MINIO_HOST", "minio")
MINIO_PORT = os.environ.get("MINIO_PORT", "9000")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY", "minioadmin")
IS_DOCKER = os.environ.get("DOCKER_NETWORK", "false").lower() == "true"

# Test bucket name
TEST_BUCKET = "test-bucket"

def get_minio_client():
    """Create and return a MinIO client."""
    endpoint = f"{MINIO_HOST}:{MINIO_PORT}"
    logger.info(f"Connecting to MinIO at {endpoint}")
    
    client = Minio(
        endpoint,
        access_key=MINIO_ACCESS_KEY,
        secret_key=MINIO_SECRET_KEY,
        secure=False  # Use HTTP instead of HTTPS for local testing
    )
    return client

def test_minio_connection():
    """Test basic connection to MinIO server."""
    client = get_minio_client()
    
    try:
        logger.info("Testing MinIO connection...")
        # List buckets to test connection
        buckets = client.list_buckets()
        logger.info(f"Successfully connected to MinIO. Found {len(buckets)} buckets.")
        
        # Create a test bucket if it doesn't exist
        if not client.bucket_exists(TEST_BUCKET):
            logger.info(f"Creating test bucket: {TEST_BUCKET}")
            client.make_bucket(TEST_BUCKET)
            logger.info(f"Created test bucket: {TEST_BUCKET}")
        else:
            logger.info(f"Test bucket already exists: {TEST_BUCKET}")
        
        # Test successful
        assert True
        
    except S3Error as err:
        logger.error(f"Error connecting to MinIO: {err}")
        pytest.fail(f"MinIO connection failed: {err}")
        
def test_minio_upload_download():
    """Test upload and download functionality."""
    client = get_minio_client()
    
    test_filename = "test_file.txt"
    test_content = b"This is a test file for MinIO uploads and downloads."
    
    try:
        # Make sure bucket exists
        if not client.bucket_exists(TEST_BUCKET):
            client.make_bucket(TEST_BUCKET)
            
        # Create a temporary file
        with open(test_filename, "wb") as f:
            f.write(test_content)
            
        # Upload file to MinIO
        logger.info(f"Uploading test file to MinIO bucket {TEST_BUCKET}...")
        client.fput_object(
            TEST_BUCKET, 
            test_filename, 
            test_filename,
        )
        
        # Download the file with a different name
        download_filename = "test_file_downloaded.txt"
        logger.info(f"Downloading test file from MinIO bucket {TEST_BUCKET}...")
        client.fget_object(
            TEST_BUCKET,
            test_filename,
            download_filename,
        )
        
        # Verify downloaded content
        with open(download_filename, "rb") as f:
            downloaded_content = f.read()
            
        # Clean up
        os.remove(test_filename)
        os.remove(download_filename)
        
        # Verify content matches
        assert downloaded_content == test_content
        logger.info("Upload and download test successful!")
        
    except S3Error as err:
        logger.error(f"Error testing MinIO uploads/downloads: {err}")
        pytest.fail(f"MinIO upload/download failed: {err}")

if __name__ == "__main__":
    # Run tests manually when script is executed directly
    test_minio_connection()
    test_minio_upload_download()
    print("All tests passed!") 