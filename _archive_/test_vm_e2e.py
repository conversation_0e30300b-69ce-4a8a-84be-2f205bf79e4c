# ARCHIVED FILE
# Original Location: /test_vm_e2e.py
# Archived Date: 2025-06-03 23:56:14
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 10662 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 5 import statements
# References: 4 potential references found in codebase
# 
# [Original file content follows...]
# 


#!/usr/bin/env python3

"""
End-to-end test for VM file injection.
This script tests the complete flow:
1. Upload a file to the API
2. Create a VM
3. Inject the file into the VM
4. Verify the file injection
5. Tear down the VM
"""

import os
import sys
import json
import time
import logging
import asyncio
import argparse
import tempfile
import requests
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API configuration
API_BASE_URL = "http://localhost:8000"
API_UPLOAD_ENDPOINT = "/api/v1/files/upload"
API_VM_CREATE_ENDPOINT = "/api/v1/vagrant/create"
API_VM_INJECT_ENDPOINT = "/api/v1/vagrant/inject"
API_VM_STATUS_ENDPOINT = "/api/v1/vagrant/status"
API_VM_DESTROY_ENDPOINT = "/api/v1/vagrant/destroy"

class VME2ETest:
    """End-to-end test for VM file injection."""

    def __init__(self, api_base_url: str = API_BASE_URL):
        """Initialize the test."""
        self.api_base_url = api_base_url
        self.file_id = None
        self.vm_id = None
        self.temp_file = None

    def create_test_file(self, content: str = "This is a test file for VM injection.") -> str:
        """
        Create a test file.
        
        Args:
            content: The content of the test file.
            
        Returns:
            The path to the test file.
        """
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".txt")
        self.temp_file.write(content.encode())
        self.temp_file.flush()
        logger.info(f"Created test file at {self.temp_file.name}")
        return self.temp_file.name

    def upload_file(self, file_path: str) -> Dict[str, Any]:
        """
        Upload a file to the API.
        
        Args:
            file_path: The path to the file to upload.
            
        Returns:
            The response from the API.
        """
        logger.info(f"Uploading file {file_path} to API")
        with open(file_path, "rb") as f:
            files = {"file": (os.path.basename(file_path), f)}
            response = requests.post(
                f"{self.api_base_url}{API_UPLOAD_ENDPOINT}",
                files=files
            )
        
        if response.status_code != 200:
            logger.error(f"Failed to upload file: {response.text}")
            return {"success": False, "error": response.text}
        
        result = response.json()
        self.file_id = result.get("file_id")
        logger.info(f"File uploaded successfully with ID {self.file_id}")
        return result

    def create_vm(self, vm_name: str = "test_vm") -> Dict[str, Any]:
        """
        Create a VM.
        
        Args:
            vm_name: The name of the VM.
            
        Returns:
            The response from the API.
        """
        logger.info(f"Creating VM {vm_name}")
        response = requests.post(
            f"{self.api_base_url}{API_VM_CREATE_ENDPOINT}",
            json={"name": vm_name}
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to create VM: {response.text}")
            return {"success": False, "error": response.text}
        
        result = response.json()
        self.vm_id = result.get("vm_id")
        logger.info(f"VM created successfully with ID {self.vm_id}")
        return result

    def wait_for_vm(self, timeout: int = 300) -> Dict[str, Any]:
        """
        Wait for the VM to be ready.
        
        Args:
            timeout: The timeout in seconds.
            
        Returns:
            The response from the API.
        """
        logger.info(f"Waiting for VM {self.vm_id} to be ready")
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = requests.get(
                f"{self.api_base_url}{API_VM_STATUS_ENDPOINT}/{self.vm_id}"
            )
            
            if response.status_code != 200:
                logger.error(f"Failed to get VM status: {response.text}")
                return {"success": False, "error": response.text}
            
            result = response.json()
            status = result.get("status")
            
            if status == "running":
                logger.info(f"VM {self.vm_id} is ready")
                return result
            
            logger.info(f"VM status: {status}, waiting...")
            time.sleep(10)
        
        logger.error(f"Timeout waiting for VM {self.vm_id} to be ready")
        return {"success": False, "error": "Timeout waiting for VM to be ready"}

    def inject_file(self, source_file_id: str, target_path: str) -> Dict[str, Any]:
        """
        Inject a file into the VM.
        
        Args:
            source_file_id: The ID of the file to inject.
            target_path: The target path in the VM.
            
        Returns:
            The response from the API.
        """
        logger.info(f"Injecting file {source_file_id} into VM {self.vm_id} at {target_path}")
        response = requests.post(
            f"{self.api_base_url}{API_VM_INJECT_ENDPOINT}",
            json={
                "vm_id": self.vm_id,
                "file_id": source_file_id,
                "target_path": target_path
            }
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to inject file: {response.text}")
            return {"success": False, "error": response.text}
        
        result = response.json()
        logger.info(f"File injected successfully: {result}")
        return result

    def verify_file_injection(self, target_path: str) -> Dict[str, Any]:
        """
        Verify that the file was injected correctly.
        
        Args:
            target_path: The target path in the VM.
            
        Returns:
            The response from the API.
        """
        logger.info(f"Verifying file injection at {target_path}")
        # Use the VM status endpoint to execute a command to check the file
        response = requests.post(
            f"{self.api_base_url}{API_VM_STATUS_ENDPOINT}/{self.vm_id}/execute",
            json={"command": f"cat {target_path}"}
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to verify file injection: {response.text}")
            return {"success": False, "error": response.text}
        
        result = response.json()
        logger.info(f"File verification result: {result}")
        return result

    def destroy_vm(self) -> Dict[str, Any]:
        """
        Destroy the VM.
        
        Returns:
            The response from the API.
        """
        if not self.vm_id:
            logger.warning("No VM to destroy")
            return {"success": True, "message": "No VM to destroy"}
        
        logger.info(f"Destroying VM {self.vm_id}")
        response = requests.delete(
            f"{self.api_base_url}{API_VM_DESTROY_ENDPOINT}/{self.vm_id}"
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to destroy VM: {response.text}")
            return {"success": False, "error": response.text}
        
        result = response.json()
        logger.info(f"VM destroyed successfully: {result}")
        self.vm_id = None
        return result

    def cleanup(self) -> None:
        """Clean up resources."""
        if self.temp_file and os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
            logger.info(f"Deleted temporary file {self.temp_file.name}")
        
        if self.vm_id:
            self.destroy_vm()

    def run_test(self) -> Dict[str, Any]:
        """
        Run the end-to-end test.
        
        Returns:
            The test result.
        """
        try:
            # Create a test file
            test_file_path = self.create_test_file()
            
            # Upload the file
            upload_result = self.upload_file(test_file_path)
            if not upload_result.get("success", False):
                return {"success": False, "stage": "upload", "error": upload_result.get("error")}
            
            # Create a VM
            create_result = self.create_vm()
            if not create_result.get("success", False):
                return {"success": False, "stage": "create_vm", "error": create_result.get("error")}
            
            # Wait for the VM to be ready
            wait_result = self.wait_for_vm()
            if not wait_result.get("success", False):
                return {"success": False, "stage": "wait_for_vm", "error": wait_result.get("error")}
            
            # Inject the file
            target_path = "/tmp/injection_test/test_file.txt"
            inject_result = self.inject_file(self.file_id, target_path)
            if not inject_result.get("success", False):
                return {"success": False, "stage": "inject_file", "error": inject_result.get("error")}
            
            # Verify the file injection
            verify_result = self.verify_file_injection(target_path)
            if not verify_result.get("success", False):
                return {"success": False, "stage": "verify_file", "error": verify_result.get("error")}
            
            # Destroy the VM
            destroy_result = self.destroy_vm()
            if not destroy_result.get("success", False):
                return {"success": False, "stage": "destroy_vm", "error": destroy_result.get("error")}
            
            return {"success": True, "message": "End-to-end test completed successfully"}
        except Exception as e:
            logger.exception("Error in end-to-end test")
            return {"success": False, "error": str(e)}
        finally:
            self.cleanup()

def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        The parsed arguments.
    """
    parser = argparse.ArgumentParser(description='End-to-end test for VM file injection.')
    parser.add_argument('--api-url', type=str, default=API_BASE_URL,
                        help='The base URL of the API.')
    
    return parser.parse_args()

def main() -> None:
    """Main function."""
    # Parse command line arguments
    args = parse_args()
    
    # Run the test
    test = VME2ETest(api_base_url=args.api_url)
    result = test.run_test()
    
    # Print the result
    print(json.dumps(result, indent=2))
    
    # Exit with the appropriate status code
    sys.exit(0 if result.get("success", False) else 1)

if __name__ == '__main__':
    main()
