# ARCHIVED FILE
# Original Location: /debug_file_validation.py
# Archived Date: 2025-06-03 23:56:18
# Reason: Root directory cleanup - temporary/debug file
# File Size: 1477 bytes
# Last Modified: 2025-05-14 17:16:51.775966765 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 4 import statements
# References: 3 potential references found in codebase
# 
# [Original file content follows...]
# 


import asyncio
import magic
from fastapi import UploadFile
from api.services.file_validation import FileValidationService

async def main():
    # Create a mock UploadFile
    class MockUploadFile:
        def __init__(self, filename):
            self.filename = filename
            self.content_type = 'text/x-python'
            
        async def read(self):
            with open('/app/service_monitor.py', 'rb') as f:
                return f.read()

    mock_file = MockUploadFile(filename='service_monitor.py')

    try:
        # Read file content
        content = await mock_file.read()
        
        # Detect MIME type
        mime = magic.Magic(mime=True)
        detected_mime_type = mime.from_buffer(content)
        print(f'Detected MIME type: {detected_mime_type}')
        
        # Check if MIME type is allowed
        print(f'MIME type in allowed types: {detected_mime_type in FileValidationService.ALLOWED_MIME_TYPES}')
        print(f'Allowed MIME types: {FileValidationService.ALLOWED_MIME_TYPES}')
        
        # Validate file
        try:
            sanitized_filename, sanitized_content, actual_mime_type = await FileValidationService.validate_file(mock_file)
            print(f'Validation successful: {sanitized_filename}, {actual_mime_type}')
        except Exception as e:
            print(f'Validation failed: {str(e)}')
    except Exception as e:
        print(f'Error: {str(e)}')

if __name__ == '__main__':
    asyncio.run(main())
