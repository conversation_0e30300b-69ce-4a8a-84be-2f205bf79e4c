# ARCHIVED FILE
# Original Location: /test_uploader.py
# Archived Date: 2025-06-03 23:56:14
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 1759 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 4 import statements
# References: 4 potential references found in codebase
# 
# [Original file content follows...]
# 


#!/usr/bin/env python3
"""
Simple file uploader for testing the TurdParty API.
"""

import sys
import os
import requests
import argparse

def upload_file(file_path, api_url, description="Test upload"):
    """Upload a file to the TurdParty API."""
    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        return False
    
    print(f"Uploading file: {file_path}")
    print(f"API URL: {api_url}")
    print(f"Description: {description}")
    
    # Open the file in binary mode
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {'description': description}
        
        # Make the request
        try:
            response = requests.post(api_url, files=files, data=data)
            print(f"Status code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 201 or response.status_code == 200:
                print("Upload successful!")
                return True
            else:
                print("Upload failed.")
                return False
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Upload a file to the TurdParty API.")
    parser.add_argument("file", help="Path to the file to upload")
    parser.add_argument("--url", default="http://localhost:3050/api/v1/file_upload/", help="API URL")
    parser.add_argument("--description", default="Test upload", help="File description")
    
    args = parser.parse_args()
    
    success = upload_file(args.file, args.url, args.description)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
