# ARCHIVED FILE
# Original Location: /test_vm_injection.py
# Archived Date: 2025-06-03 23:56:14
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 3347 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, ASCII text executable
# Dependencies: Python imports found: 5 import statements
# References: 5 potential references found in codebase
# 
# [Original file content follows...]
# 


#!/usr/bin/env python3

"""
Test script for VM injection service.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_vm_injection():
    """Test the VM injection service."""
    try:
        # Import the VagrantClient
        from api.services.vagrant_client import VagrantClient

        # Create a VagrantClient instance
        logger.info("Creating VagrantClient instance")
        vagrant_client = VagrantClient()

        # Try gRPC mode first, fall back to SSH if it fails
        vagrant_client.use_ssh = False

        # Connect to the service
        logger.info("Connecting to Vagrant service using gRPC mode")
        connected = await vagrant_client.connect()

        if not connected:
            logger.warning("Failed to connect using gRPC mode, falling back to SSH mode")
            vagrant_client.use_ssh = True
            connected = await vagrant_client.connect()

            if not connected:
                logger.error("Failed to connect using both gRPC and SSH modes")
                return

        # Get the connection mode
        mode = "SSH" if vagrant_client.use_ssh else "gRPC"
        logger.info(f"Successfully connected to Vagrant service using {mode} mode")

        # Use localhost as the VM ID for testing
        vm_id = "localhost"
        logger.info(f"Using test VM ID: {vm_id}")

        # Get the VM status
        logger.info(f"Getting status for VM {vm_id}")
        status = await vagrant_client.status(vm_id)
        logger.info(f"VM status: {status}")

        # Check if the VM is running
        if status.get("state") != "running":
            logger.info(f"VM {vm_id} is not running")
        else:
            logger.info(f"VM {vm_id} is running")

        # Create a test file
        logger.info("Creating test file")
        test_file_path = "test-file.txt"
        with open(test_file_path, "w") as f:
            f.write("This is a test file for VM injection")

        # Inject the file into the VM using execute_command
        logger.info(f"Injecting file {test_file_path} into VM {vm_id}")
        target_path = "/tmp/test-file.txt"
        copy_command = f"cp {test_file_path} {target_path} && chmod 0755 {target_path}"
        result = await vagrant_client.execute_command(vm_id, copy_command, sudo=True)
        logger.info(f"File injection result: {result}")

        # Execute a command in the VM to verify the file was injected
        logger.info(f"Executing command in VM {vm_id} to verify file injection")
        command = f"cat {target_path}"
        output = await vagrant_client.execute_command(vm_id, command)
        logger.info(f"Command output: {output}")

        # Clean up
        logger.info("Cleaning up")
        os.remove(test_file_path)

        # Close the connection
        logger.info("Closing connection")
        await vagrant_client.close()

        logger.info("Test completed successfully")
    except Exception as e:
        logger.exception(f"Error in test_vm_injection: {e}")

if __name__ == "__main__":
    asyncio.run(test_vm_injection())
