// ARCHIVED FILE
// Original Location: /simple-test.spec.js
// Archived Date: 2025-06-03 23:56:15
// Reason: Root directory cleanup - moved to /tests directory
// File Size: 1052 bytes
// Last Modified: 2025-05-14 17:16:51.779966734 +0200
// File Type: JavaScript source, ASCII text
// Dependencies: JavaScript imports found: 1 import statements
// References: 7 potential references found in codebase
// 
// [Original file content follows...]
// 


import { test, expect } from '@playwright/test';

test('upload file test', async ({ page }) => {
  console.log('Starting test');
  
  // Go to upload page
  await page.goto('http://localhost:3100/upload');
  console.log('On upload page');
  await page.screenshot({ path: 'test_screenshots/01-upload-page.png' });
  
  // Fill in form
  await page.getByPlaceholder(/description/i).fill('Test upload from Playwright Docker');
  
  // Attach file
  await page.setInputFiles('input[type="file"]', 'test-upload.txt');
  await page.screenshot({ path: 'test_screenshots/02-file-selected.png' });
  
  // Submit form
  await page.getByRole('button', { name: /upload/i }).click();
  
  // Wait for success
  await page.waitForSelector('.ant-message-success, .upload-success', { timeout: 30000 });
  await page.screenshot({ path: 'test_screenshots/03-success.png' });
  
  // Navigate to files page
  await page.goto('http://localhost:3100/files');
  await page.screenshot({ path: 'test_screenshots/04-files-list.png' });
  
  console.log('Test completed');
});
