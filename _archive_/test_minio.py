# ARCHIVED FILE
# Original Location: /test_minio.py
# Archived Date: 2025-06-03 23:56:13
# Reason: Root directory cleanup - moved to /tests directory
# File Size: 2720 bytes
# Last Modified: 2025-05-14 17:16:51.781966718 +0200
# File Type: Python script, Unicode text, UTF-8 text executable
# Dependencies: Python imports found: 5 import statements
# References: 125 potential references found in codebase
# 
# [Original file content follows...]
# 


import os
import sys
import asyncio
import logging
from urllib3.exceptions import MaxRetryError

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set environment variables to point to host machine
os.environ["MINIO_HOST"] = "host.docker.internal"
os.environ["MINIO_PORT"] = "9000"
os.environ["MINIO_ACCESS_KEY"] = "minioadmin"
os.environ["MINIO_SECRET_KEY"] = "minioadmin"
os.environ["MINIO_DIRECT"] = "true"

async def test_minio_connection():
    """Test connecting to MinIO on the host machine from the API container."""
    try:
        from minio import Minio
        
        logger.info(f"Testing MinIO connection to {os.environ['MINIO_HOST']}:{os.environ['MINIO_PORT']}")
        client = Minio(
            f"{os.environ['MINIO_HOST']}:{os.environ['MINIO_PORT']}", 
            access_key=os.environ["MINIO_ACCESS_KEY"],
            secret_key=os.environ["MINIO_SECRET_KEY"],
            secure=False
        )
        
        logger.info("Attempting to list buckets...")
        buckets = client.list_buckets()
        
        logger.info(f"Success! Found {len(buckets)} buckets:")
        for bucket in buckets:
            logger.info(f"  - {bucket.name}")
        
        return True
        
    except MaxRetryError as e:
        logger.error(f"Connection error: {e}")
        # Try alternate host
        alternate_host = "**********"  # Common Docker bridge network gateway
        logger.info(f"Trying alternate host: {alternate_host}")
        
        try:
            os.environ["MINIO_HOST"] = alternate_host
            client = Minio(
                f"{alternate_host}:{os.environ['MINIO_PORT']}", 
                access_key=os.environ["MINIO_ACCESS_KEY"],
                secret_key=os.environ["MINIO_SECRET_KEY"],
                secure=False
            )
            
            buckets = client.list_buckets()
            logger.info(f"Success with alternate host! Found {len(buckets)} buckets.")
            logger.info(f"You should update your configuration to use {alternate_host} as MINIO_HOST")
            return True
            
        except Exception as inner_e:
            logger.error(f"Failed with alternate host: {inner_e}")
            return False
        
    except Exception as e:
        logger.error(f"Failed to connect to MinIO: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting MinIO connection test")
    result = asyncio.run(test_minio_connection())
    
    if result:
        logger.info("✅ MinIO connection test PASSED")
        sys.exit(0)
    else:
        logger.error("❌ MinIO connection test FAILED")
        sys.exit(1) 