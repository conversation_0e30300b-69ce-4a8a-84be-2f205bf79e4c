# ARCHIVED FILE
# Original Location: /debug_openapi.py
# Archived Date: 2025-06-03 23:56:18
# Reason: Root directory cleanup - temporary/debug file
# File Size: 2152 bytes
# Last Modified: 2025-05-15 11:32:58.000000000 +0200
# File Type: Python script, Unicode text, UTF-8 text executable
# Dependencies: Python imports found: 4 import statements
# References: 3 potential references found in codebase
# 
# [Original file content follows...]
# 


#!/usr/bin/env python3
"""
Debug script to identify issues with OpenAPI schema generation.
"""
import sys
import traceback
import json
from fastapi.openapi.utils import get_openapi

def debug_openapi():
    """Debug OpenAPI schema generation."""
    try:
        # Import the FastAPI app
        from main import app
        
        print("Generating OpenAPI schema...")
        
        # Generate the OpenAPI schema
        schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )
        
        print("Schema generated successfully!")
        
        # Save the schema to a file
        with open("openapi.json", "w") as f:
            json.dump(schema, f, indent=2)
            
        print("Schema saved to openapi.json")
        
    except Exception as e:
        print(f"Error: {type(e).__name__}: {str(e)}")
        traceback.print_exc()
        
        # Try to identify the problematic route
        try:
            from main import app
            
            print("\nAttempting to identify problematic routes...")
            
            # Test each route individually
            for route in app.routes:
                try:
                    if hasattr(route, "path"):
                        print(f"Testing route: {route.path}")
                        
                        # Try to generate schema for just this route
                        mini_schema = get_openapi(
                            title="Test",
                            version="0.1.0",
                            description="Test",
                            routes=[route],
                        )
                        print(f"  ✓ Route {route.path} is OK")
                except Exception as route_error:
                    print(f"  ✗ Error in route {route.path}: {type(route_error).__name__}: {str(route_error)}")
                    
        except Exception as debug_error:
            print(f"Error during debugging: {type(debug_error).__name__}: {str(debug_error)}")

if __name__ == "__main__":
    debug_openapi()
