# TurdParty Cleanup Log

This file tracks all cleanup actions performed during the folder structure reorganization.

**Cleanup Started:** 2025-06-03 23:44:07  
**Project:** TurdParty  
**Cleanup Goal:** Organize folder structure and reduce root directory clutter  

## Summary Statistics

- **Total Files Archived:** 0
- **Root Directory Files Removed:** 0
- **Test Files Consolidated:** 0
- **Configuration Files Merged:** 0

## Archive Actions

| Date | File | Original Location | Reason | Notes |
|------|------|-------------------|--------|-------|

## Restoration Actions

| Date | File | Restored To | Reason | Notes |
|------|------|-------------|--------|-------|

## Notes

- All archived files include metadata headers with original location and archival reason
- Metadata JSON files are stored in the metadata/ directory
- Use scripts/restore-from-archive.sh to restore files if needed

---
*Log initialized on: 2025-06-03 23:44:07*
| 2025-06-03 | test_vagrant_vm_integration.py | /api/tests/test_vagrant_vm_integration.py | Duplicate test file - functionality exists in api/tests/test_integration/test_vagrant_vm_integration.py |  |
| 2025-06-03 | test-api-upload.js | /test-api-upload.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test-e2e-upload-container.js | /test-e2e-upload-container.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test-e2e-upload.js | /test-e2e-upload.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test-file-upload-ui.js | /test-file-upload-ui.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test-vm-injection-playwright.js | /test-vm-injection-playwright.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_api_coverage.py | /test_api_coverage.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_item_model.py | /test_item_model.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_minio_fixed.py | /test_minio_fixed.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_minio.py | /test_minio.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_redirect.py | /test_redirect.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_routes.py | /test_routes.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_uploader.py | /test_uploader.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_upload.py | /test_upload.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_vm_e2e.py | /test_vm_e2e.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_vm_injection.py | /test_vm_injection.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test_vm_injection_service.py | /test_vm_injection_service.py | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | simple-test.spec.js | /simple-test.spec.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | test.spec.js | /test.spec.js | Root directory cleanup - moved to /tests directory |  |
| 2025-06-03 | app_response.html | /app_response.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | cache_response.html | /cache_response.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | db_response.html | /db_response.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | error_page.html | /error_page.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | file_upload_page_after.html | /file_upload_page_after.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | file_upload_page.html | /file_upload_page.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | mail_response.html | /mail_response.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | setup_response.html | /setup_response.html | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | file-upload-page.png | /file-upload-page.png | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | file_upload.png | /file_upload.png | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | generated-icon.png | /generated-icon.png | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | homepage.png | /homepage.png | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | ui_test_screenshot.png | /ui_test_screenshot.png | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | vm_status.png | /vm_status.png | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | debug_file_upload.py | /debug_file_upload.py | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | debug_file_validation.py | /debug_file_validation.py | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | debug_openapi.py | /debug_openapi.py | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | temp_vm_test.py | /temp_vm_test.py | Root directory cleanup - temporary/debug file |  |
| 2025-06-03 | alembic.ini | /./alembic.ini | Root directory cleanup - duplicate configuration |  |
| 2025-06-03 | docker-compose.diagnostic.yml | /./docker-compose.diagnostic.yml | Root directory cleanup - duplicate configuration |  |
| 2025-06-03 | docker-compose.test.yml | /./docker-compose.test.yml | Root directory cleanup - duplicate configuration |  |
| 2025-06-03 | docker-compose.testing.yml | /./docker-compose.testing.yml | Root directory cleanup - duplicate configuration |  |
