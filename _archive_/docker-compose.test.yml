# ARCHIVED FILE
# Original Location: /./docker-compose.test.yml
# Archived Date: 2025-06-03 23:56:19
# Reason: Root directory cleanup - duplicate configuration
# File Size: 910 bytes
# Last Modified: 2025-05-14 17:16:51.775966765 +0200
# File Type: ASCII text
# Dependencies: None identified
# References: 21 potential references found in codebase
# 
# [Original file content follows...]
# 


version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=test
      - DATABASE_URL=********************************************/turdparty_test
    volumes:
      - ./:/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty_test
    volumes:
      - postgres_data:/var/lib/postgresql/data

  test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - NODE_ENV=test
      - DATABASE_URL=********************************************/turdparty_test
    volumes:
      - ./:/app
      - ./test-results:/app/test-results
      - ./test_logs:/app/test_logs
    depends_on:
      - api
      - postgres

volumes:
  postgres_data: 